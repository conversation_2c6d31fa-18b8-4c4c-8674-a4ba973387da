#!/usr/bin/env python3
"""
Test simple UI với hardware detection
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

from hardware_detector import hardware_detector

class SimpleMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('Test Simple UI v5.0.12')
        self.setFixedSize(600, 400)
        
        # Setup UI
        self.setup_ui()
        
        # Cập nhật thông tin phần cứng
        QTimer.singleShot(100, self.update_hardware_info)
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("Test Hardware Detection UI")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("", 16, QFont.Bold))
        main_layout.addWidget(title)
        
        # Status frame
        status_frame = QFrame()
        status_frame.setFrameShape(QFrame.StyledPanel)
        status_layout = QVBoxLayout(status_frame)
        
        # Dòng 1: Thời gian
        top_status_layout = QHBoxLayout()
        self.time_label = QLabel("⏱ Thời gian: 00:00:00")
        top_status_layout.addStretch()
        top_status_layout.addWidget(self.time_label)
        top_status_layout.addStretch()
        
        # Dòng 2: Model và phần cứng
        bottom_status_layout = QHBoxLayout()
        
        self.model_status_label = QLabel("✅ Model sẵn sàng")
        self.model_status_label.setStyleSheet("color: green; font-size: 11px;")
        
        self.hardware_status_label = QLabel("🔍 Đang detect phần cứng...")
        self.hardware_status_label.setStyleSheet("color: blue; font-weight: bold; font-size: 11px;")
        
        bottom_status_layout.addWidget(self.model_status_label)
        bottom_status_layout.addStretch()
        bottom_status_layout.addWidget(self.hardware_status_label)
        
        status_layout.addLayout(top_status_layout)
        status_layout.addLayout(bottom_status_layout)
        main_layout.addWidget(status_frame)
        
        # Test button
        test_button = QPushButton("Test Hardware Detection")
        test_button.clicked.connect(self.test_hardware_detection)
        main_layout.addWidget(test_button)
        
        # Result area
        self.result_label = QLabel("Kết quả sẽ hiển thị ở đây...")
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("border: 1px solid gray; padding: 10px; margin: 10px;")
        main_layout.addWidget(self.result_label)
    
    def update_hardware_info(self):
        """Cập nhật thông tin phần cứng"""
        try:
            device_display_name = hardware_detector.get_device_display_name()
            self.hardware_status_label.setText(device_display_name)
            self.hardware_status_label.setStyleSheet("color: green; font-weight: bold; font-size: 11px;")
            
            # Cập nhật result area
            device_type, device_name, device_info = hardware_detector.determine_best_device()
            result_text = f"Device Type: {device_type}\n"
            result_text += f"Device Name: {device_name}\n"
            result_text += f"Display Name: {device_display_name}\n"
            
            if device_type == 'cuda' and 'memory' in device_info:
                result_text += f"VRAM: {device_info['memory']}\n"
                if 'compute_capability' in device_info:
                    result_text += f"Compute: {device_info['compute_capability']}\n"
            elif device_type == 'cpu':
                if 'cores' in device_info:
                    result_text += f"CPU Cores: {device_info['cores']}\n"
                if 'intel_gpu' in device_info:
                    result_text += f"Intel GPU: {device_info['intel_gpu']}\n"
            
            self.result_label.setText(result_text)
            
        except Exception as e:
            self.hardware_status_label.setText("❌ Lỗi detect phần cứng")
            self.hardware_status_label.setStyleSheet("color: red; font-weight: bold; font-size: 11px;")
            self.result_label.setText(f"Lỗi: {e}")
    
    def test_hardware_detection(self):
        """Test lại hardware detection"""
        self.hardware_status_label.setText("🔍 Đang detect lại...")
        self.hardware_status_label.setStyleSheet("color: blue; font-weight: bold; font-size: 11px;")
        
        # Clear cache và detect lại
        hardware_detector.clear_cache()
        QTimer.singleShot(100, self.update_hardware_info)

def main():
    app = QApplication(sys.argv)
    
    # Tăng cỡ chữ
    font = app.font()
    font.setPointSize(font.pointSize() + 1)
    app.setFont(font)
    
    window = SimpleMainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
