@echo off
echo ========================================
echo Building Kiem Tra MST Qt C# Version
echo Target Framework: .NET 9.0
echo GUI Framework: Qt via Python.NET
echo ========================================

echo.
echo 🔍 Checking requirements...

echo.
echo 1. Checking .NET 9.0...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET runtime not found!
    echo Please install .NET 9.0 from:
    echo https://dotnet.microsoft.com/download/dotnet/9.0
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ .NET version: %DOTNET_VERSION%

echo.
echo 2. Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found!
    echo Please install Python 3.8+ from:
    echo https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python version: %PYTHON_VERSION%

echo.
echo 3. Checking PySide6...
python -c "import PySide6; print('PySide6 version:', PySide6.__version__)" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PySide6 not found!
    echo Installing PySide6...
    pip install PySide6
    if %errorlevel% neq 0 (
        echo ❌ Failed to install PySide6
        pause
        exit /b 1
    )
)
echo ✅ PySide6 available

echo.
echo ========================================
echo Building Qt Version
echo ========================================

echo.
echo 🧹 Cleaning previous builds...
dotnet clean --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ Clean failed
    pause
    exit /b 1
)
echo ✅ Clean completed

echo.
echo 📦 Restoring packages...
dotnet restore KiemTraMST_Qt.csproj --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ Package restore failed
    pause
    exit /b 1
)
echo ✅ Packages restored

echo.
echo 🔨 Building Qt version...
dotnet build KiemTraMST_Qt.csproj --configuration Release --verbosity minimal
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
echo 📊 Build Summary:
echo    Project: KiemTraMST_Qt.csproj
echo    Framework: .NET 9.0
echo    GUI: Qt via PySide6
echo    Configuration: Release
echo    Output: bin\Release\net9.0-windows\
echo.

echo 🚀 Ready to run Qt version!
echo Use: dotnet run --project KiemTraMST_Qt.csproj --configuration Release
echo.
pause
