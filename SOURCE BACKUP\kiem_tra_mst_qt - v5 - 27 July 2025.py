
# Để chạy file này, bạn cần cài đặt PySide6:
# pip install PySide6
#
# File này là phiên bản đư<PERSON><PERSON> viết lại của kiem_tra_mst.py, sử dụng bộ công cụ Qt (thông qua PySide6)
# để có giao diện hiện đại hơn và hỗ trợ tốt hơn cho emoji màu.

import sys, os, datetime, threading, re, json, configparser, traceback, concurrent.futures, time, requests
from typing import Optional, List, Dict, Any
import random
from PIL import Image, ImageTk, ImageDraw, ImageFont

# --- Thêm import cho PySide6 ---
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QTextEdit, QLineEdit, QPushButton, QProgressBar,
    QMessageBox, QDialog, QFileDialog, Q<PERSON>heckBox, QFrame
)
from PySide6.QtCore import (
    Qt, QRunnable, QThreadPool, QObject, Signal, QTimer, QSize, Slot
)
from PySide6.QtGui import QFont, QMovie, QPixmap, QTransform, QIcon

# ==============================================================================
# PHẦN CẤU HÌNH VÀ LOGIC BACKEND (GIỮ NGUYÊN TỪ BẢN GỐC)
# ==============================================================================

cfg = configparser.ConfigParser()
cfg.read("setting.ini", encoding="utf-8")
default_workers = cfg.getint("DEFAULT", "max_workers", fallback=20)
default_retry_failed = cfg.getint("DEFAULT", "retry_failed", fallback=20)
default_timeout = cfg.getint("DEFAULT", "request_timeout", fallback=120)
default_ocr_timeout = cfg.getint("DEFAULT", "ocr_timeout", fallback=120)

# ======= BẮT ĐẦU PHẦN GỘP TỪ trocr_solver.py =======
# Đường dẫn thư mục chứa model đã tải sẵn
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
MODEL_DIR = os.path.join(BASE_DIR, "CaptchaData")

_model = None
_processor = None
_device = None
_model_lock = threading.Lock()
_model_loaded = False

def load_model(progress_callback=None):
    """
    Trì hoãn import mô hình để tránh cảnh báo CUDA hiện sớm.
    Có thể gọi nhiều lần, chỉ load 1 lần (thread-safe).
    """
    global _model, _processor, _device, _model_loaded
    if _model_loaded:
        if progress_callback: progress_callback(100)
        return
    with _model_lock:
        if _model_loaded:
            if progress_callback: progress_callback(100)
            return
        if progress_callback: progress_callback(5)
        import torch
        from transformers import VisionEncoderDecoderModel, TrOCRProcessor
        _device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        if progress_callback: progress_callback(15)
        _processor = TrOCRProcessor.from_pretrained(MODEL_DIR)
        if progress_callback: progress_callback(60)
        _model = VisionEncoderDecoderModel.from_pretrained(MODEL_DIR)
        if progress_callback: progress_callback(90)
        _model.to(_device)
        _model_loaded = True
        if progress_callback: progress_callback(100)

def preprocess_image(image_path_or_buffer):
    image = Image.open(image_path_or_buffer).convert("RGBA")
    background = Image.new("RGBA", image.size, (255, 255, 255, 255))
    combined = Image.alpha_composite(background, image).convert("RGB")
    return combined

def solve_captcha(image_path_or_buffer):
    global _model, _processor, _device, _model_loaded
    if not _model_loaded or _model is None or _processor is None:
        load_model()
    try:
        import torch
        img = preprocess_image(image_path_or_buffer)
        encoding = _processor(images=img, return_tensors="pt").to(_device)
        generated_ids = _model.generate(encoding.pixel_values, max_length=20)
        text = _processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
        return text.strip()
    except Exception as e:
        return f"❌ Lỗi OCR: {e}"
# ======= KẾT THÚC PHẦN GỘP TỪ trocr_solver.py =======


# ==============================================================================
# PHẦN GIAO DIỆN (UI) VÀ LOGIC TƯƠNG TÁC VỚI QT
# ==============================================================================

class WorkerSignals(QObject):
    """
    Định nghĩa các tín hiệu (signals) có thể được phát ra từ một worker thread.
    Bao gồm các tín hiệu cho việc log, cập nhật dòng kết quả, báo cáo kết quả captcha,
    và báo hiệu khi worker hoàn thành.
    """
    log = Signal(str)
    update_result = Signal(int, str)
    captcha_result = Signal(bool) # True for OK, False for Fail
    task_completed = Signal(bool) # True nếu vòng lặp retry thành công, False nếu thất bại
    finished = Signal()
    error = Signal(str)

class TraCuuWorker(QRunnable):
    """
    Worker class để tra cứu MST trong một thread riêng biệt sử dụng QThreadPool.
    """
    def __init__(self, mst: str, idx: int, debug_flags: dict):
        super().__init__()
        self.mst = mst
        self.idx = idx
        self.signals = WorkerSignals()
        self.session = None
        self.debug_flags = debug_flags

    @Slot()
    def run(self):
        self.session = requests.Session()
        self.signals.log.emit(f"[{self.mst}] 🚀 Bắt đầu xử lý")
        self.signals.update_result.emit(self.idx, f"Đang kiểm tra...\t{self.mst}")

        try:
            attempt = 0
            while attempt < default_retry_failed:
                try:
                    self.signals.log.emit(f"[{self.mst}] 🧩 Đang lấy captcha")
                    dia_chi = self.tra_cuu()
                    self.signals.log.emit(f"[{self.mst}] ✅ Đã lấy được địa chỉ")

                    if dia_chi.lower().startswith("không lấy được"):
                        raise Exception("Không lấy được địa chỉ")

                    if not dia_chi.strip():
                        dia_chi = "(Rỗng)"

                    result_text = f"{dia_chi}\t{self.mst}"
                    self.signals.update_result.emit(self.idx, result_text)
                    self.signals.task_completed.emit(True) # Báo hiệu thành công
                    break
                except Exception as e:
                    attempt += 1
                    error_msg = str(e)
                    self.signals.log.emit(f"[{self.mst}] ❌ Lỗi lần {attempt}: {error_msg}")

                    if attempt >= default_retry_failed:
                        fail_text = f"không lấy được địa chỉ\t{self.mst}"
                        self.signals.update_result.emit(self.idx, fail_text)
                        self.signals.task_completed.emit(False) # Báo hiệu thất bại

        except Exception as e:
            self.signals.error.emit(f"Lỗi nghiêm trọng worker [{self.mst}]: {e}\n{traceback.format_exc()}")
        finally:
            self.session.close()
            self.signals.finished.emit()


    def tra_cuu(self) -> str:
        if self.debug_flags.get('always_fail'):
            return "không lấy được địa chỉ (debug)"
        headers = {
            'User-Agent': 'Mozilla/5.0',
            'Referer': 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp'
        }

        try:
            self.session.get("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp", headers=headers, timeout=default_timeout)
        except requests.RequestException as e:
            raise Exception(f"Lỗi kết nối: {e}")

        captcha_url = "https://tracuunnt.gdt.gov.vn/tcnnt/captcha.png"
        try:
            r = self.session.get(captcha_url, headers=headers, timeout=default_timeout)
            r.raise_for_status()
            import io
            image_buffer = io.BytesIO(r.content)
        except requests.RequestException as e:
            raise Exception(f"Lỗi tải captcha: {e}")

        if self.debug_flags.get('wrong_captcha'):
            code = ''.join(random.choices('ABCDEFGHJKLMNPQRSTUVWXYZ23456789', k=5))
        else:
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(solve_captcha, image_buffer)
                    code = future.result(timeout=default_ocr_timeout)
                    if not code or "❌" in code:
                        raise Exception("Lỗi OCR captcha")
            except concurrent.futures.TimeoutError:
                raise Exception(f"OCR captcha timeout sau {default_ocr_timeout} giây")
            except Exception as e:
                raise Exception(f"Lỗi giải captcha: {e}")

        data = {"mst": self.mst, "fullname": "", "address": "", "captcha": code}
        self.signals.log.emit(f"[{self.mst}] 📤 Gửi request tra cứu")

        try:
            res = self.session.post("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp", data=data, headers=headers, timeout=default_timeout)
            res.raise_for_status()
        except requests.RequestException as e:
            raise Exception(f"Lỗi gửi request: {e}")

        html = res.text
        if self.debug_flags.get('save_response'):
            os.makedirs("debug", exist_ok=True)
            ts = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = os.path.join("debug", f"debug_raw_{self.mst}_{ts}.txt")
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(html)
            self.signals.log.emit(f"[DEBUG] Đã lưu file trả về: {file_path}")
            self.debug_flags['save_response'] = False # Chỉ lưu 1 lần

        if any(x in html.lower() for x in ["vui lòng nhập đúng mã xác nhận", "vui l&#242;ng nh&#7853;p &#273;&#250;ng m&#227; x&#225;c nh&#7853;n"]):
            self.signals.captcha_result.emit(False)
            raise Exception("Captcha sai")

        self.signals.captcha_result.emit(True)

        if "không tìm thấy người nộp thuế" in html.lower():
            return "Không tìm thấy người nộp thuế nào phù hợp."
        if "mã số thuế không hợp lệ" in html.lower():
            return "Mã số thuế không hợp lệ."

        try:
            match = re.search(r"var\s+nntJson\s*=\s*(\{.*?\});", html, re.DOTALL)
            if match:
                json_str = match.group(1)
                data = json.loads(json_str)
                info = next((item for item in data.get("DATA", []) if item.get("MST") == self.mst), None)
                if not info:
                    return "không lấy được địa chỉ (không tìm thấy MST phù hợp)"

                dkt_dia_chi = info.get("DKT_DIA_CHI", [])
                dia_chi_parts = {item.get("LOAI"): item for item in dkt_dia_chi}
                dia_chi_info = dia_chi_parts.get("0300", {})

                dia_chi = dia_chi_info.get("DIA_CHI", "")
                xa = dia_chi_info.get("PHUONG_XA", "")
                tinh = dia_chi_info.get("TINH_TP", "")

                if not dia_chi:
                    dia_chi = info.get("DIA_CHI_TRU_SO_EXT", "").strip()
                full_address = f"{dia_chi}, {xa}, {tinh}".strip(", ")
                return full_address
        except Exception as e:
            return f"không lấy được địa chỉ (lỗi parse: {e})"
        return "không lấy được địa chỉ"

class EmojiAnimationLabel(QLabel):
    """QLabel hiển thị một chuỗi emoji động."""
    def __init__(self, emojis, delay=200, size=32, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.emojis = emojis
        self.idx = 0
        self.setFont(QFont("Segoe UI Emoji", size // 2))
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.animate)
        self.timer.start(delay)
        self.animate()

    def animate(self):
        self.setText(self.emojis[self.idx])
        self.idx = (self.idx + 1) % len(self.emojis)

class FlippingGIFLabel(QLabel):
    """QLabel hiển thị GIF động có thể lật qua lại."""
    def __init__(self, gif_path, flip_interval=2000, size=(32, 32), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.movie = QMovie(gif_path)
        self.movie.setScaledSize(QSize(*size))
        
        # Kết nối signal frameChanged để xử lý mỗi khung hình
        self.movie.frameChanged.connect(self.on_frame_changed)
        
        # Timer để lật ảnh
        self.flipper = QTimer(self)
        self.flipper.timeout.connect(self.toggle_flip)
        self.flipper.start(flip_interval)
        self.is_flipped = False
        
        self.movie.start()

    def toggle_flip(self):
        """Đảo ngược trạng thái lật."""
        self.is_flipped = not self.is_flipped
        # Cập nhật lại khung hình hiện tại với trạng thái lật mới
        self.on_frame_changed()

    @Slot(int)
    def on_frame_changed(self, frame_number=-1):
        """Được gọi mỗi khi QMovie có khung hình mới."""
        pixmap = self.movie.currentPixmap()
        if self.is_flipped:
            # Lật ảnh theo chiều ngang
            pixmap = pixmap.transformed(QTransform().scale(-1, 1))
        
        self.setPixmap(pixmap)

    def destroyEvent(self, event):
        """Dọn dẹp timer và movie khi widget bị hủy."""
        if self.movie:
            self.movie.stop()
        if self.flipper:
            self.flipper.stop()
        super().destroyEvent(event)


class LoadingModelDialog(QDialog):
    """Dialog hiển thị tiến trình tải model TrOCR."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Đang tải model TrOCR...")
        self.setFixedSize(320, 140)
        self.setModal(True)
        self.setWindowFlag(Qt.WindowCloseButtonHint, False)

        layout = QVBoxLayout(self)
        self.emoji = EmojiAnimationLabel(["⏳", "⌛"], delay=300, size=32, parent=self)
        self.emoji.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.emoji)

        self.label = QLabel("Đang tải model TrOCR, vui lòng chờ...", self)
        self.label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label)

        self.progress = QProgressBar(self)
        self.progress.setRange(0, 100)
        layout.addWidget(self.progress)

    @Slot(int)
    def update_progress(self, value):
        self.progress.setValue(value)
        if value == 100:
            self.accept()

    def set_error(self, message):
        self.label.setText(f"Lỗi: {message}")
        self.setWindowFlag(Qt.WindowCloseButtonHint, True) # Cho phép đóng khi lỗi
        self.update()


class MainWindow(QMainWindow):
    MST_COUNT_PREFIX = "📊 Số MST trong bảng: "
    VERSION = "5.0.0"

    def __init__(self):
        super().__init__()
        self._model_ready = False  # Thêm biến trạng thái model
        # Cập nhật version
        self.setWindowTitle(f'Tra cứu thuế v{self.VERSION} (TNT)')
        self.setWindowIcon(QIcon("default.ico"))

        # Khởi tạo các biến trạng thái
        self.setup_state()
        # Khởi tạo UI
        self.setup_ui()
        # Kết nối các signals và slots
        self.setup_connections()
        
        # Tải model trong background
        self.load_model_bg()
        
        # Kiểm tra file setting
        self.check_setting_file()
        self.update_mst_count()

    def setup_state(self):
        self.output_lines_full: Optional[List[str]] = None
        self.update_indices: Optional[List[int]] = None
        self.danh_sach: List[str] = []
        self.ketqua_file: str = ""
        self.start_time: Optional[float] = None
        self.is_paused: bool = False
        self.active_threads = 0
        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        self.total = 0
        self.debug_flags = {'save_response': False, 'wrong_captcha': False, 'always_fail': False}

    def setup_ui(self):
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # 1. Hướng dẫn và Số lượng MST
        top_layout = QHBoxLayout()
        top_layout.addWidget(QLabel("📋 Mỗi dòng 1 MST (không chứa khoảng trắng/tab)"))
        top_layout.addStretch()
        self.mst_count_label = QLabel(f"{self.MST_COUNT_PREFIX}0")
        top_layout.addWidget(self.mst_count_label)
        self.main_layout.addLayout(top_layout)

        # 2. Textbox nhập liệu
        self.text_box = QTextEdit()
        self.text_box.setLineWrapMode(QTextEdit.NoWrap) # Tắt chế độ tự động xuống dòng
        self.text_box.setPlaceholderText("Dán danh sách MST vào đây...")
        self.text_box.setMinimumHeight(150)
        self.main_layout.addWidget(self.text_box)

        # 3. Cài đặt luồng (canh giữa)
        workers_layout = QHBoxLayout()
        workers_layout.addStretch()
        workers_layout.addWidget(QLabel("Số luồng: (có thể thay đổi khi đang chạy)"))
        self.entry_workers = QLineEdit(str(default_workers))
        self.entry_workers.setFixedWidth(50)
        workers_layout.addWidget(self.entry_workers)
        workers_layout.addWidget(QLabel("(thấy máy lag thì hạ xuống)"))
        workers_layout.addStretch()
        self.main_layout.addLayout(workers_layout)

        # 4. Các nút chức năng
        btn_layout = QHBoxLayout()
        self.btn_start = QPushButton("🚀 Bắt đầu tra")
        self.btn_pause = QPushButton("⏸ Tạm dừng")
        self.btn_pause.setEnabled(False)
        self.btn_retry = QPushButton("🔁 Làm lại từ kết quả cũ")
        self.btn_open_result = QPushButton("📄 Mở file kết quả")
        self.btn_open_result.setEnabled(False)
        self.btn_open_folder = QPushButton("📁 Mở thư mục")
        self.btn_debug = QPushButton("🐞 Debug")
        self.btn_exit = QPushButton("❌ Thoát")
        
        btn_layout.addWidget(self.btn_start)
        btn_layout.addWidget(self.btn_pause)
        btn_layout.addWidget(self.btn_retry)
        btn_layout.addWidget(self.btn_open_result)
        btn_layout.addWidget(self.btn_open_folder)
        btn_layout.addWidget(self.btn_debug)
        btn_layout.addWidget(self.btn_exit)
        self.main_layout.addLayout(btn_layout)

        # 5. Khung trạng thái
        status_frame = QFrame()
        status_frame.setFrameShape(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)

        self.kq_frame = QHBoxLayout()
        self.label_kq = QLabel("")
        self.kq_frame.addWidget(self.label_kq)
        self.kq_widget = None 

        self.time_label = QLabel("⏱ Thời gian: 00:00:00")

        status_layout.addStretch()
        status_layout.addLayout(self.kq_frame)
        status_layout.addWidget(self.time_label)
        status_layout.addStretch()

        self.main_layout.addWidget(status_frame)

        # 6. Label trạng thái chi tiết
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.main_layout.addWidget(self.status_label)

        # 7. Label trạng thái model
        self.model_status_label = QLabel("Đang tải model...")
        self.model_status_label.setAlignment(Qt.AlignCenter)
        self.model_status_label.setStyleSheet("color: orange;")
        self.main_layout.addWidget(self.model_status_label)

        # 8. Log box
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        self.main_layout.addWidget(self.log_box)

        # Timer cho đồng hồ
        self.timer = QTimer(self)
        
        self.update_status()

    def setup_connections(self):
        self.text_box.textChanged.connect(self.update_mst_count)
        self.entry_workers.editingFinished.connect(self.on_workers_change)
        
        self.btn_start.clicked.connect(self.bat_dau)
        self.btn_pause.clicked.connect(self.toggle_pause)
        self.btn_retry.clicked.connect(self.lam_lai)
        self.btn_open_result.clicked.connect(self.open_result_file_directly)
        self.btn_open_folder.clicked.connect(self.open_result_folder)
        self.btn_debug.clicked.connect(self.open_debug_window)
        self.btn_exit.clicked.connect(self.close)

        self.timer.timeout.connect(self.update_time_display)

    def closeEvent(self, event):
        reply = QMessageBox.question(self, 'Xác nhận thoát', 'Bạn có chắc muốn thoát chương trình không?',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

    # =====================================================================
    # CÁC HÀM XỬ LÝ LOGIC (Đã được chuyển đổi sang PySide6)
    # =====================================================================
    @Slot()
    def log(self, msg: str):
        self.log_box.append(msg)
    
    @Slot()
    def update_mst_count(self):
        content = self.text_box.toPlainText().strip()
        if content:
            lines = [line.strip() for line in content.splitlines() if line.strip()]
            count = len(lines)
            self.mst_count_label.setText(f"{self.MST_COUNT_PREFIX}{count}")
        else:
            self.mst_count_label.setText(f"{self.MST_COUNT_PREFIX}0")

    @Slot()
    def update_status(self):
        captcha_total = self.captcha_ok + self.captcha_fail
        percent = (self.captcha_ok * 100 / captcha_total) if captcha_total > 0 else 0
        captcha_info = f"🧩 Captcha đúng: {self.captcha_ok}/{captcha_total} ({percent:.1f}%)"
        
        max_workers = QThreadPool.globalInstance().maxThreadCount()
        status_text = (f"✔ Thành công: {self.ok}   ❌ Thất bại: {self.fail}   {captcha_info}   "
                       f"✍️ Đã xử lý: {self.done} / {self.total}   "
                       f"👨‍👩‍👦‍👦 Luồng: {self.active_threads}/{max_workers}")
        self.status_label.setText(status_text)
        
    @Slot()
    def update_time_display(self):
        if self.start_time:
            elapsed = int(time.time() - self.start_time)
            hours, remainder = divmod(elapsed, 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.setText(f"⏱ {hours:02d}:{minutes:02d}:{seconds:02d}")

    def load_model_bg(self):
        # self.btn_start.setEnabled(False)  # Không disable nút bắt đầu nữa
        self.model_status_label.setText("Đang tải model...")
        self.model_status_label.setStyleSheet("color: orange;")

        class ModelLoader(QRunnable):
            def __init__(self, signals):
                super().__init__()
                self.signals = signals
            def run(self):
                try:
                    # Sử dụng callback để báo cáo tiến trình
                    load_model(progress_callback=self.signals.progress.emit) 
                    self.signals.finished.emit()
                except Exception as e:
                    self.signals.error.emit(str(e))
        
        class ModelSignals(QObject):
            progress = Signal(int) # Thêm signal cho tiến trình
            finished = Signal()
            error = Signal(str)
            
        self.model_signals = ModelSignals()
        self.model_signals.progress.connect(self.on_model_progress) # Kết nối signal
        self.model_signals.finished.connect(self.on_model_loaded)
        self.model_signals.error.connect(self.on_model_load_error)
        
        # Chạy trong background thread pool
        QThreadPool.globalInstance().start(ModelLoader(self.model_signals))

    @Slot(int)
    def on_model_progress(self, percent):
        """Cập nhật label trạng thái với % tải."""
        self._last_model_percent = percent
        if hasattr(self, '_pending_start') and self._pending_start:
            self.model_status_label.setText(f"Đang tải model... Sẽ tự động bắt đầu khi model sẵn sàng. {percent}%")
        else:
            self.model_status_label.setText(f"Đang tải model... {percent}%")

    @Slot()
    def on_model_loaded(self):
        self.model_status_label.setText("Model đã sẵn sàng")
        self.model_status_label.setStyleSheet("color: green;")
        self._model_ready = True
        # self.btn_start.setEnabled(True)  # Không cần enable lại vì luôn sáng
        # Nếu có pending_start thì tự động bắt đầu
        if hasattr(self, '_pending_start') and self._pending_start:
            self._pending_start = False
            self.bat_dau()

    @Slot(str)
    def on_model_load_error(self, error_msg):
        self.model_status_label.setText(f"Lỗi tải model: {error_msg}")
        self.model_status_label.setStyleSheet("color: red;")
        QMessageBox.critical(self, "Lỗi nghiêm trọng", f"Không thể tải model TrOCR:\n{error_msg}\nChương trình sẽ thoát.")
        self.close()

    def check_setting_file(self):
        """Kiểm tra và thông báo về file setting.ini"""
        if os.path.exists("setting.ini"):
            try:
                test_cfg = configparser.ConfigParser()
                test_cfg.read("setting.ini", encoding="utf-8")
                workers = test_cfg.getint("DEFAULT", "max_workers", fallback=None)
                retry = test_cfg.getint("DEFAULT", "retry_failed", fallback=None)
                timeout = test_cfg.getint("DEFAULT", "request_timeout", fallback=None)
                ocr_timeout = test_cfg.getint("DEFAULT", "ocr_timeout", fallback=None)
                if workers is not None and retry is not None and timeout is not None and ocr_timeout is not None:
                    self.log("✅ File setting.ini đã được tìm thấy và hợp lệ")
                    self.log(f"   - Số luồng mặc định: {workers}")
                    self.log(f"   - Số lần thử lại: {retry}")
                    self.log(f"   - Timeout request: {timeout}s")
                    self.log(f"   - Timeout OCR captcha: {ocr_timeout}s")
                else:
                    self.log("⚠️ File setting.ini tồn tại nhưng thiếu một số cấu hình")
                    self.log("   Sử dụng giá trị mặc định")
            except Exception as e:
                self.log(f"⚠️ File setting.ini có lỗi: {e}")
                self.log("   Sử dụng giá trị mặc định")
        else:
            self.log("❌ Không tìm thấy file setting.ini")
            self.log("   Tạo file setting.ini với cấu hình mặc định...")
            self.create_default_setting_file()

    def create_default_setting_file(self):
        """Tạo file setting.ini với cấu hình mặc định"""
        try:
            default_cfg = configparser.ConfigParser()
            default_cfg['DEFAULT'] = {
                'max_workers': '20',
                'retry_failed': '20',
                'request_timeout': '120',
                'ocr_timeout': '120'
            }
            with open('setting.ini', 'w', encoding='utf-8') as f:
                default_cfg.write(f)
            self.log("✅ Đã tạo file setting.ini với cấu hình mặc định")
        except Exception as e:
            self.log(f"❌ Không thể tạo file setting.ini: {e}")

    def save_workers_to_setting(self, workers_count):
        """Lưu số luồng vào file setting.ini"""
        global default_workers, cfg
        try:
            # Đọc file setting hiện tại hoặc tạo mới nếu không tồn tại
            setting_cfg = configparser.ConfigParser()
            if os.path.exists('setting.ini'):
                setting_cfg.read('setting.ini', encoding='utf-8')

            # Đảm bảo có section DEFAULT
            if 'DEFAULT' not in setting_cfg:
                setting_cfg['DEFAULT'] = {}

            # Cập nhật số luồng
            setting_cfg['DEFAULT']['max_workers'] = str(workers_count)

            # Ghi lại file
            with open('setting.ini', 'w', encoding='utf-8') as f:
                setting_cfg.write(f)

            # Cập nhật biến global
            default_workers = workers_count
            cfg.set('DEFAULT', 'max_workers', str(workers_count))

            self.log(f"💾 Đã lưu số luồng {workers_count} vào file setting.ini")

        except Exception as e:
            self.log(f"⚠️ Lỗi khi lưu setting: {e}")

    @Slot()
    def on_workers_change(self):
        value = self.entry_workers.text().strip()
        if value.isdigit() and int(value) > 0:
            new_workers = int(value)
            current_max = QThreadPool.globalInstance().maxThreadCount()
            if new_workers != current_max:
                reply = QMessageBox.question(self, "Xác nhận", f"Bạn có muốn cập nhật số luồng từ {current_max} thành {new_workers} không?")
                if reply == QMessageBox.Yes:
                    QThreadPool.globalInstance().setMaxThreadCount(new_workers)
                    self.log(f"🔧 Đã cập nhật số luồng: {new_workers}")
                    # Lưu số luồng mới vào file setting
                    self.save_workers_to_setting(new_workers)
                    self.update_status()
                else:
                    self.entry_workers.setText(str(current_max))
        else:
            self.entry_workers.setText(str(QThreadPool.globalInstance().maxThreadCount()))

    @Slot()
    def toggle_pause(self):
        self.is_paused = not self.is_paused
        if self.is_paused:
            self.btn_pause.setText("▶️ Chạy tiếp")
            self.log("⏸ Đã tạm dừng. Các luồng đang chạy sẽ hoàn thành nốt.")
            QThreadPool.globalInstance().setMaxThreadCount(0) # Tạm dừng gửi task mới
        else:
            self.btn_pause.setText("⏸ Tạm dừng")
            max_threads = int(self.entry_workers.text())
            self.log(f"▶️ Đã chạy tiếp với {max_threads} luồng.")
            QThreadPool.globalInstance().setMaxThreadCount(max_threads)
            self.process_queue()
        self.update_status()

    def show_kq_loading(self, show=True):
        if show:
            if self.kq_widget: return
            gif_path = "resource/working.gif"
            if os.path.exists(gif_path):
                 self.kq_widget = FlippingGIFLabel(gif_path, size=(28,28), parent=self)
            else: # Fallback
                self.kq_widget = EmojiAnimationLabel(["🧎🏻‍♂️","🧍🏻‍♂️","🚶🏻‍♂️","🏃🏻‍♂️"], delay=300, size=28, parent=self)
            self.kq_frame.insertWidget(0, self.kq_widget)
            self.label_kq.setText(" Đang chạy...")
        else:
            if self.kq_widget:
                self.kq_widget.deleteLater()
                self.kq_widget = None
            self.label_kq.setText("")

    def show_kq_done(self):
        self.show_kq_loading(False)
        self.kq_widget = QLabel("🎉", self)
        self.kq_widget.setFont(QFont("Segoe UI Emoji", 16))
        self.kq_frame.insertWidget(0, self.kq_widget)
        self.label_kq.setText(" Đã xong hết rồi!")
    
    @Slot(str)
    def handle_worker_error(self, error_msg):
        self.log(error_msg)
        # Có thể thêm logic khác ở đây, ví dụ hiện messagebox
        
    @Slot()
    def bat_dau(self):
        if not getattr(self, '_model_ready', False):
            # Nếu model chưa sẵn sàng, chờ model xong rồi mới chạy
            self._pending_start = True
            # Hiển thị % nếu có
            percent = getattr(self, '_last_model_percent', 0)
            self.model_status_label.setText(f"Đang tải model... Sẽ tự động bắt đầu khi model sẵn sàng. {percent}%")
            self.btn_start.setEnabled(False)
            return
        self.btn_start.setEnabled(False)
        self._pending_start = False
        # Reset trạng thái label kq trước
        self.label_kq.setText("")
        if self.kq_widget:
            self.kq_widget.deleteLater()
            self.kq_widget = None

        raw_text = self.text_box.toPlainText()
        danh_sach_raw = [line.strip() for line in raw_text.splitlines() if line.strip()]

        if not danh_sach_raw:
            self.label_kq.setText("❌ Không có MST nào.")
            return

        for idx, mst in enumerate(danh_sach_raw, 1):
            if " " in mst or "\t" in mst:
                self.label_kq.setText(f"❌ MST dòng {idx} chứa khoảng trắng/tab: [{mst}]")
                return

        self.danh_sach = danh_sach_raw
        # Reset state
        self.update_indices = None
        self.output_lines_full = None
        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        self.active_threads = 0
        self.total = len(self.danh_sach)
        self.output_lines = [""] * self.total
        self.is_paused = False
        
        # Setup file kết quả
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.ketqua_file = f"ketqua_{timestamp}.txt"
        
        self.log(f"Bắt đầu tra cứu {self.total} MST...")
        self.update_status()
        
        # Bắt đầu timer
        self.start_time = time.time()
        self.timer.start(1000)

        # Cập nhật UI
        self.btn_start.setEnabled(False)
        self.btn_retry.setEnabled(False)
        self.btn_pause.setEnabled(True)
        self.btn_open_result.setEnabled(True)
        self.show_kq_loading(True)

        # Tạo queue và bắt đầu xử lý
        self.task_queue = list(enumerate(self.danh_sach))
        QThreadPool.globalInstance().setMaxThreadCount(int(self.entry_workers.text()))
        self.process_queue()
        
    def process_queue(self):
        while self.task_queue:
            if QThreadPool.globalInstance().activeThreadCount() >= QThreadPool.globalInstance().maxThreadCount():
                break # Chờ luồng trống

            idx, mst = self.task_queue.pop(0)
            worker = TraCuuWorker(mst, idx, self.debug_flags.copy())
            worker.signals.log.connect(self.log)
            worker.signals.update_result.connect(self.on_worker_update)
            worker.signals.captcha_result.connect(self.on_captcha_result)
            worker.signals.task_completed.connect(self.on_task_completed)
            worker.signals.finished.connect(self.on_worker_finished)
            worker.signals.error.connect(self.handle_worker_error)
            
            self.active_threads += 1
            self.log(f"🔧 Số luồng đang hoạt động tăng: {self.active_threads - 1} → {self.active_threads}")
            self.update_status()
            QThreadPool.globalInstance().start(worker)

    @Slot(int, str)
    def on_worker_update(self, idx, text):
        # Cập nhật vào list kết quả
        if self.update_indices:
             real_idx = self.update_indices[idx]
             self.output_lines_full[real_idx] = text
        else:
            self.output_lines[idx] = text
        
        self.write_result_file()
        self.log(text)

    @Slot(bool)
    def on_task_completed(self, is_ok):
        """Chỉ xử lý việc đếm ok/fail ở đây."""
        if is_ok:
            self.ok += 1
        else:
            self.fail += 1
        # Không cần update_status ở đây vì on_worker_finished sẽ làm

    @Slot(bool)
    def on_captcha_result(self, is_ok):
        if is_ok: self.captcha_ok += 1
        else: self.captcha_fail += 1
        self.update_status()
        
    @Slot()
    def on_worker_finished(self):
        self.active_threads -= 1
        self.log(f"🔧 Số luồng đang hoạt động giảm: {self.active_threads + 1} → {self.active_threads}")
        self.done += 1
        self.update_status()

        if not self.is_paused:
            self.process_queue() # Lấy task tiếp theo

        if self.done == self.total:
            self.finish_processing()
            
    def finish_processing(self):
        self.timer.stop()
        self.log("🎯 Hoàn thành xử lý tất cả MST")
        
        self.show_kq_done()
        self.btn_start.setEnabled(True)
        self.btn_retry.setEnabled(True)
        self.btn_pause.setEnabled(False)
        self.is_paused = False
        
        # Thay đổi điều kiện: chỉ hỏi làm lại khi self.fail > 0
        if self.fail > 0:
            reply = QMessageBox.question(self, "Hoàn thành",
                                         "🎉 Đã xong hết rồi!\nCó kết quả không lấy được địa chỉ.\n"
                                         "Bạn có muốn làm lại ngay từ file kết quả này không?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
            if reply == QMessageBox.Yes:
                self._auto_lamlai_from_last_result()
        else:
            reply = QMessageBox.question(self, "Hoàn thành", 
                                         "🎉 Đã xong hết rồi!\nBạn có muốn mở file kết quả không?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
            if reply == QMessageBox.Yes:
                self.open_result_file_directly()

    def write_result_file(self):
        try:
            lines_to_write = self.output_lines_full if self.output_lines_full is not None else self.output_lines
            with open(self.ketqua_file, "w", encoding="utf-8") as f:
                f.write("\n".join(lines_to_write))
        except Exception as e:
            self.log(f"⚠ Lỗi ghi file kết quả: {e}")

    @Slot()
    def open_result_file_directly(self):
        if not self.ketqua_file or not os.path.exists(self.ketqua_file):
            QMessageBox.warning(self, "Thông báo", "Chưa có file kết quả hoặc file đã bị xóa.")
            return
        os.startfile(self.ketqua_file)
        self.log(f"📄 Đã mở file kết quả: {self.ketqua_file}")

    @Slot()
    def open_result_folder(self):
        try:
            if getattr(sys, 'frozen', False):
                app_dir = os.path.dirname(sys.executable)
                os.startfile(app_dir)
                self.log(f"📁 Đã mở thư mục ứng dụng: {app_dir}")
            else:
                script_dir = os.path.dirname(os.path.abspath(__file__))
                os.startfile(script_dir)
                self.log(f"📁 Đã mở thư mục script: {script_dir}")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở thư mục:\n{e}")
            self.log(f"❌ Lỗi mở thư mục: {e}")

    @Slot()
    def lam_lai(self):
        retry_dialog = RetryDialog(self)
        if retry_dialog.exec() != QDialog.Accepted:
            return
            
        options = retry_dialog.get_options()
        path = options['path']
        if not path or not os.path.exists(path):
            QMessageBox.warning(self, "Lỗi", f"File không tồn tại hoặc đường dẫn trống:\n{path}")
            return
            
        self.retry_processing(path, options['retry_pending'], options['retry_failed'])

    def retry_processing(self, file_path: str, retry_pending: bool, retry_failed: bool):
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
        except Exception as e:
            QMessageBox.critical(self, "Lỗi đọc file", f"Không thể đọc file kết quả:\n{e}")
            self.log(f"⚠ Lỗi đọc file kết quả: {e}")
            return

        self.output_lines_full = [line.strip() for line in lines]
        
        tasks_to_retry = []
        self.update_indices = []

        for idx, line in enumerate(self.output_lines_full):
            should_retry = False
            # Chuyển sang lower() để bắt case-insensitive
            line_lower = line.lower()
            if retry_pending and "đang kiểm tra" in line_lower:
                should_retry = True
            elif retry_failed and "không lấy được địa chỉ" in line_lower:
                should_retry = True
            
            if should_retry:
                parts = line.strip().split("\t")
                if len(parts) >= 2:
                    mst = parts[1]
                    tasks_to_retry.append(mst)
                    self.update_indices.append(idx)
        
        if not tasks_to_retry:
            QMessageBox.information(self, "Thông báo", "Không có MST nào phù hợp trong file được chọn để tra lại.")
            return

        # Reset state & UI for the new run
        self.danh_sach = tasks_to_retry
        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        self.active_threads = 0
        self.total = len(self.danh_sach)
        self.is_paused = False
        
        # Create a new result file for the retry
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name, ext = os.path.splitext(os.path.basename(file_path))
        dir_name = os.path.dirname(file_path)
        self.ketqua_file = os.path.join(dir_name, f"{base_name}_lamlai_{timestamp}{ext}")
        
        # We write the full original content to the new file first
        self.write_result_file() 
        self.log(f"🔁 Bắt đầu tra lại từ file: {file_path} ({self.total} MST)")
        
        self.start_time = time.time()
        self.timer.start(1000)

        self.btn_start.setEnabled(False)
        self.btn_retry.setEnabled(False)
        self.btn_pause.setEnabled(True)
        self.btn_open_result.setEnabled(True)
        self.show_kq_loading(True)

        # Cần reset lại output_lines cho lần chạy lại này, map từ index của task_queue sang index của file gốc
        self.output_lines = [""] * len(self.danh_sach)
        self.task_queue = list(enumerate(self.danh_sach))
        
        QThreadPool.globalInstance().setMaxThreadCount(int(self.entry_workers.text()))
        self.process_queue()

    @Slot()
    def open_debug_window(self):
        debug_dialog = DebugDialog(self.debug_flags, self)
        if debug_dialog.exec() == QDialog.Accepted:
            self.debug_flags = debug_dialog.new_flags
            QMessageBox.information(self, "Debug", "Chế độ debug đã được lưu cho lần tra cứu tiếp theo!")
        
    def _auto_lamlai_from_last_result(self):
        # Automatically retry failed and pending items from the last run
        self.retry_processing(self.ketqua_file, retry_pending=True, retry_failed=True)

class DebugDialog(QDialog):
    """Dialog cho các công cụ debug."""
    def __init__(self, current_flags, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Debug Tools")
        self.setFixedSize(400, 250)
        self.setModal(True)

        self.new_flags = current_flags.copy()

        layout = QVBoxLayout(self)

        info_label = QLabel("Tính năng này dành cho việc kiểm tra lỗi, trong ngữ cảnh bình thường vui lòng không sử dụng")
        info_label.setStyleSheet("color: red;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        self.save_cb = QCheckBox("Lưu file trả về (raw HTML/json)")
        self.save_cb.setChecked(self.new_flags.get('save_response', False))
        layout.addWidget(self.save_cb)

        self.wrong_cb = QCheckBox("Cố ý sai captcha")
        self.wrong_cb.setChecked(self.new_flags.get('wrong_captcha', False))
        layout.addWidget(self.wrong_cb)

        self.fail_cb = QCheckBox("Kết quả luôn là không tìm được địa chỉ")
        self.fail_cb.setChecked(self.new_flags.get('always_fail', False))
        layout.addWidget(self.fail_cb)
        
        button_layout = QHBoxLayout()
        save_button = QPushButton("Lưu")
        close_button = QPushButton("Đóng")

        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)

        save_button.clicked.connect(self.save_and_close)
        close_button.clicked.connect(self.reject)

    def save_and_close(self):
        self.new_flags['save_response'] = self.save_cb.isChecked()
        self.new_flags['wrong_captcha'] = self.wrong_cb.isChecked()
        self.new_flags['always_fail'] = self.fail_cb.isChecked()
        self.accept()

class RetryDialog(QDialog):
    """Dialog để chọn file và các tùy chọn làm lại."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔁 Làm lại từ kết quả cũ")
        self.setMinimumWidth(450)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Chọn file kết quả cũ (.txt):"))

        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("Đường dẫn tới file kết quả...")
        path_layout.addWidget(self.path_edit)
        browse_button = QPushButton("Chọn file")
        browse_button.clicked.connect(self.browse_file)
        path_layout.addWidget(browse_button)
        layout.addLayout(path_layout)
        
        self.retry_pending_cb = QCheckBox("Tra cứu lại các kết quả 'Đang kiểm tra'")
        self.retry_pending_cb.setChecked(True)
        layout.addWidget(self.retry_pending_cb)

        self.retry_failed_cb = QCheckBox("Tra cứu lại các kết quả 'không lấy được địa chỉ'")
        self.retry_failed_cb.setChecked(True)
        layout.addWidget(self.retry_failed_cb)

        layout.addWidget(QLabel("➡️ Chức năng này cho phép tra lại những dòng chưa có kết quả."))

        button_layout = QHBoxLayout()
        start_button = QPushButton("🚀 Bắt đầu")
        cancel_button = QPushButton("Hủy")
        button_layout.addStretch()
        button_layout.addWidget(start_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
        start_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)

    def browse_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Chọn file kết quả", "", "Text files (*.txt)")
        if file_path:
            self.path_edit.setText(file_path)

    def get_options(self):
        return {
            "path": self.path_edit.text(),
            "retry_pending": self.retry_pending_cb.isChecked(),
            "retry_failed": self.retry_failed_cb.isChecked()
        }

def main():
    app = QApplication(sys.argv)
    
    # Tăng cỡ chữ chung cho toàn bộ ứng dụng
    font = app.font()
    font.setPointSize(font.pointSize() + 2)
    app.setFont(font)

    # Cấu hình thread pool toàn cục
    QThreadPool.globalInstance().setMaxThreadCount(default_workers)
    
    window = MainWindow()
    window.resize(800, 600)
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()