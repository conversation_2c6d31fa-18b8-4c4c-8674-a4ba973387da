#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/bitwise_xor_ops.h>

namespace at {


// aten::bitwise_xor.Tensor_out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bitwise_xor_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::bitwise_xor_Tensor_out::call(self, other, out);
}
// aten::bitwise_xor.Tensor_out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bitwise_xor_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out) {
    return at::_ops::bitwise_xor_Tensor_out::call(self, other, out);
}

// aten::bitwise_xor.Scalar_out(Tensor self, Scalar other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bitwise_xor_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other) {
    return at::_ops::bitwise_xor_Scalar_out::call(self, other, out);
}
// aten::bitwise_xor.Scalar_out(Tensor self, Scalar other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bitwise_xor_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out) {
    return at::_ops::bitwise_xor_Scalar_out::call(self, other, out);
}

// aten::bitwise_xor.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor bitwise_xor(const at::Tensor & self, const at::Scalar & other) {
    return at::_ops::bitwise_xor_Scalar::call(self, other);
}

// aten::bitwise_xor.Scalar_Tensor(Scalar self, Tensor other) -> Tensor
inline at::Tensor bitwise_xor(const at::Scalar & self, const at::Tensor & other) {
    return at::_ops::bitwise_xor_Scalar_Tensor::call(self, other);
}

// aten::bitwise_xor.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor bitwise_xor(const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::bitwise_xor_Tensor::call(self, other);
}

// aten::bitwise_xor.Scalar_Tensor_out(Scalar self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bitwise_xor_out(at::Tensor & out, const at::Scalar & self, const at::Tensor & other) {
    return at::_ops::bitwise_xor_Scalar_Tensor_out::call(self, other, out);
}
// aten::bitwise_xor.Scalar_Tensor_out(Scalar self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bitwise_xor_outf(const at::Scalar & self, const at::Tensor & other, at::Tensor & out) {
    return at::_ops::bitwise_xor_Scalar_Tensor_out::call(self, other, out);
}

}
