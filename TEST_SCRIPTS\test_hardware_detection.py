#!/usr/bin/env python3
"""
Script để test việc detect phần cứng GPU và CPU cho OCR
"""

import os
import sys
import platform
import subprocess
import json
from typing import Dict, List, Optional, Tuple

def get_cpu_info() -> Dict[str, str]:
    """Lấy thông tin CPU"""
    cpu_info = {
        'name': platform.processor() or 'Unknown CPU',
        'architecture': platform.machine(),
        'cores': str(os.cpu_count() or 'Unknown')
    }
    
    # Thử lấy thông tin chi tiết hơn trên Windows
    if platform.system() == 'Windows':
        try:
            result = subprocess.run(['wmic', 'cpu', 'get', 'name'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    cpu_name = lines[1].strip()
                    if cpu_name:
                        cpu_info['name'] = cpu_name
        except:
            pass
    
    return cpu_info

def get_nvidia_gpu_info() -> List[Dict[str, str]]:
    """Lấy thông tin GPU NVIDIA qua nvidia-smi"""
    gpus = []
    try:
        # Thử chạy nvidia-smi
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=name,memory.total,driver_version,compute_cap', 
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                if line.strip():
                    parts = [p.strip() for p in line.split(',')]
                    if len(parts) >= 4:
                        gpus.append({
                            'index': str(i),
                            'name': parts[0],
                            'memory': f"{parts[1]} MB",
                            'driver': parts[2],
                            'compute_cap': parts[3],
                            'vendor': 'NVIDIA'
                        })
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        pass
    
    return gpus

def get_amd_gpu_info() -> List[Dict[str, str]]:
    """Lấy thông tin GPU AMD"""
    gpus = []
    
    # Thử dùng rocm-smi (Linux)
    try:
        result = subprocess.run(['rocm-smi', '--showproductname'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                if 'GPU' in line and ':' in line:
                    gpu_name = line.split(':', 1)[1].strip()
                    gpus.append({
                        'index': str(i),
                        'name': gpu_name,
                        'vendor': 'AMD'
                    })
    except:
        pass
    
    # Thử dùng Windows Device Manager info
    if platform.system() == 'Windows' and not gpus:
        try:
            result = subprocess.run([
                'wmic', 'path', 'win32_VideoController', 'get', 'name'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for i, line in enumerate(lines[1:]):  # Skip header
                    line = line.strip()
                    if line and ('AMD' in line or 'Radeon' in line):
                        gpus.append({
                            'index': str(i),
                            'name': line,
                            'vendor': 'AMD'
                        })
        except:
            pass
    
    return gpus

def get_intel_gpu_info() -> List[Dict[str, str]]:
    """Lấy thông tin GPU Intel"""
    gpus = []
    
    if platform.system() == 'Windows':
        try:
            result = subprocess.run([
                'wmic', 'path', 'win32_VideoController', 'get', 'name'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for i, line in enumerate(lines[1:]):  # Skip header
                    line = line.strip()
                    if line and 'Intel' in line:
                        gpus.append({
                            'index': str(i),
                            'name': line,
                            'vendor': 'Intel'
                        })
        except:
            pass
    
    return gpus

def get_pytorch_device_info() -> Dict[str, any]:
    """Lấy thông tin device từ PyTorch"""
    device_info = {
        'torch_available': False,
        'cuda_available': False,
        'cuda_device_count': 0,
        'cuda_devices': [],
        'current_device': 'cpu'
    }
    
    try:
        import torch
        device_info['torch_available'] = True
        
        # CUDA info
        if torch.cuda.is_available():
            device_info['cuda_available'] = True
            device_info['cuda_device_count'] = torch.cuda.device_count()
            device_info['current_device'] = 'cuda'
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                device_info['cuda_devices'].append({
                    'index': i,
                    'name': props.name,
                    'memory': f"{props.total_memory // (1024**2)} MB",
                    'compute_capability': f"{props.major}.{props.minor}",
                    'multiprocessor_count': props.multi_processor_count
                })
        
        # Thử các backend khác
        backends = []
        try:
            if hasattr(torch, 'xpu') and hasattr(torch.xpu, 'is_available') and torch.xpu.is_available():
                backends.append('XPU (Intel)')
        except:
            pass

        try:
            if hasattr(torch, 'mps') and hasattr(torch.mps, 'is_available') and torch.mps.is_available():
                backends.append('MPS (Apple)')
        except:
            pass

        device_info['other_backends'] = backends
        
    except ImportError:
        pass
    
    return device_info

def determine_best_device() -> Tuple[str, str, Dict]:
    """Xác định device tốt nhất cho OCR"""
    pytorch_info = get_pytorch_device_info()
    
    # Ưu tiên CUDA nếu có
    if pytorch_info['cuda_available'] and pytorch_info['cuda_device_count'] > 0:
        best_cuda = None
        max_memory = 0
        
        for device in pytorch_info['cuda_devices']:
            memory_mb = int(device['memory'].split()[0])
            if memory_mb > max_memory:
                max_memory = memory_mb
                best_cuda = device
        
        if best_cuda:
            return 'cuda', f"NVIDIA {best_cuda['name']}", best_cuda
    
    # Thử các backend khác
    if pytorch_info['other_backends']:
        return pytorch_info['other_backends'][0].split()[0].lower(), pytorch_info['other_backends'][0], {}
    
    # Fallback to CPU
    cpu_info = get_cpu_info()
    return 'cpu', f"CPU: {cpu_info['name']}", cpu_info

def main():
    print("=== HARDWARE DETECTION TEST ===\n")
    
    # CPU Info
    print("🖥️  CPU Information:")
    cpu_info = get_cpu_info()
    for key, value in cpu_info.items():
        print(f"   {key}: {value}")
    print()
    
    # GPU Detection
    print("🎮 GPU Detection:")
    
    # NVIDIA
    nvidia_gpus = get_nvidia_gpu_info()
    if nvidia_gpus:
        print("   NVIDIA GPUs found:")
        for gpu in nvidia_gpus:
            print(f"     - {gpu['name']} ({gpu.get('memory', 'Unknown memory')})")
    else:
        print("   No NVIDIA GPUs found")
    
    # AMD
    amd_gpus = get_amd_gpu_info()
    if amd_gpus:
        print("   AMD GPUs found:")
        for gpu in amd_gpus:
            print(f"     - {gpu['name']}")
    else:
        print("   No AMD GPUs found")
    
    # Intel
    intel_gpus = get_intel_gpu_info()
    if intel_gpus:
        print("   Intel GPUs found:")
        for gpu in intel_gpus:
            print(f"     - {gpu['name']}")
    else:
        print("   No Intel GPUs found")
    
    print()
    
    # PyTorch Device Info
    print("🔥 PyTorch Device Information:")
    pytorch_info = get_pytorch_device_info()
    
    if pytorch_info['torch_available']:
        print(f"   PyTorch available: ✅")
        print(f"   CUDA available: {'✅' if pytorch_info['cuda_available'] else '❌'}")
        print(f"   CUDA device count: {pytorch_info['cuda_device_count']}")
        
        if pytorch_info['cuda_devices']:
            print("   CUDA devices:")
            for device in pytorch_info['cuda_devices']:
                print(f"     - GPU {device['index']}: {device['name']} ({device['memory']})")
        
        if pytorch_info['other_backends']:
            print(f"   Other backends: {', '.join(pytorch_info['other_backends'])}")
    else:
        print("   PyTorch not available: ❌")
    
    print()
    
    # Best Device Recommendation
    print("🏆 Recommended Device for OCR:")
    device_type, device_name, device_info = determine_best_device()
    print(f"   Device Type: {device_type}")
    print(f"   Device Name: {device_name}")
    
    if device_info:
        print("   Device Details:")
        for key, value in device_info.items():
            if key not in ['index']:
                print(f"     {key}: {value}")
    
    print("\n=== TEST COMPLETED ===")

if __name__ == "__main__":
    main()
