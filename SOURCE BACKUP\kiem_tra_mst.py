import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests, os, datetime, threading, re, json, configparser, traceback, concurrent.futures
import time
from typing import Optional, List, Dict, Any
import random

from trocr_solver import load_model, solve_captcha  # Dùng TrOCR offline
from PIL import Image, ImageTk, ImageDraw, ImageFont

cfg = configparser.ConfigParser()
cfg.read("setting.ini", encoding="utf-8")
default_workers = cfg.getint("DEFAULT", "max_workers", fallback=20)
default_retry_failed = cfg.getint("DEFAULT", "retry_failed", fallback=20)
default_timeout = cfg.getint("DEFAULT", "request_timeout", fallback=120)
default_ocr_timeout = cfg.getint("DEFAULT", "ocr_timeout", fallback=120)

def clear_temp_folder():
    """<PERSON>óa tất cả file trong thư mục TEM<PERSON>, và xóa luôn thư mục nếu rỗng"""
    if os.path.exists("TEMP"):
        for file in os.listdir("TEMP"):
            path = os.path.join("TEMP", file)
            try:
                if os.path.isfile(path):
                    os.remove(path)
            except (OSError, PermissionError) as e:
                print(f"Không thể xóa file {path}: {e}")
        # Sau khi xóa hết file, nếu thư mục rỗng thì xóa luôn thư mục TEMP
        try:
            if not os.listdir("TEMP"):
                os.rmdir("TEMP")
        except Exception as e:
            print(f"Không thể xóa thư mục TEMP: {e}")

# ======= Thêm class emoji động với màu =======
class EmojiAnimationLabel(tk.Label):
    def __init__(self, master, emojis, delay=200, size=32, *args, **kwargs):
        super().__init__(master, *args, **kwargs)
        self.emojis = emojis
        self.delay = delay
        self.size = size
        self.idx = 0
        self.emoji_images = []
        self.create_emoji_images()
        self.animate()

    def create_emoji_images(self):
        """Tạo danh sách emoji màu"""
        for emoji in self.emojis:
            try:
                font = ImageFont.truetype("seguiemj.ttf", size=int(round(self.size*72/96, 0)))
                im = Image.new("RGBA", (self.size, self.size), (255, 255, 255, 0))
                draw = ImageDraw.Draw(im)
                draw.text((self.size/2, self.size/2), emoji, embedded_color=True, font=font, anchor="mm")
                self.emoji_images.append(ImageTk.PhotoImage(im))
            except Exception:
                # Fallback về emoji text nếu không tạo được emoji màu
                self.emoji_images.append(None)

    def animate(self):
        if self.emoji_images[self.idx] is not None:
            self.config(image=self.emoji_images[self.idx], text="")
        else:
            self.config(image="", text=self.emojis[self.idx])
        self.idx = (self.idx + 1) % len(self.emojis)
        self.after(self.delay, self.animate)

# ======= Thêm class hiển thị GIF động =======

class LoadingModelWindow:
    def __init__(self, root):
        self.root = root
        self.top = tk.Toplevel(root)
        self.top.title("Đang tải model TrOCR...")
        w, h = 320, 140
        x = (self.top.winfo_screenwidth() - w) // 2
        y = (self.top.winfo_screenheight() - h) // 2
        self.top.geometry(f"{w}x{h}+{x}+{y}")
        self.top.resizable(False, False)
        # Sử dụng emoji động đồng hồ cát
        self.emoji = EmojiAnimationLabel(self.top, ["⏳", "⌛"], delay=300, size=32)
        self.emoji.pack(pady=(10, 0))
        self.label = ttk.Label(self.top, text="Đang tải model TrOCR, vui lòng chờ...", font=("Arial", 11))
        self.label.pack(pady=5)
        self.progress = ttk.Progressbar(self.top, mode='determinate', length=250)
        self.progress.pack(pady=5)
        self.top.grab_set()
        self.top.protocol("WM_DELETE_WINDOW", lambda: None)  # Vô hiệu hóa nút đóng
        self.top.after(100, self.load_model)

    def load_model(self):
        def update(percent):
            self.progress["value"] = percent
            self.top.update_idletasks()

        def do_load():
            try:
                load_model(progress_callback=update)
            except Exception as e:
                self.label.config(text=f"Lỗi: {e}")
                messagebox.showerror("Lỗi", f"Không tải được model:\n{e}")
                self.top.destroy()
                self.root.destroy()  # Thoát app luôn
                return
            self.top.destroy()

        threading.Thread(target=do_load, daemon=True).start()

class TraCuuWorker:
    def __init__(self, mst: str, idx: int, app):
        self.mst = mst
        self.idx = idx
        self.session = requests.Session()
        self.app = app

    def run(self):
        def safe_ui_call(func, *args, **kwargs):
            # Đảm bảo gọi hàm UI trên main thread
            self.app.root.after(0, func, *args, **kwargs)

        with self.app.lock:
            safe_ui_call(self.app._inc_active_threads)

        safe_ui_call(self.app.update_line_and_file, self.idx, f"Đang kiểm tra...\t{self.mst}")
        safe_ui_call(self.app.log, f"[{self.mst}] 🚀 Bắt đầu xử lý")

        try:
            attempt = 0
            while attempt < default_retry_failed:
                try:
                    safe_ui_call(self.app.log, f"[{self.mst}] 🧩 Đang lấy captcha")
                    dia_chi = self.tra_cuu()
                    safe_ui_call(self.app.log, f"[{self.mst}] ✅ Đã lấy được địa chỉ")

                    if dia_chi.lower().startswith("không lấy được"):
                        raise Exception("Không lấy được địa chỉ")

                    if not dia_chi.strip():
                        dia_chi = "(Rỗng)"

                    result_text = f"{dia_chi}\t{self.mst}"
                    safe_ui_call(self.app.update_line_and_file, self.idx, result_text)
                    safe_ui_call(self.app._inc_ok)
                    break
                except Exception as e:
                    attempt += 1
                    safe_ui_call(self.app.log, f"[{self.mst}] ❌ Lỗi lần {attempt}: {e}")
                    if attempt >= default_retry_failed:
                        fail_text = f"không lấy được địa chỉ\t{self.mst}"
                        safe_ui_call(self.app.update_line_and_file, self.idx, fail_text)
                        safe_ui_call(self.app._inc_fail)

            safe_ui_call(self.app._inc_done)
            safe_ui_call(self.app.update_status)
        finally:
            # Đảm bảo luôn giảm active_threads khi kết thúc
            with self.app.lock:
                safe_ui_call(self.app._dec_active_threads)
                safe_ui_call(self.app.update_status)
            self.session.close()

    def tra_cuu(self) -> str:
        # Nếu bật debug_always_fail thì trả về luôn không lấy được địa chỉ
        if hasattr(self.app, 'debug_always_fail') and self.app.debug_always_fail:
            return "không lấy được địa chỉ (debug)"
        headers = {
            'User-Agent': 'Mozilla/5.0',
            'Referer': 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp'
        }

        try:
            self.session.get("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp", headers=headers, timeout=default_timeout)
        except requests.RequestException as e:
            raise Exception(f"Lỗi kết nối: {e}")

        captcha_url = "https://tracuunnt.gdt.gov.vn/tcnnt/captcha.png"
        os.makedirs("TEMP", exist_ok=True)
        temp_path = f"TEMP/captcha_temp_{self.mst}_{threading.get_ident()}.png"

        try:
            r = self.session.get(captcha_url, headers=headers, timeout=default_timeout)
            r.raise_for_status()
            with open(temp_path, "wb") as f:
                f.write(r.content)
        except requests.RequestException as e:
            raise Exception(f"Lỗi tải captcha: {e}")

        # DEBUG: Nếu bật debug_enable_wrong thì nhập captcha ngẫu nhiên
        if hasattr(self.app, 'debug_enable_wrong') and self.app.debug_enable_wrong:
            code = ''.join(random.choices('ABCDEFGHJKLMNPQRSTUVWXYZ23456789', k=5))
        else:
            try:
                # Thêm timeout cho quá trình OCR captcha
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(solve_captcha, temp_path)
                    try:
                        code = future.result(timeout=default_ocr_timeout)
                        if not code or "❌" in code:
                            raise Exception("Lỗi OCR captcha")
                    except concurrent.futures.TimeoutError:
                        raise Exception(f"OCR captcha timeout sau {default_ocr_timeout} giây")
            finally:
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except (OSError, PermissionError):
                        pass

        data = {"mst": self.mst, "fullname": "", "address": "", "captcha": code}
        self.app.log(f"[{self.mst}] 📤 Gửi request tra cứu")
        
        try:
            res = self.session.post("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp", data=data, headers=headers, timeout=default_timeout)
            res.raise_for_status()
        except requests.RequestException as e:
            raise Exception(f"Lỗi gửi request: {e}")
            
        html = res.text
        # DEBUG: Nếu bật debug_enable_save thì lưu file trả về (chỉ khi không sai captcha)
        if hasattr(self.app, 'debug_enable_save') and self.app.debug_enable_save and (not hasattr(self.app, 'debug_enable_wrong') or not self.app.debug_enable_wrong):
            os.makedirs("debug", exist_ok=True)
            ts = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = os.path.join("debug", f"debug_raw_{self.mst}_{ts}.txt")
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(html)
            self.app.debug_enable_save = False
            self.app.log(f"[DEBUG] Đã lưu file trả về: {file_path}")

        if any(x in html.lower() for x in [
            "vui lòng nhập đúng mã xác nhận",
            "vui l&#242;ng nh&#7853;p &#273;&#250;ng m&#227; x&#225;c nh&#7853;n"
        ]):
            with self.app.lock:
                self.app.captcha_fail += 1
            self.app.root.after(0, self.app.update_status)
            raise Exception("Captcha sai")
            
        # ✅ Nếu không vào đoạn trên => captcha đúng
        with self.app.lock:
            self.app.captcha_ok += 1

        if "không tìm thấy người nộp thuế" in html.lower():
            return "Không tìm thấy người nộp thuế nào phù hợp."
        if "mã số thuế không hợp lệ" in html.lower():
            return "Mã số thuế không hợp lệ."

        try:
            match = re.search(r"var\s+nntJson\s*=\s*(\{.*?\});", html, re.DOTALL)
            if match:
                json_str = match.group(1)
                data = json.loads(json_str)
                # Tìm đúng MST trùng với self.mst
                info = None
                for item in data["DATA"]:
                    mst_item = item.get("MST", "")
                    if mst_item == self.mst:
                        info = item
                        break
                if not info:
                    return "không lấy được địa chỉ (không tìm thấy MST phù hợp)"
                dkt_dia_chi = info.get("DKT_DIA_CHI", [])
                dia_chi = self.get_from_list(dkt_dia_chi, "0300", "DIA_CHI")
                xa = self.get_from_list(dkt_dia_chi, "0300", "PHUONG_XA")
                tinh = self.get_from_list(dkt_dia_chi, "0300", "TINH_TP")
                if not dia_chi:
                    dia_chi = info.get("DIA_CHI_TRU_SO_EXT", "").strip()
                full_address = f"{dia_chi}, {xa}, {tinh}".strip(", ")
                return full_address
        except (json.JSONDecodeError, KeyError, IndexError) as e:
            return f"không lấy được địa chỉ (lỗi parse: {e})"
        return "không lấy được địa chỉ"

    def get_from_list(self, lst: List[Dict[str, Any]], loai: str, field: str) -> str:
        for item in lst:
            if item.get("LOAI") == loai:
                return item.get(field, "")
        return ""

class FlippingGIFLabel(tk.Label):
    def __init__(self, master, gif_path, delay=100, flip_interval=2000, size=(32, 32), *args, **kwargs):
        super().__init__(master, *args, **kwargs)
        self.gif = Image.open(gif_path)
        self.frames_right = []
        self.frames_left = []
        try:
            # Lấy hằng số flip đúng kiểu, fallback về None nếu không có
            transpose_flip = getattr(getattr(Image, 'Transpose', Image), 'FLIP_LEFT_RIGHT', None)
            while True:
                frame = self.gif.copy().resize(size, getattr(getattr(Image, 'Resampling', Image), 'LANCZOS', 0))
                self.frames_right.append(ImageTk.PhotoImage(frame))
                if transpose_flip is not None:
                    self.frames_left.append(ImageTk.PhotoImage(frame.transpose(transpose_flip)))
                else:
                    self.frames_left.append(ImageTk.PhotoImage(frame))  # Không flip nếu không hỗ trợ
                self.gif.seek(len(self.frames_right))
        except EOFError:
            pass
        self.idx = 0
        self.delay = delay
        self.flip_interval = flip_interval
        self.flipped = False
        self.after_id = None
        self.flip_after_id = None
        self.animate()
        self.schedule_flip()

    def animate(self):
        frames = self.frames_left if self.flipped else self.frames_right
        if frames:
            self.config(image=frames[self.idx])
            self.idx = (self.idx + 1) % len(frames)
            self.after_id = self.after(self.delay, self.animate)

    def schedule_flip(self):
        self.flipped = not self.flipped
        self.flip_after_id = self.after(self.flip_interval, self.schedule_flip)

    def destroy(self):
        if self.after_id:
            self.after_cancel(self.after_id)
        if self.flip_after_id:
            self.after_cancel(self.flip_after_id)
        super().destroy()

class TraCuuApp:
    MST_COUNT_PREFIX = "📊 Số MST trong bảng: "
    
    def __init__(self, root):
        self.root = root
        # --- Hardcode version và timestamp trực tiếp vào title ---
        self.root.title('Tra cứu thuế v4.865 (2507221410) (TNT)')

        # Khởi tạo các thuộc tính để tránh lỗi linter
        self.update_indices: Optional[List[int]] = None
        self.output_lines_full: Optional[List[str]] = None
        self.output_lines: List[str] = []
        self.danh_sach: List[str] = []
        # Khởi tạo ketqua_file để trống, sẽ được set khi cần
        self.ketqua_file: str = ""
        self.timestamp: str = ""
        self.start_time: Optional[float] = None
        self._timer_final: Optional[int] = None

        clear_temp_folder()  # 🔥 Xóa file tạm khi khởi động

        self.root.protocol("WM_DELETE_WINDOW", self.on_exit)  # Xóa file tạm khi thoát
        
        tk.Label(root, text="📋 Mỗi dòng 1 MST (không chứa khoảng trắng/tab)").pack(pady=5)
        
        # Frame chứa textbox và thanh cuộn
        text_frame = tk.Frame(root)
        text_frame.pack(pady=5, fill=tk.BOTH, expand=True)
        
        # Textbox với thanh cuộn
        self.text_box = tk.Text(text_frame, height=10, width=100)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.text_box.yview)
        self.text_box.configure(yscrollcommand=scrollbar.set)
        
        self.text_box.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind event để cập nhật số lượng MST khi text thay đổi
        self.text_box.bind('<KeyRelease>', self.update_mst_count)
        self.text_box.bind('<ButtonRelease-1>', self.update_mst_count)
        self.text_box.bind('<MouseWheel>', self.update_mst_count)

        frame = tk.Frame(root)
        frame.pack(pady=5)
        tk.Label(frame, text="Số luồng: (có thể thay đổi khi đang chạy, chỉ nhập số nguyên > 0)").pack(side=tk.LEFT)
        self.entry_workers = ttk.Entry(frame, width=5)
        self.entry_workers.insert(0, str(default_workers))
        self.entry_workers.pack(side=tk.LEFT)
        self.entry_workers.bind("<FocusOut>", self.on_workers_change_on_focus_out)
        tk.Label(frame, text="(càng nhiều luồng làm càng nhanh nhưng càng nặng máy)", fg="gray").pack(side=tk.LEFT, padx=10)

        btn_frame = tk.Frame(root)
        btn_frame.pack(pady=5)
        self.btn_start = tk.Button(btn_frame, text="🚀 Bắt đầu tra", command=self.bat_dau)
        self.btn_start.pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="🔁 Làm lại từ kết quả cũ", command=self.lam_lai).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="📁 Mở thư mục", command=self.open_result_folder).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="🐞 Debug", command=self.open_debug_window).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="❌ Thoát", command=self.on_exit).pack(side=tk.LEFT, padx=5)

        # Frame chứa thông tin trạng thái ở giữa màn hình
        status_frame = tk.Frame(root)
        status_frame.pack(pady=5)
        
        # Label hiển thị số lượng MST
        self.mst_count_label = tk.Label(status_frame, text=f"{self.MST_COUNT_PREFIX}0", fg="blue")
        self.mst_count_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # Label trạng thái kết quả
        self.kq_frame = tk.Frame(status_frame)
        self.kq_frame.pack(side=tk.LEFT, padx=(0, 20))
        self.kq_emoji = None  # Sẽ khởi tạo khi cần
        self.label_kq = tk.Label(self.kq_frame, text="", fg="blue", font=("Arial", 12))
        self.label_kq.pack(side=tk.LEFT)
        
        # Label thời gian thực hiện
        self.time_label = tk.Label(status_frame, text="⏱ Thời gian: 00:00:00", fg="purple")
        self.time_label.pack(side=tk.LEFT)
        
        # Label trạng thái chi tiết
        self.status_label = tk.Label(root, text="", fg="green")
        self.status_label.pack(pady=5)

        self.log_box = scrolledtext.ScrolledText(root, height=15, width=100, state="disabled")
        self.log_box.pack(pady=5)

        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        self.total = 0
        self.max_workers = default_workers
        self.active_threads = 0
        self.lock = threading.Lock()
        self.running = False
        self.update_status()
        self.update_time_display()  # Khởi động đồng hồ
        
        # Kiểm tra file setting.ini sau khi log_box đã được khởi tạo
        self.check_setting_file()
        
        # Cập nhật số lượng MST ban đầu
        self.update_mst_count()

    def check_setting_file(self):
        """Kiểm tra và thông báo về file setting.ini"""
        if os.path.exists("setting.ini"):
            try:
                # Thử đọc file để kiểm tra có hợp lệ không
                test_cfg = configparser.ConfigParser()
                test_cfg.read("setting.ini", encoding="utf-8")
                
                # Kiểm tra các giá trị cần thiết
                workers = test_cfg.getint("DEFAULT", "max_workers", fallback=None)
                retry = test_cfg.getint("DEFAULT", "retry_failed", fallback=None)
                timeout = test_cfg.getint("DEFAULT", "request_timeout", fallback=None)
                ocr_timeout = test_cfg.getint("DEFAULT", "ocr_timeout", fallback=None)
                
                if workers is not None and retry is not None and timeout is not None and ocr_timeout is not None:
                    self.log("✅ File setting.ini đã được tìm thấy và hợp lệ")
                    self.log(f"   - Số luồng mặc định: {workers}")
                    self.log(f"   - Số lần thử lại: {retry}")
                    self.log(f"   - Timeout request: {timeout}s")
                    self.log(f"   - Timeout OCR captcha: {ocr_timeout}s")
                else:
                    self.log("⚠️ File setting.ini tồn tại nhưng thiếu một số cấu hình")
                    self.log("   Sử dụng giá trị mặc định")
            except Exception as e:
                self.log(f"⚠️ File setting.ini có lỗi: {e}")
                self.log("   Sử dụng giá trị mặc định")
        else:
            self.log("❌ Không tìm thấy file setting.ini")
            self.log("   Tạo file setting.ini với cấu hình mặc định...")
            self.create_default_setting_file()

    def create_default_setting_file(self):
        """Tạo file setting.ini với cấu hình mặc định"""
        try:
            default_cfg = configparser.ConfigParser()
            default_cfg['DEFAULT'] = {
                'max_workers': '20',
                'retry_failed': '20',
                'request_timeout': '120',
                'ocr_timeout': '120'
            }
            
            with open('setting.ini', 'w', encoding='utf-8') as f:
                default_cfg.write(f)
            
            self.log("✅ Đã tạo file setting.ini với cấu hình mặc định")
        except Exception as e:
            self.log(f"❌ Không thể tạo file setting.ini: {e}")

    def update_mst_count(self, event=None):
        """Cập nhật số lượng MST trong textbox"""
        try:
            content = self.text_box.get("1.0", tk.END).strip()
            if content:
                lines = [line.strip() for line in content.splitlines() if line.strip()]
                count = len(lines)
                self.mst_count_label.config(text=f"{self.MST_COUNT_PREFIX}{count}")
            else:
                self.mst_count_label.config(text=f"{self.MST_COUNT_PREFIX}0")
        except Exception as e:
            self.mst_count_label.config(text=f"{self.MST_COUNT_PREFIX}Lỗi")

    def open_result_folder(self):
        """Mở thư mục gốc chứa source code"""
        try:
            # Mở thư mục gốc (thư mục chứa source code, không phải thư mục build)
            # Nếu đang chạy từ file .py thì mở thư mục chứa file đó
            # Nếu đang chạy từ file build (.exe hoặc .pyd) thì mở thư mục cha
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # Kiểm tra xem có phải đang chạy từ thư mục build không
            if "_internal" in current_dir or "release" in current_dir:
                # Nếu đang ở thư mục build, mở thư mục cha (thư mục gốc)
                parent_dir = os.path.dirname(current_dir)
                if os.path.exists(parent_dir):
                    os.startfile(parent_dir)
                    self.log(f"📁 Đã mở thư mục gốc: {parent_dir}")
                else:
                    messagebox.showwarning("Thông báo", "Thư mục gốc không tồn tại.")
            else:
                # Nếu đang ở thư mục gốc, mở thư mục hiện tại
                if os.path.exists(current_dir):
                    os.startfile(current_dir)
                    self.log(f"📁 Đã mở thư mục hiện tại: {current_dir}")
                else:
                    messagebox.showwarning("Thông báo", "Thư mục hiện tại không tồn tại.")
        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("Lỗi", f"Không thể mở thư mục:\n{e}")
            self.log(f"❌ Lỗi mở thư mục: {e}")

    def on_exit(self):
        clear_temp_folder()
        self.root.destroy()

    def on_workers_change_on_focus_out(self, event=None):
        """Cập nhật số luồng khi ô nhập mất focus"""
        value = self.entry_workers.get().strip()
        if value.isdigit() and int(value) > 0:
            new_workers = int(value)
            if new_workers != self.max_workers:  # Chỉ xác nhận khi thực sự thay đổi
                # Hiển thị dialog xác nhận
                if messagebox.askyesno("Xác nhận", f"Bạn có muốn cập nhật số luồng từ {self.max_workers} thành {new_workers} không?"):
                    self.max_workers = new_workers
                    self.log(f"🔧 Đã cập nhật số luồng: {self.max_workers}")
                    self.update_status()
                else:
                    # Người dùng không đồng ý, trở về giá trị cũ
                    self.entry_workers.delete(0, tk.END)
                    self.entry_workers.insert(0, str(self.max_workers))
        else:
            # Nhập sai, trở về giá trị cũ
            self.entry_workers.delete(0, tk.END)
            self.entry_workers.insert(0, str(self.max_workers))

    def update_status(self):
        captcha_total = self.captcha_ok + self.captcha_fail
        if captcha_total > 0:
            percent = self.captcha_ok * 100 / captcha_total
            captcha_info = f"🧩 Captcha đúng: {self.captcha_ok}/{captcha_total} ({percent:.1f}%)"
        else:
            captcha_info = "🧩 Captcha đúng: 0/0"
        self.status_label.config(
            text=f"✔ Thành công: {self.ok}   ❌ Thất bại: {self.fail}   {captcha_info}   🧠 Đã xử lý: {self.done} / {self.total}   🧵 Luồng: {self.active_threads}/{self.max_workers}"
        )

    # Các hàm tăng/giảm biến đếm để gọi từ thread phụ
    def _inc_active_threads(self):
        self.active_threads += 1
        # Log để debug
        self.log(f"🔧 Số luồng đang hoạt động tăng: {self.active_threads - 1} → {self.active_threads}")
    def _dec_active_threads(self):
        self.active_threads -= 1
        # Log để debug
        self.log(f"🔧 Số luồng đang hoạt động giảm: {self.active_threads + 1} → {self.active_threads}")
    def _inc_ok(self):
        self.ok += 1
    def _inc_fail(self):
        self.fail += 1
    def _inc_done(self):
        self.done += 1

    def log(self, msg: str):
        self.log_box.config(state="normal")
        self.log_box.insert(tk.END, msg + "\n")
        self.log_box.see(tk.END)
        self.log_box.config(state="disabled")

    def update_line_and_file(self, idx: int, new_text: str):
        if self.update_indices is not None and self.output_lines_full is not None:
            if idx < len(self.update_indices):
                real_idx = self.update_indices[idx]
                if real_idx < len(self.output_lines_full):
                    self.output_lines_full[real_idx] = new_text
        else:
            if idx >= len(self.output_lines):
                self.output_lines.extend([""] * (idx + 1 - len(self.output_lines)))
            self.output_lines[idx] = new_text
        self.log(new_text)
        self.write_result_file()

    def write_result_file(self):
        try:
            with open(self.ketqua_file, "w", encoding="utf-8") as f:
                if self.output_lines_full is not None:
                    f.write("\n".join(self.output_lines_full))
                else:
                    f.write("\n".join(line for line in self.output_lines if line))
        except Exception as e:
            self.log("⚠ Lỗi ghi file kết quả:")
            self.log(traceback.format_exc())
            try:
                messagebox.showerror("Lỗi ghi file", f"Không thể ghi file kết quả: {e}\nVui lòng kiểm tra dung lượng ổ đĩa hoặc quyền ghi file.")
            except Exception:
                pass

    def start_kq_loading_anim(self):
        self.kq_loading_dots = 0
        self._kq_loading_anim()

    def _kq_loading_anim(self):
        dots = '.' * (self.kq_loading_dots + 1)
        spaces = ' ' * (2 - self.kq_loading_dots)
        self.label_kq.config(text=f'Đang chạy {dots}{spaces}')
        self.kq_loading_dots = (self.kq_loading_dots + 1) % 3
        self.kq_loading_anim_id = self.root.after(400, self._kq_loading_anim)

    def stop_kq_loading_anim(self):
        if hasattr(self, 'kq_loading_anim_id') and self.kq_loading_anim_id:
            self.root.after_cancel(self.kq_loading_anim_id)
            self.kq_loading_anim_id = None
        self.label_kq.config(text="")

    def show_kq_loading(self):
        if self.kq_emoji is not None:
            return
        gif_path = "resource/working.gif"
        if os.path.exists(gif_path):
            self.kq_emoji = FlippingGIFLabel(self.kq_frame, gif_path, delay=80, flip_interval=5000, size=(32, 32))
            self.kq_emoji.pack(side=tk.LEFT, before=self.label_kq)
        else:
            # Fallback: chỉ dùng emoji text động (không màu)
            self.kq_emoji = tk.Label(self.kq_frame, text="🧍🏻", font=("Arial", 16))
            self.kq_emoji.pack(side=tk.LEFT, before=self.label_kq)
            self._kq_loading_emojis = ["🧍🏻","🚶🏻","🏃🏻","🏃🏻","🚶🏻","🧍🏻"]
            self._kq_loading_emoji_idx = 0
            def animate_emoji():
                self.kq_emoji.config(text=self._kq_loading_emojis[self._kq_loading_emoji_idx])
                self._kq_loading_emoji_idx = (self._kq_loading_emoji_idx + 1) % len(self._kq_loading_emojis)
                self._kq_loading_emoji_anim_id = self.root.after(300, animate_emoji)
            animate_emoji()
        self.start_kq_loading_anim()

    def hide_kq_loading(self):
        if self.kq_emoji is not None:
            if hasattr(self, '_kq_loading_emoji_anim_id'):
                self.root.after_cancel(self._kq_loading_emoji_anim_id)
                del self._kq_loading_emoji_anim_id
            self.kq_emoji.destroy()
            self.kq_emoji = None
        self.stop_kq_loading_anim()

    def show_kq_done(self):
        if self.kq_emoji is not None:
            if hasattr(self, '_kq_loading_emoji_anim_id'):
                self.root.after_cancel(self._kq_loading_emoji_anim_id)
                del self._kq_loading_emoji_anim_id
            self.kq_emoji.destroy()
            self.kq_emoji = None
        self.stop_kq_loading_anim()
        # Emoji tĩnh 🎉 (không màu)
        self.kq_emoji = tk.Label(self.kq_frame, text="🎉", font=("Arial", 16))
        self.kq_emoji.pack(side=tk.LEFT, before=self.label_kq)
        self.label_kq.config(text=" Đã xong hết rồi!")

    def bat_dau(self):
        self.update_indices = None
        self.output_lines_full = None

        # Reset trạng thái label_kq và emoji về mặc định
        if self.kq_emoji is not None:
            self.kq_emoji.destroy()
            self.kq_emoji = None
        self.label_kq.config(text="")

        try:
            self.max_workers = int(self.entry_workers.get())
            if self.max_workers <= 0:
                raise ValueError
        except ValueError:
            self.label_kq.config(text="❌ Số luồng không hợp lệ (phải là số nguyên > 0).")
            return

        raw = self.text_box.get("1.0", tk.END).splitlines()
        self.danh_sach = [line.strip() for line in raw if line.strip()]
        if not self.danh_sach:
            self.label_kq.config(text="❌ Không có MST nào.")
            return

        for idx, mst in enumerate(self.danh_sach, 1):
            if " " in mst or "\t" in mst:
                self.label_kq.config(text=f"❌ MST dòng {idx} chứa khoảng trắng/tab: [{mst}]")
                return

        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        self.total = len(self.danh_sach)
        self.output_lines = []
        self.update_status()

        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.ketqua_file = f"ketqua_{self.timestamp}.txt"
        self.show_kq_loading()
        self.btn_start.config(state=tk.DISABLED)
        self.log(f"Bắt đầu tra cứu {self.total} MST...")

        self.start_time = time.time()  # Reset timer
        self.running = True
        self._timer_final = None  # Reset timer final value
        self.update_time_display()

        threading.Thread(target=self.run_all, daemon=True).start()

    def run_all(self):
        # Không dùng ThreadPoolExecutor, tự quản lý threads
        futures = []
        i = 0
        
        while i < len(self.danh_sach):
            # Kiểm tra số luồng hiện tại trước khi tạo luồng mới
            with self.lock:
                current_workers = self.max_workers  # Lấy giá trị hiện tại
                if self.active_threads >= current_workers:
                    # Đợi một chút nếu đã đạt giới hạn luồng
                    time.sleep(0.1)
                    continue
                else:
                    self.log(f"✅ Có thể tạo luồng mới ({self.active_threads}/{current_workers})")
            
            worker = TraCuuWorker(self.danh_sach[i], i, self)
            thread = threading.Thread(target=worker.run, daemon=True)
            thread.start()
            futures.append(thread)
            i += 1
            
        # Đợi tất cả threads hoàn thành
        for thread in futures:
            thread.join()

        # Gọi các hàm UI trên main thread
        self.root.after(0, self._finish_processing)

    def _finish_processing(self):
        """Hoàn thành xử lý - gọi trên main thread"""
        self.log("🎯 Hoàn thành xử lý tất cả MST")
        self.hide_kq_loading()
        self.show_kq_done()
        self.btn_start.config(state=tk.NORMAL)
        self.running = False  # Kết thúc đếm thời gian
        self._timer_final = int(time.time() - self.start_time) if self.start_time else None
        
        # Kiểm tra có dòng nào không lấy được địa chỉ không
        has_failed = False
        failed_lines = []
        if self.output_lines_full is not None:
            for line in self.output_lines_full:
                if "không lấy được địa chỉ" in line.lower():
                    has_failed = True
                    failed_lines.append(line)
        else:
            for line in self.output_lines:
                if "không lấy được địa chỉ" in line.lower():
                    has_failed = True
                    failed_lines.append(line)
        
        # Hiển thị messagebox hoàn thành
        if has_failed:
            from tkinter import messagebox
            if messagebox.askyesno("Hoàn thành", "🎉 Đã xong hết rồi!\nCó kết quả không lấy được địa chỉ.\nBạn có muốn làm lại ngay từ file kết quả này không?"):
                # Tự động gọi lại lam_lai với file kết quả vừa tạo
                self._auto_lamlai_from_last_result()
                return
        from tkinter import messagebox
        if messagebox.askyesno("Hoàn thành", "🎉 Đã xong hết rồi!\nBạn có muốn mở file kết quả không?"):
            try:
                import subprocess
                subprocess.Popen(["notepad", self.ketqua_file])
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không mở được file:\n{e}")

    def _auto_lamlai_from_last_result(self):
        # Hàm này mô phỏng thao tác làm lại từ file kết quả vừa tạo
        path = self.ketqua_file
        if not os.path.exists(path):
            messagebox.showerror("Lỗi", "File kết quả không tồn tại!")
            return
        try:
            with open(path, "r", encoding="utf-8") as f:
                lines = f.readlines()
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không đọc được file: {e}")
            return
        self.output_lines_full = [line.strip() for line in lines]
        danh_sach, update_indices = [], []
        for idx, line in enumerate(self.output_lines_full):
            if "không lấy được địa chỉ" in line:
                parts = line.strip().split("\t")
                if len(parts) >= 2:
                    danh_sach.append(parts[1])
                    update_indices.append(idx)
        if not danh_sach:
            messagebox.showinfo("Không có dữ liệu", "Không có MST nào phù hợp để tra lại.")
            return
        # Reset trạng thái label_kq và emoji về mặc định
        if self.kq_emoji is not None:
            self.kq_emoji.destroy()
            self.kq_emoji = None
        self.label_kq.config(text="")
        self.danh_sach = danh_sach
        self.update_indices = update_indices
        self.total = len(danh_sach)
        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name, ext = os.path.splitext(os.path.basename(path))
        dir_name = os.path.dirname(path)
        self.ketqua_file = os.path.join(dir_name, f"{base_name}_lamlai_{timestamp}{ext}")
        self.output_lines = ["" for _ in danh_sach]
        self.write_result_file()
        try:
            self.max_workers = int(self.entry_workers.get())
            if self.max_workers <= 0:
                raise ValueError
        except ValueError:
            self.max_workers = default_workers
        self.show_kq_loading()
        self.btn_start.config(state=tk.DISABLED)
        self.update_status()
        self.log(f"🔁 Bắt đầu tra lại từ file: {path} ({len(danh_sach)} MST)")

        self.start_time = time.time()
        self.running = True
        self.update_time_display()

        threading.Thread(target=self.run_all, daemon=True).start()

    def lam_lai(self):
        if hasattr(self, 'lam_lai_window') and self.lam_lai_window.winfo_exists():
            self.lam_lai_window.lift()
            return

        self.lam_lai_window = tk.Toplevel(self.root)
        self.lam_lai_window.title("🔁 Làm lại từ kết quả cũ")
        self.lam_lai_window.transient(self.root)
        self.lam_lai_window.grab_set()

        x, y = self.root.winfo_x(), self.root.winfo_y()
        self.lam_lai_window.geometry(f"+{x}+{y}")

        tk.Label(self.lam_lai_window, text="Chọn file kết quả cũ (.txt):").pack(padx=10, pady=5)
        path_var = tk.StringVar()
        tk.Entry(self.lam_lai_window, textvariable=path_var, width=60).pack(padx=10, pady=5)
        tk.Button(self.lam_lai_window, text="Chọn file", command=lambda: path_var.set(
            filedialog.askopenfilename(filetypes=[("Text files", "*.txt")]))
        ).pack(pady=5)

        retry_pending = tk.BooleanVar(value=True)
        retry_failed = tk.BooleanVar(value=True)
        tk.Checkbutton(self.lam_lai_window, text="Tra cứu lại các kết quả Đang kiểm tra", variable=retry_pending).pack(anchor='w', padx=20)
        tk.Checkbutton(self.lam_lai_window, text="Tra cứu lại các kết quả không lấy được địa chỉ", variable=retry_failed).pack(anchor='w', padx=20)
        tk.Label(self.lam_lai_window, text="➡️ Chức năng này cho phép tra lại những dòng chưa có kết quả.").pack(pady=5)

        def run_retry():
            path = path_var.get()
            if not os.path.exists(path):
                messagebox.showerror("Lỗi", "File không tồn tại")
                return

            try:
                with open(path, "r", encoding="utf-8") as f:
                    lines = f.readlines()
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không đọc được file: {e}")
                return

            self.output_lines_full = [line.strip() for line in lines]
            danh_sach, update_indices = [], []

            for idx, line in enumerate(self.output_lines_full):
                if retry_pending.get() and "Đang kiểm tra" in line:
                    parts = line.strip().split("\t")
                    if len(parts) >= 2:
                        danh_sach.append(parts[1])
                        update_indices.append(idx)
                elif retry_failed.get() and "không lấy được địa chỉ" in line:
                    parts = line.strip().split("\t")
                    if len(parts) >= 2:
                        danh_sach.append(parts[1])
                        update_indices.append(idx)

            if not danh_sach:
                messagebox.showinfo("Không có dữ liệu", "Không có MST nào phù hợp để tra lại.")
                return

            # Reset trạng thái label_kq và emoji về mặc định
            if self.kq_emoji is not None:
                self.kq_emoji.destroy()
                self.kq_emoji = None
            self.label_kq.config(text="")

            self.danh_sach = danh_sach
            self.update_indices = update_indices
            self.total = len(danh_sach)
            self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name, ext = os.path.splitext(os.path.basename(path))
            dir_name = os.path.dirname(path)
            self.ketqua_file = os.path.join(dir_name, f"{base_name}_lamlai_{timestamp}{ext}")
            self.output_lines = ["" for _ in danh_sach]
            self.write_result_file()

            try:
                self.max_workers = int(self.entry_workers.get())
                if self.max_workers <= 0:
                    raise ValueError
            except ValueError:
                self.max_workers = default_workers

            self.show_kq_loading()
            self.btn_start.config(state=tk.DISABLED)
            self.update_status()
            self.log(f"🔁 Bắt đầu tra lại từ file: {path} ({len(danh_sach)} MST)")

            self.start_time = time.time()
            self.running = True
            self.update_time_display()

            threading.Thread(target=self.run_all, daemon=True).start()
            self.lam_lai_window.destroy()

        # Đảm bảo luôn có nút bắt đầu
        tk.Button(self.lam_lai_window, text="🚀 Bắt đầu", command=run_retry).pack(pady=10)

    def update_time_display(self):
        if self.start_time and self.running:
            elapsed = int(time.time() - self.start_time)
            hours = elapsed // 3600
            minutes = (elapsed % 3600) // 60
            seconds = elapsed % 60
            self.time_label.config(text=f"⏱ {hours:02d}:{minutes:02d}:{seconds:02d}")
            self.root.after(1000, self.update_time_display)
        elif not self.running and self.start_time:
            # Hiển thị thời gian cuối cùng khi xong
            elapsed = self._timer_final if self._timer_final is not None else int(time.time() - self.start_time)
            hours = elapsed // 3600
            minutes = (elapsed % 3600) // 60
            seconds = elapsed % 60
            self.time_label.config(text=f"⏱ {hours:02d}:{minutes:02d}:{seconds:02d} ✓")
            # Không gọi lại self.root.after nữa để ngừng đếm

    def open_debug_window(self):
        if hasattr(self, 'debug_window') and self.debug_window.winfo_exists():
            self.debug_window.lift()
            return
        self.debug_window = tk.Toplevel(self.root)
        self.debug_window.title("Debug Tools")
        self.debug_window.geometry("400x250")
        self.debug_window.grab_set()
        tk.Label(self.debug_window, text="Tính năng này dành cho việc kiểm tra lỗi, trong ngữ cảnh bình thường vui lòng không sử dụng", fg="red", wraplength=380, justify="center").pack(pady=10)
        self.debug_var_save = tk.BooleanVar(value=getattr(self, 'debug_enable_save', False))
        self.debug_var_wrong = tk.BooleanVar(value=getattr(self, 'debug_enable_wrong', False))
        self.debug_var_always_fail = tk.BooleanVar(value=getattr(self, 'debug_always_fail', False))
        tk.Checkbutton(self.debug_window, text="Lưu file trả về (raw HTML/json)", variable=self.debug_var_save).pack(anchor='w', padx=40)
        tk.Checkbutton(self.debug_window, text="Cố ý sai captcha", variable=self.debug_var_wrong).pack(anchor='w', padx=40)
        tk.Checkbutton(self.debug_window, text="Kết quả luôn là không tìm được địa chỉ", variable=self.debug_var_always_fail).pack(anchor='w', padx=40)
        # Thay nút "Thực hiện debug" bằng 2 nút: Lưu và Đóng
        btn_frame = tk.Frame(self.debug_window)
        btn_frame.pack(pady=10)
        tk.Button(btn_frame, text="Lưu", command=self.debug_do_action).pack(side=tk.LEFT, padx=10)
        tk.Button(btn_frame, text="Đóng", command=self.debug_window.destroy).pack(side=tk.LEFT, padx=10)

    def debug_do_action(self):
        self.debug_enable_save = self.debug_var_save.get()
        self.debug_enable_wrong = self.debug_var_wrong.get()
        self.debug_always_fail = self.debug_var_always_fail.get()
        if not self.debug_enable_save and not self.debug_enable_wrong and not self.debug_always_fail:
            messagebox.showinfo("Debug", "Chưa chọn chức năng nào!")
        else:
            messagebox.showinfo("Debug", "Chế độ debug đã được lưu cho lần tra cứu tiếp theo!")
        # Đóng cửa sổ sau khi lưu
        if hasattr(self, 'debug_window') and self.debug_window.winfo_exists():
            self.debug_window.destroy()

    def debug_save_raw_response(self):
        # Lưu file trả về gần nhất (nếu có) vào folder debug
        if not hasattr(self, 'last_raw_response') or not self.last_raw_response:
            messagebox.showinfo("Debug", "Chưa có dữ liệu trả về để lưu!")
            return
        os.makedirs("debug", exist_ok=True)
        ts = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join("debug", f"debug_raw_{ts}.txt")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(self.last_raw_response)
        messagebox.showinfo("Debug", f"Đã lưu file debug: {file_path}")

    def debug_wrong_captcha(self):
        # Đặt cờ để lần tra cứu tiếp theo sẽ cố ý sai captcha
        self.debug_enable_wrong = True
        messagebox.showinfo("Debug", "Lần tra cứu tiếp theo sẽ cố ý sai captcha!")

def center_window(window, width=None, height=None):
    window.update_idletasks()
    w = width or window.winfo_width()
    h = height or window.winfo_height()
    x = (window.winfo_screenwidth() - w) // 2
    y = (window.winfo_screenheight() - h) // 2
    window.geometry(f"{w}x{h}+{x}+{y}")

def main():
    root = tk.Tk()
    # Xóa đoạn đặt font mặc định, trả lại font Tkinter gốc
    clear_temp_folder()  # 🔥 Xóa file tạm trước khi hiển thị gì
    app = TraCuuApp(root)  # Khởi tạo App ngay từ đầu
    app.root.withdraw()    # Ẩn giao diện chính
    def on_loaded():
        center_window(app.root)
        app.root.deiconify()  # Hiện giao diện khi tải model xong
    loading = LoadingModelWindow(root)
    loading.top.bind("<Destroy>", lambda e: on_loaded())
    root.mainloop()

if __name__ == "__main__":
    main()
