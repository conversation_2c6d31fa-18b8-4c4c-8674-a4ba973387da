{"best_metric": 0.12520700693130493, "best_model_checkpoint": "finetuned_model\\checkpoint-15500", "epoch": 9.4859241126071, "eval_steps": 500, "global_step": 15500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.06119951040391677, "grad_norm": 0.9122058153152466, "learning_rate": 9.7e-06, "loss": 4.0608, "step": 100}, {"epoch": 0.12239902080783353, "grad_norm": 0.4548240005970001, "learning_rate": 1.97e-05, "loss": 0.143, "step": 200}, {"epoch": 0.1835985312117503, "grad_norm": 0.5197239518165588, "learning_rate": 2.97e-05, "loss": 0.139, "step": 300}, {"epoch": 0.24479804161566707, "grad_norm": 0.3684103786945343, "learning_rate": 3.97e-05, "loss": 0.138, "step": 400}, {"epoch": 0.30599755201958384, "grad_norm": 0.3690888583660126, "learning_rate": 4.97e-05, "loss": 0.1722, "step": 500}, {"epoch": 0.30599755201958384, "eval_loss": 0.1428232491016388, "eval_runtime": 148.8012, "eval_samples_per_second": 21.962, "eval_steps_per_second": 2.749, "step": 500}, {"epoch": 0.3671970624235006, "grad_norm": 0.40016674995422363, "learning_rate": 4.969381313131313e-05, "loss": 0.1411, "step": 600}, {"epoch": 0.4283965728274174, "grad_norm": 0.5059754252433777, "learning_rate": 4.937815656565657e-05, "loss": 0.1386, "step": 700}, {"epoch": 0.48959608323133413, "grad_norm": 1.7452431917190552, "learning_rate": 4.90625e-05, "loss": 0.137, "step": 800}, {"epoch": 0.5507955936352509, "grad_norm": 0.3559176027774811, "learning_rate": 4.8746843434343435e-05, "loss": 0.1574, "step": 900}, {"epoch": 0.6119951040391677, "grad_norm": 0.43953773379325867, "learning_rate": 4.843118686868687e-05, "loss": 0.1356, "step": 1000}, {"epoch": 0.6119951040391677, "eval_loss": 0.1318066418170929, "eval_runtime": 132.8192, "eval_samples_per_second": 24.605, "eval_steps_per_second": 3.079, "step": 1000}, {"epoch": 0.6731946144430845, "grad_norm": 0.33815911412239075, "learning_rate": 4.8115530303030305e-05, "loss": 0.1339, "step": 1100}, {"epoch": 0.7343941248470012, "grad_norm": 0.5463588237762451, "learning_rate": 4.779987373737374e-05, "loss": 0.1335, "step": 1200}, {"epoch": 0.795593635250918, "grad_norm": 0.37059488892555237, "learning_rate": 4.748737373737374e-05, "loss": 0.2916, "step": 1300}, {"epoch": 0.8567931456548348, "grad_norm": 0.3490618169307709, "learning_rate": 4.7171717171717174e-05, "loss": 0.1328, "step": 1400}, {"epoch": 0.9179926560587516, "grad_norm": 0.5948259830474854, "learning_rate": 4.685606060606061e-05, "loss": 0.1578, "step": 1500}, {"epoch": 0.9179926560587516, "eval_loss": 0.13178099691867828, "eval_runtime": 135.982, "eval_samples_per_second": 24.033, "eval_steps_per_second": 3.008, "step": 1500}, {"epoch": 0.9791921664626683, "grad_norm": 0.33345192670822144, "learning_rate": 4.6540404040404044e-05, "loss": 0.1329, "step": 1600}, {"epoch": 1.040391676866585, "grad_norm": 0.2715766131877899, "learning_rate": 4.622474747474748e-05, "loss": 0.131, "step": 1700}, {"epoch": 1.1015911872705018, "grad_norm": 0.24832375347614288, "learning_rate": 4.5909090909090914e-05, "loss": 0.1327, "step": 1800}, {"epoch": 1.1627906976744187, "grad_norm": 0.27526211738586426, "learning_rate": 4.559343434343434e-05, "loss": 0.1303, "step": 1900}, {"epoch": 1.2239902080783354, "grad_norm": 0.2959044575691223, "learning_rate": 4.527777777777778e-05, "loss": 0.1311, "step": 2000}, {"epoch": 1.2239902080783354, "eval_loss": 0.12902431190013885, "eval_runtime": 134.2823, "eval_samples_per_second": 24.337, "eval_steps_per_second": 3.046, "step": 2000}, {"epoch": 1.2851897184822523, "grad_norm": 0.25129276514053345, "learning_rate": 4.496212121212122e-05, "loss": 0.1305, "step": 2100}, {"epoch": 1.346389228886169, "grad_norm": 0.3423830270767212, "learning_rate": 4.464646464646465e-05, "loss": 0.1324, "step": 2200}, {"epoch": 1.4075887392900857, "grad_norm": 0.3664688766002655, "learning_rate": 4.433080808080808e-05, "loss": 0.1322, "step": 2300}, {"epoch": 1.4687882496940023, "grad_norm": 0.323308527469635, "learning_rate": 4.401515151515152e-05, "loss": 0.1313, "step": 2400}, {"epoch": 1.5299877600979193, "grad_norm": 0.2795449197292328, "learning_rate": 4.369949494949495e-05, "loss": 0.1306, "step": 2500}, {"epoch": 1.5299877600979193, "eval_loss": 0.12857797741889954, "eval_runtime": 132.4227, "eval_samples_per_second": 24.679, "eval_steps_per_second": 3.089, "step": 2500}, {"epoch": 1.591187270501836, "grad_norm": 0.2790302634239197, "learning_rate": 4.338383838383839e-05, "loss": 0.1307, "step": 2600}, {"epoch": 1.6523867809057529, "grad_norm": 0.3374452292919159, "learning_rate": 4.3068181818181816e-05, "loss": 0.1297, "step": 2700}, {"epoch": 1.7135862913096696, "grad_norm": 0.2663882076740265, "learning_rate": 4.275252525252526e-05, "loss": 0.1308, "step": 2800}, {"epoch": 1.7747858017135862, "grad_norm": 0.23108506202697754, "learning_rate": 4.243686868686869e-05, "loss": 0.1305, "step": 2900}, {"epoch": 1.835985312117503, "grad_norm": 0.3097258508205414, "learning_rate": 4.212121212121212e-05, "loss": 0.1308, "step": 3000}, {"epoch": 1.835985312117503, "eval_loss": 0.1302657574415207, "eval_runtime": 132.5528, "eval_samples_per_second": 24.654, "eval_steps_per_second": 3.086, "step": 3000}, {"epoch": 1.8971848225214198, "grad_norm": 0.309317946434021, "learning_rate": 4.1805555555555556e-05, "loss": 0.1321, "step": 3100}, {"epoch": 1.9583843329253368, "grad_norm": 0.30747222900390625, "learning_rate": 4.148989898989899e-05, "loss": 0.1311, "step": 3200}, {"epoch": 2.0195838433292534, "grad_norm": 0.7360403537750244, "learning_rate": 4.1174242424242426e-05, "loss": 0.1304, "step": 3300}, {"epoch": 2.08078335373317, "grad_norm": 0.2750777304172516, "learning_rate": 4.085858585858586e-05, "loss": 0.1296, "step": 3400}, {"epoch": 2.141982864137087, "grad_norm": 0.33391323685646057, "learning_rate": 4.0542929292929296e-05, "loss": 0.1292, "step": 3500}, {"epoch": 2.141982864137087, "eval_loss": 0.12941095232963562, "eval_runtime": 141.7518, "eval_samples_per_second": 23.054, "eval_steps_per_second": 2.885, "step": 3500}, {"epoch": 2.2031823745410035, "grad_norm": 0.2665056884288788, "learning_rate": 4.022727272727273e-05, "loss": 0.129, "step": 3600}, {"epoch": 2.26438188494492, "grad_norm": 0.2675129175186157, "learning_rate": 3.991161616161616e-05, "loss": 0.1288, "step": 3700}, {"epoch": 2.3255813953488373, "grad_norm": 0.23957771062850952, "learning_rate": 3.9595959595959594e-05, "loss": 0.1292, "step": 3800}, {"epoch": 2.386780905752754, "grad_norm": 0.2920595705509186, "learning_rate": 3.9280303030303036e-05, "loss": 0.1289, "step": 3900}, {"epoch": 2.4479804161566707, "grad_norm": 0.26439011096954346, "learning_rate": 3.8964646464646464e-05, "loss": 0.128, "step": 4000}, {"epoch": 2.4479804161566707, "eval_loss": 0.13041216135025024, "eval_runtime": 134.4176, "eval_samples_per_second": 24.312, "eval_steps_per_second": 3.043, "step": 4000}, {"epoch": 2.5091799265605874, "grad_norm": 0.27107861638069153, "learning_rate": 3.865530303030303e-05, "loss": 0.3557, "step": 4100}, {"epoch": 2.5703794369645045, "grad_norm": 0.3066437244415283, "learning_rate": 3.833964646464647e-05, "loss": 0.1343, "step": 4200}, {"epoch": 2.6315789473684212, "grad_norm": 0.2865728735923767, "learning_rate": 3.8023989898989905e-05, "loss": 0.1333, "step": 4300}, {"epoch": 2.692778457772338, "grad_norm": 0.30439066886901855, "learning_rate": 3.770833333333333e-05, "loss": 0.1323, "step": 4400}, {"epoch": 2.7539779681762546, "grad_norm": 0.29614922404289246, "learning_rate": 3.739267676767677e-05, "loss": 0.1295, "step": 4500}, {"epoch": 2.7539779681762546, "eval_loss": 0.1276780217885971, "eval_runtime": 134.5491, "eval_samples_per_second": 24.289, "eval_steps_per_second": 3.04, "step": 4500}, {"epoch": 2.8151774785801713, "grad_norm": 0.2335578054189682, "learning_rate": 3.70770202020202e-05, "loss": 0.1301, "step": 4600}, {"epoch": 2.876376988984088, "grad_norm": 0.2218065708875656, "learning_rate": 3.676136363636364e-05, "loss": 0.1286, "step": 4700}, {"epoch": 2.9375764993880047, "grad_norm": 0.2371879518032074, "learning_rate": 3.644570707070707e-05, "loss": 0.1296, "step": 4800}, {"epoch": 2.998776009791922, "grad_norm": 0.25928419828414917, "learning_rate": 3.613005050505051e-05, "loss": 0.1384, "step": 4900}, {"epoch": 3.0599755201958385, "grad_norm": 0.3098906874656677, "learning_rate": 3.581439393939394e-05, "loss": 0.1317, "step": 5000}, {"epoch": 3.0599755201958385, "eval_loss": 0.12892764806747437, "eval_runtime": 137.2834, "eval_samples_per_second": 23.805, "eval_steps_per_second": 2.979, "step": 5000}, {"epoch": 3.121175030599755, "grad_norm": 0.8480717539787292, "learning_rate": 3.549873737373737e-05, "loss": 0.1305, "step": 5100}, {"epoch": 3.182374541003672, "grad_norm": 0.2520119845867157, "learning_rate": 3.5183080808080806e-05, "loss": 0.1285, "step": 5200}, {"epoch": 3.2435740514075886, "grad_norm": 0.2614128291606903, "learning_rate": 3.486742424242425e-05, "loss": 0.1281, "step": 5300}, {"epoch": 3.3047735618115057, "grad_norm": 0.2519570589065552, "learning_rate": 3.4551767676767676e-05, "loss": 0.1287, "step": 5400}, {"epoch": 3.3659730722154224, "grad_norm": 0.2697165310382843, "learning_rate": 3.423611111111111e-05, "loss": 0.1279, "step": 5500}, {"epoch": 3.3659730722154224, "eval_loss": 0.13015474379062653, "eval_runtime": 143.6896, "eval_samples_per_second": 22.743, "eval_steps_per_second": 2.846, "step": 5500}, {"epoch": 3.427172582619339, "grad_norm": 0.26382172107696533, "learning_rate": 3.3920454545454546e-05, "loss": 0.1281, "step": 5600}, {"epoch": 3.488372093023256, "grad_norm": 0.21070098876953125, "learning_rate": 3.360479797979798e-05, "loss": 0.1272, "step": 5700}, {"epoch": 3.5495716034271725, "grad_norm": 0.23340322077274323, "learning_rate": 3.3289141414141416e-05, "loss": 0.1267, "step": 5800}, {"epoch": 3.610771113831089, "grad_norm": 0.24593128263950348, "learning_rate": 3.2973484848484845e-05, "loss": 0.1294, "step": 5900}, {"epoch": 3.671970624235006, "grad_norm": 0.26511523127555847, "learning_rate": 3.2657828282828286e-05, "loss": 0.1295, "step": 6000}, {"epoch": 3.671970624235006, "eval_loss": 0.13048012554645538, "eval_runtime": 141.6918, "eval_samples_per_second": 23.064, "eval_steps_per_second": 2.887, "step": 6000}, {"epoch": 3.733170134638923, "grad_norm": 0.25356873869895935, "learning_rate": 3.234217171717172e-05, "loss": 0.1281, "step": 6100}, {"epoch": 3.7943696450428397, "grad_norm": 0.24507039785385132, "learning_rate": 3.202651515151515e-05, "loss": 0.1289, "step": 6200}, {"epoch": 3.8555691554467564, "grad_norm": 0.23811763525009155, "learning_rate": 3.171085858585859e-05, "loss": 0.1278, "step": 6300}, {"epoch": 3.916768665850673, "grad_norm": 0.24671797454357147, "learning_rate": 3.139520202020202e-05, "loss": 0.1274, "step": 6400}, {"epoch": 3.97796817625459, "grad_norm": 0.23955537378787994, "learning_rate": 3.1079545454545455e-05, "loss": 0.1277, "step": 6500}, {"epoch": 3.97796817625459, "eval_loss": 0.12736409902572632, "eval_runtime": 133.929, "eval_samples_per_second": 24.401, "eval_steps_per_second": 3.054, "step": 6500}, {"epoch": 4.039167686658507, "grad_norm": 0.23539991676807404, "learning_rate": 3.076388888888889e-05, "loss": 0.1278, "step": 6600}, {"epoch": 4.100367197062424, "grad_norm": 0.24202801287174225, "learning_rate": 3.044823232323232e-05, "loss": 0.1735, "step": 6700}, {"epoch": 4.16156670746634, "grad_norm": 0.29010024666786194, "learning_rate": 3.013257575757576e-05, "loss": 0.128, "step": 6800}, {"epoch": 4.222766217870257, "grad_norm": 0.24137403070926666, "learning_rate": 2.9816919191919195e-05, "loss": 0.1273, "step": 6900}, {"epoch": 4.283965728274174, "grad_norm": 0.22378841042518616, "learning_rate": 2.9501262626262626e-05, "loss": 0.1271, "step": 7000}, {"epoch": 4.283965728274174, "eval_loss": 0.12743398547172546, "eval_runtime": 142.7009, "eval_samples_per_second": 22.901, "eval_steps_per_second": 2.866, "step": 7000}, {"epoch": 4.34516523867809, "grad_norm": 0.27847349643707275, "learning_rate": 2.9185606060606065e-05, "loss": 0.1265, "step": 7100}, {"epoch": 4.406364749082007, "grad_norm": 0.22828105092048645, "learning_rate": 2.8869949494949493e-05, "loss": 0.1266, "step": 7200}, {"epoch": 4.467564259485924, "grad_norm": 0.23773297667503357, "learning_rate": 2.855429292929293e-05, "loss": 0.1265, "step": 7300}, {"epoch": 4.52876376988984, "grad_norm": 0.21544882655143738, "learning_rate": 2.8238636363636367e-05, "loss": 0.1265, "step": 7400}, {"epoch": 4.589963280293758, "grad_norm": 0.2472406029701233, "learning_rate": 2.7922979797979798e-05, "loss": 0.1273, "step": 7500}, {"epoch": 4.589963280293758, "eval_loss": 0.1265777051448822, "eval_runtime": 135.7432, "eval_samples_per_second": 24.075, "eval_steps_per_second": 3.013, "step": 7500}, {"epoch": 4.651162790697675, "grad_norm": 0.2343478798866272, "learning_rate": 2.7607323232323233e-05, "loss": 0.1266, "step": 7600}, {"epoch": 4.712362301101591, "grad_norm": 0.25510966777801514, "learning_rate": 2.7291666666666665e-05, "loss": 0.1266, "step": 7700}, {"epoch": 4.773561811505508, "grad_norm": 0.2252868413925171, "learning_rate": 2.6976010101010103e-05, "loss": 0.1271, "step": 7800}, {"epoch": 4.834761321909425, "grad_norm": 0.254788339138031, "learning_rate": 2.6660353535353538e-05, "loss": 0.1278, "step": 7900}, {"epoch": 4.8959608323133414, "grad_norm": 0.23061254620552063, "learning_rate": 2.634469696969697e-05, "loss": 0.1273, "step": 8000}, {"epoch": 4.8959608323133414, "eval_loss": 0.12644502520561218, "eval_runtime": 139.6842, "eval_samples_per_second": 23.396, "eval_steps_per_second": 2.928, "step": 8000}, {"epoch": 4.957160342717258, "grad_norm": 0.24553315341472626, "learning_rate": 2.6029040404040405e-05, "loss": 0.1264, "step": 8100}, {"epoch": 5.018359853121175, "grad_norm": 0.26036110520362854, "learning_rate": 2.5713383838383843e-05, "loss": 0.1287, "step": 8200}, {"epoch": 5.0795593635250915, "grad_norm": 0.2647603452205658, "learning_rate": 2.539772727272727e-05, "loss": 0.1264, "step": 8300}, {"epoch": 5.140758873929008, "grad_norm": 0.21830523014068604, "learning_rate": 2.508207070707071e-05, "loss": 0.1262, "step": 8400}, {"epoch": 5.201958384332926, "grad_norm": 0.2772153913974762, "learning_rate": 2.476641414141414e-05, "loss": 0.1267, "step": 8500}, {"epoch": 5.201958384332926, "eval_loss": 0.12665461003780365, "eval_runtime": 135.0597, "eval_samples_per_second": 24.197, "eval_steps_per_second": 3.028, "step": 8500}, {"epoch": 5.2631578947368425, "grad_norm": 0.25674208998680115, "learning_rate": 2.4450757575757577e-05, "loss": 0.1264, "step": 8600}, {"epoch": 5.324357405140759, "grad_norm": 0.2743734121322632, "learning_rate": 2.413510101010101e-05, "loss": 0.1272, "step": 8700}, {"epoch": 5.385556915544676, "grad_norm": 0.27094581723213196, "learning_rate": 2.3819444444444443e-05, "loss": 0.1265, "step": 8800}, {"epoch": 5.4467564259485926, "grad_norm": 0.28454920649528503, "learning_rate": 2.3503787878787882e-05, "loss": 0.1256, "step": 8900}, {"epoch": 5.507955936352509, "grad_norm": 0.2273436337709427, "learning_rate": 2.3188131313131313e-05, "loss": 0.1255, "step": 9000}, {"epoch": 5.507955936352509, "eval_loss": 0.12656117975711823, "eval_runtime": 143.4106, "eval_samples_per_second": 22.788, "eval_steps_per_second": 2.852, "step": 9000}, {"epoch": 5.569155446756426, "grad_norm": 0.2299308478832245, "learning_rate": 2.287247474747475e-05, "loss": 0.1264, "step": 9100}, {"epoch": 5.630354957160343, "grad_norm": 0.24222755432128906, "learning_rate": 2.2556818181818183e-05, "loss": 0.1265, "step": 9200}, {"epoch": 5.691554467564259, "grad_norm": 0.27320006489753723, "learning_rate": 2.224116161616162e-05, "loss": 0.1259, "step": 9300}, {"epoch": 5.752753977968176, "grad_norm": 0.20730991661548615, "learning_rate": 2.1925505050505053e-05, "loss": 0.1254, "step": 9400}, {"epoch": 5.813953488372093, "grad_norm": 0.2576620578765869, "learning_rate": 2.1609848484848485e-05, "loss": 0.1262, "step": 9500}, {"epoch": 5.813953488372093, "eval_loss": 0.12670473754405975, "eval_runtime": 135.7717, "eval_samples_per_second": 24.07, "eval_steps_per_second": 3.012, "step": 9500}, {"epoch": 5.875152998776009, "grad_norm": 0.2144061177968979, "learning_rate": 2.129419191919192e-05, "loss": 0.1259, "step": 9600}, {"epoch": 5.936352509179926, "grad_norm": 0.24197609722614288, "learning_rate": 2.0978535353535355e-05, "loss": 0.1263, "step": 9700}, {"epoch": 5.997552019583844, "grad_norm": 0.22296887636184692, "learning_rate": 2.066287878787879e-05, "loss": 0.1258, "step": 9800}, {"epoch": 6.05875152998776, "grad_norm": 0.24147918820381165, "learning_rate": 2.0347222222222222e-05, "loss": 0.1256, "step": 9900}, {"epoch": 6.119951040391677, "grad_norm": 0.26197612285614014, "learning_rate": 2.0031565656565657e-05, "loss": 0.1262, "step": 10000}, {"epoch": 6.119951040391677, "eval_loss": 0.12663882970809937, "eval_runtime": 135.9262, "eval_samples_per_second": 24.042, "eval_steps_per_second": 3.009, "step": 10000}, {"epoch": 6.181150550795594, "grad_norm": 0.2267717570066452, "learning_rate": 1.9715909090909092e-05, "loss": 0.1253, "step": 10100}, {"epoch": 6.24235006119951, "grad_norm": 0.2915138900279999, "learning_rate": 1.9400252525252527e-05, "loss": 0.1256, "step": 10200}, {"epoch": 6.303549571603427, "grad_norm": 0.21126341819763184, "learning_rate": 1.9084595959595962e-05, "loss": 0.1253, "step": 10300}, {"epoch": 6.364749082007344, "grad_norm": 0.2158355563879013, "learning_rate": 1.8768939393939393e-05, "loss": 0.1259, "step": 10400}, {"epoch": 6.4259485924112605, "grad_norm": 0.23935580253601074, "learning_rate": 1.845328282828283e-05, "loss": 0.1255, "step": 10500}, {"epoch": 6.4259485924112605, "eval_loss": 0.12606465816497803, "eval_runtime": 130.9021, "eval_samples_per_second": 24.965, "eval_steps_per_second": 3.124, "step": 10500}, {"epoch": 6.487148102815177, "grad_norm": 0.2260945737361908, "learning_rate": 1.8137626262626264e-05, "loss": 0.1257, "step": 10600}, {"epoch": 6.548347613219094, "grad_norm": 0.25032928586006165, "learning_rate": 1.78219696969697e-05, "loss": 0.1259, "step": 10700}, {"epoch": 6.6095471236230114, "grad_norm": 0.2702242434024811, "learning_rate": 1.750631313131313e-05, "loss": 0.1252, "step": 10800}, {"epoch": 6.670746634026928, "grad_norm": 0.19762498140335083, "learning_rate": 1.720012626262626e-05, "loss": 0.3229, "step": 10900}, {"epoch": 6.731946144430845, "grad_norm": 0.23758991062641144, "learning_rate": 1.68844696969697e-05, "loss": 0.1253, "step": 11000}, {"epoch": 6.731946144430845, "eval_loss": 0.12600551545619965, "eval_runtime": 134.3883, "eval_samples_per_second": 24.318, "eval_steps_per_second": 3.043, "step": 11000}, {"epoch": 6.7931456548347615, "grad_norm": 0.22479449212551117, "learning_rate": 1.656881313131313e-05, "loss": 0.1258, "step": 11100}, {"epoch": 6.854345165238678, "grad_norm": 0.23173139989376068, "learning_rate": 1.6253156565656566e-05, "loss": 0.125, "step": 11200}, {"epoch": 6.915544675642595, "grad_norm": 0.23901784420013428, "learning_rate": 1.59375e-05, "loss": 0.1259, "step": 11300}, {"epoch": 6.976744186046512, "grad_norm": 0.22954006493091583, "learning_rate": 1.5621843434343436e-05, "loss": 0.1257, "step": 11400}, {"epoch": 7.037943696450428, "grad_norm": 0.24196568131446838, "learning_rate": 1.530618686868687e-05, "loss": 0.1245, "step": 11500}, {"epoch": 7.037943696450428, "eval_loss": 0.12579667568206787, "eval_runtime": 137.946, "eval_samples_per_second": 23.69, "eval_steps_per_second": 2.965, "step": 11500}, {"epoch": 7.099143206854345, "grad_norm": 0.46720290184020996, "learning_rate": 1.4990530303030303e-05, "loss": 0.1252, "step": 11600}, {"epoch": 7.160342717258262, "grad_norm": 0.2298312783241272, "learning_rate": 1.4674873737373738e-05, "loss": 0.1251, "step": 11700}, {"epoch": 7.221542227662178, "grad_norm": 0.2390119433403015, "learning_rate": 1.4359217171717173e-05, "loss": 0.1249, "step": 11800}, {"epoch": 7.282741738066095, "grad_norm": 0.21674780547618866, "learning_rate": 1.4043560606060608e-05, "loss": 0.1254, "step": 11900}, {"epoch": 7.343941248470013, "grad_norm": 0.2588801980018616, "learning_rate": 1.3727904040404041e-05, "loss": 0.1251, "step": 12000}, {"epoch": 7.343941248470013, "eval_loss": 0.12616348266601562, "eval_runtime": 139.632, "eval_samples_per_second": 23.404, "eval_steps_per_second": 2.929, "step": 12000}, {"epoch": 7.405140758873929, "grad_norm": 0.28165164589881897, "learning_rate": 1.3412247474747475e-05, "loss": 0.1245, "step": 12100}, {"epoch": 7.466340269277846, "grad_norm": 0.2243030071258545, "learning_rate": 1.3096590909090908e-05, "loss": 0.1247, "step": 12200}, {"epoch": 7.527539779681763, "grad_norm": 0.22669844329357147, "learning_rate": 1.2780934343434345e-05, "loss": 0.1251, "step": 12300}, {"epoch": 7.588739290085679, "grad_norm": 0.23518474400043488, "learning_rate": 1.2465277777777778e-05, "loss": 0.1254, "step": 12400}, {"epoch": 7.649938800489596, "grad_norm": 0.24458260834217072, "learning_rate": 1.2149621212121213e-05, "loss": 0.1246, "step": 12500}, {"epoch": 7.649938800489596, "eval_loss": 0.12569041550159454, "eval_runtime": 143.0429, "eval_samples_per_second": 22.846, "eval_steps_per_second": 2.859, "step": 12500}, {"epoch": 7.711138310893513, "grad_norm": 0.22344303131103516, "learning_rate": 1.1833964646464648e-05, "loss": 0.1245, "step": 12600}, {"epoch": 7.7723378212974294, "grad_norm": 0.23193806409835815, "learning_rate": 1.1518308080808081e-05, "loss": 0.1252, "step": 12700}, {"epoch": 7.833537331701346, "grad_norm": 0.25006112456321716, "learning_rate": 1.1202651515151516e-05, "loss": 0.1247, "step": 12800}, {"epoch": 7.894736842105263, "grad_norm": 0.22549296915531158, "learning_rate": 1.088699494949495e-05, "loss": 0.1243, "step": 12900}, {"epoch": 7.95593635250918, "grad_norm": 0.23913076519966125, "learning_rate": 1.0571338383838385e-05, "loss": 0.1245, "step": 13000}, {"epoch": 7.95593635250918, "eval_loss": 0.12577275931835175, "eval_runtime": 144.892, "eval_samples_per_second": 22.555, "eval_steps_per_second": 2.823, "step": 13000}, {"epoch": 8.017135862913097, "grad_norm": 0.2343587428331375, "learning_rate": 1.0255681818181818e-05, "loss": 0.1244, "step": 13100}, {"epoch": 8.078335373317014, "grad_norm": 0.2485724538564682, "learning_rate": 9.940025252525253e-06, "loss": 0.1239, "step": 13200}, {"epoch": 8.13953488372093, "grad_norm": 0.2316555678844452, "learning_rate": 9.624368686868688e-06, "loss": 0.1246, "step": 13300}, {"epoch": 8.200734394124847, "grad_norm": 0.21623378992080688, "learning_rate": 9.308712121212123e-06, "loss": 0.1243, "step": 13400}, {"epoch": 8.261933904528764, "grad_norm": 0.2941739559173584, "learning_rate": 8.993055555555556e-06, "loss": 0.1236, "step": 13500}, {"epoch": 8.261933904528764, "eval_loss": 0.12594464421272278, "eval_runtime": 132.7908, "eval_samples_per_second": 24.61, "eval_steps_per_second": 3.08, "step": 13500}, {"epoch": 8.32313341493268, "grad_norm": 0.2230304330587387, "learning_rate": 8.67739898989899e-06, "loss": 0.1244, "step": 13600}, {"epoch": 8.384332925336597, "grad_norm": 0.2180684208869934, "learning_rate": 8.361742424242425e-06, "loss": 0.1246, "step": 13700}, {"epoch": 8.445532435740514, "grad_norm": 0.21756350994110107, "learning_rate": 8.046085858585858e-06, "loss": 0.1244, "step": 13800}, {"epoch": 8.50673194614443, "grad_norm": 0.24432049691677094, "learning_rate": 7.730429292929293e-06, "loss": 0.1246, "step": 13900}, {"epoch": 8.567931456548347, "grad_norm": 0.2527487874031067, "learning_rate": 7.414772727272727e-06, "loss": 0.1244, "step": 14000}, {"epoch": 8.567931456548347, "eval_loss": 0.12545812129974365, "eval_runtime": 135.4649, "eval_samples_per_second": 24.124, "eval_steps_per_second": 3.019, "step": 14000}, {"epoch": 8.629130966952264, "grad_norm": 0.2558770179748535, "learning_rate": 7.099116161616162e-06, "loss": 0.1247, "step": 14100}, {"epoch": 8.69033047735618, "grad_norm": 0.2382304072380066, "learning_rate": 6.783459595959596e-06, "loss": 0.1242, "step": 14200}, {"epoch": 8.751529987760097, "grad_norm": 0.21859833598136902, "learning_rate": 6.467803030303031e-06, "loss": 0.1233, "step": 14300}, {"epoch": 8.812729498164014, "grad_norm": 0.2573760449886322, "learning_rate": 6.152146464646465e-06, "loss": 0.1241, "step": 14400}, {"epoch": 8.87392900856793, "grad_norm": 0.2471712976694107, "learning_rate": 5.836489898989899e-06, "loss": 0.1244, "step": 14500}, {"epoch": 8.87392900856793, "eval_loss": 0.12545296549797058, "eval_runtime": 142.0286, "eval_samples_per_second": 23.009, "eval_steps_per_second": 2.88, "step": 14500}, {"epoch": 8.935128518971847, "grad_norm": 0.234666109085083, "learning_rate": 5.520833333333333e-06, "loss": 0.1247, "step": 14600}, {"epoch": 8.996328029375764, "grad_norm": 0.2501690685749054, "learning_rate": 5.205176767676768e-06, "loss": 0.124, "step": 14700}, {"epoch": 9.057527539779683, "grad_norm": 0.25173553824424744, "learning_rate": 4.889520202020202e-06, "loss": 0.1241, "step": 14800}, {"epoch": 9.1187270501836, "grad_norm": 0.276428759098053, "learning_rate": 4.5738636363636365e-06, "loss": 0.1234, "step": 14900}, {"epoch": 9.179926560587516, "grad_norm": 0.23317430913448334, "learning_rate": 4.258207070707071e-06, "loss": 0.1234, "step": 15000}, {"epoch": 9.179926560587516, "eval_loss": 0.12540371716022491, "eval_runtime": 137.0379, "eval_samples_per_second": 23.847, "eval_steps_per_second": 2.985, "step": 15000}, {"epoch": 9.241126070991433, "grad_norm": 0.21292752027511597, "learning_rate": 3.942550505050506e-06, "loss": 0.1237, "step": 15100}, {"epoch": 9.30232558139535, "grad_norm": 0.2165219485759735, "learning_rate": 3.62689393939394e-06, "loss": 0.1241, "step": 15200}, {"epoch": 9.363525091799266, "grad_norm": 0.22908800840377808, "learning_rate": 3.311237373737374e-06, "loss": 0.1238, "step": 15300}, {"epoch": 9.424724602203183, "grad_norm": 0.22948110103607178, "learning_rate": 2.9955808080808082e-06, "loss": 0.1235, "step": 15400}, {"epoch": 9.4859241126071, "grad_norm": 0.22540077567100525, "learning_rate": 2.6799242424242424e-06, "loss": 0.1233, "step": 15500}, {"epoch": 9.4859241126071, "eval_loss": 0.12520700693130493, "eval_runtime": 136.8797, "eval_samples_per_second": 23.875, "eval_steps_per_second": 2.988, "step": 15500}], "logging_steps": 100, "max_steps": 16340, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 9.276042541662418e+19, "train_batch_size": 8, "trial_name": null, "trial_params": null}