_multiprocess/__init__.py,sha256=zX5_h36TGSL0brHRtBvCL5E59ccW7yjL79i-Y399ODM,321
_multiprocess/__pycache__/__init__.cpython-38.pyc,,
multiprocess-0.70.16.dist-info/COPYING,sha256=n3_yfLkw0sMgLuB-PS1hRvTeZ20GmjPaMWbJjNuoOpU,1493
multiprocess-0.70.16.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
multiprocess-0.70.16.dist-info/LICENSE,sha256=6XUJedJKg2dhI98BD3PMtVtZvRFT-oGczkOr5B4tEEA,1930
multiprocess-0.70.16.dist-info/METADATA,sha256=r64vFh-qJVqCD6d-BvyT73-vI4yta48yAA24fTcvUk8,7108
multiprocess-0.70.16.dist-info/RECORD,,
multiprocess-0.70.16.dist-info/WHEEL,sha256=cc4K7jU8ZZX-fXnmDzFJSD9TVRP3C3AJtY7X7258Zng,93
multiprocess-0.70.16.dist-info/top_level.txt,sha256=qtJc8GNdvi6suNpISX0Myln9AXJBYrNuas1MCqRPPqg,27
multiprocess/__info__.py,sha256=84TUBn1oJMNpbVvXKs0lKyfLYaZvRr-ZVh1zHM9VeCY,7997
multiprocess/__init__.py,sha256=XWUBDGorUkDW04h64xe51pUV9N5gzvSDj3tNT2ekifw,1856
multiprocess/__pycache__/__info__.cpython-38.pyc,,
multiprocess/__pycache__/__init__.cpython-38.pyc,,
multiprocess/__pycache__/connection.cpython-38.pyc,,
multiprocess/__pycache__/context.cpython-38.pyc,,
multiprocess/__pycache__/forkserver.cpython-38.pyc,,
multiprocess/__pycache__/heap.cpython-38.pyc,,
multiprocess/__pycache__/managers.cpython-38.pyc,,
multiprocess/__pycache__/pool.cpython-38.pyc,,
multiprocess/__pycache__/popen_fork.cpython-38.pyc,,
multiprocess/__pycache__/popen_forkserver.cpython-38.pyc,,
multiprocess/__pycache__/popen_spawn_posix.cpython-38.pyc,,
multiprocess/__pycache__/popen_spawn_win32.cpython-38.pyc,,
multiprocess/__pycache__/process.cpython-38.pyc,,
multiprocess/__pycache__/queues.cpython-38.pyc,,
multiprocess/__pycache__/reduction.cpython-38.pyc,,
multiprocess/__pycache__/resource_sharer.cpython-38.pyc,,
multiprocess/__pycache__/resource_tracker.cpython-38.pyc,,
multiprocess/__pycache__/shared_memory.cpython-38.pyc,,
multiprocess/__pycache__/sharedctypes.cpython-38.pyc,,
multiprocess/__pycache__/spawn.cpython-38.pyc,,
multiprocess/__pycache__/synchronize.cpython-38.pyc,,
multiprocess/__pycache__/util.cpython-38.pyc,,
multiprocess/connection.py,sha256=TO9BbLVlLVjTjr0fP7lIumBgiLwaFVnpqMBgFG6iL9s,31843
multiprocess/context.py,sha256=zpJw0Rb1QhPO_OUHW7PIoXhWt0yAC2g9P0htI1ExNzY,11270
multiprocess/dummy/__init__.py,sha256=kSekDqD_NCy0FDg7XnxZSgW-Ldg1_iRr07sNwDajKpA,3061
multiprocess/dummy/__pycache__/__init__.cpython-38.pyc,,
multiprocess/dummy/__pycache__/connection.cpython-38.pyc,,
multiprocess/dummy/connection.py,sha256=1j3Rl5_enBM-_kMO6HDmum3kPAoFE4Zs485HV5H-V6s,1598
multiprocess/forkserver.py,sha256=t9salrNZmjv0t68zoIZwgpEfDXjUjV5G4L-C_tARmFc,12528
multiprocess/heap.py,sha256=9rt5u5m5rkhJNfDWiCLpYDoWIt0LbElmx52yMqk7phQ,11626
multiprocess/managers.py,sha256=0qFVEz8BxdAs3i012kvp-1aDHhLcroY6HGfvml_YNic,48977
multiprocess/pool.py,sha256=qkusuDj4TwAFBp-YjNeOdx2-vcY2ngskRxOiPOy_ZCg,32509
multiprocess/popen_fork.py,sha256=WGSEKgwdMTq_aOi0vrS6TFdbVsZRxtQZ6HHI5pv_eko,2565
multiprocess/popen_forkserver.py,sha256=SrEbV8Wv0Uu_UegkaW-cayXRdjTGXr560Yyy90pj-yE,2227
multiprocess/popen_spawn_posix.py,sha256=l7XSWqR5UWiUSJh35qeSElLuNfUeEYwvH5HzKRnnyqg,2029
multiprocess/popen_spawn_win32.py,sha256=A9uvlPmhO8JBzNcEU_Gmix2Q_qYJW1NXZgXPwtN5Ao0,4011
multiprocess/process.py,sha256=L_4TBh91cxtKYPZZh10clAwks2UIOu-Bjka_kNxr3ds,11999
multiprocess/queues.py,sha256=9zNyet64T6steQ32YN60P4hQmCUVPCtyNjPSCegQYJY,11806
multiprocess/reduction.py,sha256=NQQ6KbDhmuAyaDeWaIarTZQokGPhcFda1poNnPm5uNc,9637
multiprocess/resource_sharer.py,sha256=eEGklEb732aLl5QiXSYfNEA6fUCJMEwa8raknmcK5zg,5352
multiprocess/resource_tracker.py,sha256=AUypNVano3I0_mEA1GXmdg0Vfy0bsKMUU8mxCwg6uCs,8696
multiprocess/shared_memory.py,sha256=_HIB4pGdYL0Z_VjC-OgebSH2j_AO96pcIg_JKwMG5j8,17422
multiprocess/sharedctypes.py,sha256=d-9SKRJHRlJJC331IxEoWOUXIeY9zxCbhWejXOmzGw0,6306
multiprocess/spawn.py,sha256=cgtV66HhV_yIVzvdblc8bVdSpem16Ks0BOFu_bV5PDQ,9293
multiprocess/synchronize.py,sha256=6q1ijwWyWLWLO8uUtaYT9MKepAYKfdzWPSEZGyJFP4s,11829
multiprocess/tests/__init__.py,sha256=VLh2tsdJ083GasHjeoqh6WHmR0a_B8BuylJNXTmXIVA,188947
multiprocess/tests/__main__.py,sha256=RauIRQrO0HwRq_clLqbBk4gwo5Xw3-ASLuC029XaHeA,912
multiprocess/tests/__pycache__/__init__.cpython-38.pyc,,
multiprocess/tests/__pycache__/__main__.cpython-38.pyc,,
multiprocess/tests/__pycache__/mp_fork_bomb.cpython-38.pyc,,
multiprocess/tests/__pycache__/mp_preload.cpython-38.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_fork.cpython-38.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_forkserver.cpython-38.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_main_handling.cpython-38.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_spawn.cpython-38.pyc,,
multiprocess/tests/mp_fork_bomb.py,sha256=6ADOEzh1aXHZ21aOGoBPhKcgB5sj15G9tQVgSc6GrlY,448
multiprocess/tests/mp_preload.py,sha256=1-WvLFMaPoH-vZbpUaJvvZHFxTpA9tgmct2vblQy99M,365
multiprocess/tests/test_multiprocessing_fork.py,sha256=ue1SQLJFxm1oc_3F2gR_WRtt39jhaj0l_Ht6Y1MBmFo,476
multiprocess/tests/test_multiprocessing_forkserver.py,sha256=VFlUuZI60gyRbNxfHWDlgmy3zm-dPTldLWuKQZ8KObs,391
multiprocess/tests/test_multiprocessing_main_handling.py,sha256=T0SweOROvIyuvkCTmBWlsIRcEstG6TBIh4RJg3WS7XA,11888
multiprocess/tests/test_multiprocessing_spawn.py,sha256=2UAisJX58GZCbYuDFay_x97R9akhzzjIA4VuUUzITOY,276
multiprocess/util.py,sha256=VnDd99ijWxbd53nnXkou7MWjfGEwAlRsx--E6PX0t9o,13938
