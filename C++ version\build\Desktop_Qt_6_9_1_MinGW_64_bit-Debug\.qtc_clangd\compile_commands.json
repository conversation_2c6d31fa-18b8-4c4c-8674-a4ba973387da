[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PROGRAMS_D\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PROGRAMS_D\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PROGRAMS_D\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\build\\moc", "-IC:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version\\kiem_tra_mst_qt.cpp"], "directory": "C:/Users/<USER>/Desktop/kiem tra mst v4/C++ version/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/kiem tra mst v4/C++ version/kiem_tra_mst_qt.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PROGRAMS_D\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PROGRAMS_D\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PROGRAMS_D\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\build\\moc", "-IC:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-ID:\\PROGRAMS_D\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\PROGRAMS_D\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version\\kiem_tra_mst_qt.h"], "directory": "C:/Users/<USER>/Desktop/kiem tra mst v4/C++ version/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/kiem tra mst v4/C++ version/kiem_tra_mst_qt.h"}]