using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace KiemTraMST
{
    public class ConfigManager
    {
        private static readonly Lazy<ConfigManager> _instance = new(() => new ConfigManager());
        public static ConfigManager Instance => _instance.Value;

        private readonly Dictionary<string, Dictionary<string, string>> _config = [];
        private readonly string _configPath = "setting.ini";

        private ConfigManager()
        {
            LoadConfig();
        }

        public void LoadConfig()
        {
            _config.Clear();

            if (!File.Exists(_configPath))
            {
                CreateDefaultConfig();
                return;
            }

            try
            {
                var lines = File.ReadAllLines(_configPath, Encoding.UTF8);
                string currentSection = "DEFAULT";
                
                if (!_config.ContainsKey(currentSection))
                    _config[currentSection] = [];

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith(";") || trimmedLine.StartsWith("#"))
                        continue;

                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                    {
                        currentSection = trimmedLine[1..^1];
                        if (!_config.ContainsKey(currentSection))
                            _config[currentSection] = new Dictionary<string, string>();
                        continue;
                    }

                    var equalIndex = trimmedLine.IndexOf('=');
                    if (equalIndex > 0)
                    {
                        var key = trimmedLine[..equalIndex].Trim();
                        var value = trimmedLine[(equalIndex + 1)..].Trim();
                        _config[currentSection][key] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading config: {ex.Message}");
                CreateDefaultConfig();
            }
        }

        private void CreateDefaultConfig()
        {
            _config["DEFAULT"] = new Dictionary<string, string>
            {
                ["max_workers"] = "20",
                ["timeout"] = "30",
                ["retry_count"] = "3"
            };

            SaveConfig();
        }

        public void SaveConfig()
        {
            try
            {
                var lines = new List<string>();

                foreach (var section in _config)
                {
                    if (section.Key != "DEFAULT")
                        lines.Add($"[{section.Key}]");

                    foreach (var kvp in section.Value)
                    {
                        lines.Add($"{kvp.Key}={kvp.Value}");
                    }

                    lines.Add("");
                }

                File.WriteAllLines(_configPath, lines, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving config: {ex.Message}");
            }
        }

        public string GetString(string section, string key, string defaultValue = "")
        {
            return _config.TryGetValue(section, out var sectionDict) &&
                   sectionDict.TryGetValue(key, out var value) ? value : defaultValue;
        }

        public int GetInt(string section, string key, int defaultValue = 0)
        {
            var value = GetString(section, key);
            return int.TryParse(value, out var result) ? result : defaultValue;
        }

        public bool GetBool(string section, string key, bool defaultValue = false)
        {
            var value = GetString(section, key).ToLowerInvariant();
            return value switch
            {
                "true" or "1" or "yes" or "on" => true,
                "false" or "0" or "no" or "off" => false,
                _ => defaultValue
            };
        }

        public void SetString(string section, string key, string value)
        {
            if (!_config.ContainsKey(section))
                _config[section] = [];

            _config[section][key] = value;
        }

        public void SetInt(string section, string key, int value)
        {
            SetString(section, key, value.ToString());
        }

        public void SetBool(string section, string key, bool value)
        {
            SetString(section, key, value.ToString().ToLowerInvariant());
        }
    }
}
