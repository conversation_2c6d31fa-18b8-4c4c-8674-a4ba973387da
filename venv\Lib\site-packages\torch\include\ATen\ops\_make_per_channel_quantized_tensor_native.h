#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor & _make_per_channel_quantized_tensor_out(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis, at::Tensor & out);
TORCH_API at::Tensor make_per_channel_quantized_tensor_cpu(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis);
TORCH_API at::Tensor make_per_channel_quantized_tensor_cuda(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis);
} // namespace native
} // namespace at
