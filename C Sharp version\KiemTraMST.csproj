<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>Kiểm Tra MST</AssemblyTitle>
    <AssemblyVersion>********</AssemblyVersion>
    <FileVersion>********</FileVersion>
    <AssemblyDescription>Ứng dụng kiểm tra MST với OCR và Hardware Detection</AssemblyDescription>
    <Copyright>Copyright © 2024</Copyright>

    <PublishAot>false</PublishAot>
    <PublishTrimmed>false</PublishTrimmed>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <UseWindowsForms>false</UseWindowsForms>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <!-- OCR and ML -->
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.19.2" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime.Gpu" Version="1.19.2" />

    <!-- Image processing -->
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.4" />

    <!-- HTTP and JSON -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RestSharp" Version="112.0.0" />

    <!-- Captcha solving (simplified) -->
    <!-- Using HTTP clients for API calls -->

    <!-- Emoji support (built-in) -->
    <!-- Using native WPF emoji support -->

    <!-- System utilities -->
    <PackageReference Include="System.Management" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <!-- Exclude Qt files from WPF build -->
    <Compile Remove="MainWindow_Qt.cs" />
    <Compile Remove="Program_Qt.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Update="setting.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Models\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
