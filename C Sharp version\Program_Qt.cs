using System;
using System.IO;
using System.Threading.Tasks;
using Python.Runtime;

namespace KiemTraMST
{
    class Program_Qt
    {
        [STAThread]
        static int Main(string[] args)
        {
            try
            {
                Console.WriteLine("========================================");
                Console.WriteLine("Kiểm Tra MST v5.0.13 - Qt C# Version");
                Console.WriteLine("========================================");
                Console.WriteLine();

                // Check Python installation
                if (!CheckPythonEnvironment())
                {
                    Console.WriteLine("❌ Python environment not ready!");
                    Console.WriteLine("Please install Python 3.8+ and PySide6:");
                    Console.WriteLine("  pip install PySide6");
                    Console.WriteLine();
                    Console.WriteLine("Press any key to exit...");
                    Console.ReadKey();
                    return 1;
                }

                Console.WriteLine("✅ Python environment ready");
                Console.WriteLine("🚀 Starting Qt application...");
                Console.WriteLine();

                // Initialize and run Qt application
                using var mainWindow = new MainWindow_Qt();
                mainWindow.Show();
                
                Console.WriteLine("✅ Qt application started successfully");
                Console.WriteLine("📱 Main window is now visible");
                Console.WriteLine();

                return mainWindow.Exec();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Fatal error: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("Stack trace:");
                Console.WriteLine(ex.StackTrace);
                Console.WriteLine();
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
                return 1;
            }
        }

        private static bool CheckPythonEnvironment()
        {
            try
            {
                // Try to initialize Python.NET
                if (!PythonEngine.IsInitialized)
                {
                    // Set Python DLL path if needed
                    var pythonDll = FindPythonDll();
                    if (!string.IsNullOrEmpty(pythonDll))
                    {
                        Runtime.PythonDLL = pythonDll;
                    }

                    PythonEngine.Initialize();
                }

                using (Py.GIL())
                {
                    // Test basic Python functionality
                    dynamic sys = Py.Import("sys");
                    Console.WriteLine($"🐍 Python version: {sys.version}");

                    // Test PySide6 import
                    try
                    {
                        dynamic QtWidgets = Py.Import("PySide6.QtWidgets");
                        Console.WriteLine("✅ PySide6.QtWidgets imported successfully");
                        return true;
                    }
                    catch (PythonException ex)
                    {
                        Console.WriteLine($"❌ Failed to import PySide6: {ex.Message}");
                        Console.WriteLine("Please install PySide6: pip install PySide6");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Python initialization failed: {ex.Message}");
                return false;
            }
        }

        private static string FindPythonDll()
        {
            // Common Python DLL locations
            var possiblePaths = new[]
            {
                @"C:\Python39\python39.dll",
                @"C:\Python310\python310.dll",
                @"C:\Python311\python311.dll",
                @"C:\Python312\python312.dll",
                @"C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python39.dll",
                @"C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python310.dll",
                @"C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python311.dll",
                @"C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.dll"
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    Console.WriteLine($"🔍 Found Python DLL: {path}");
                    return path;
                }
            }

            Console.WriteLine("⚠️ Python DLL not found in common locations");
            return "";
        }
    }
}
