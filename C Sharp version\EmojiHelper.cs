using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;

namespace KiemTraMST
{
    /// <summary>
    /// Helper class for colorful emoji support in WPF
    /// </summary>
    public static class EmojiHelper
    {
        // Emoji definitions with colors
        private static readonly Dictionary<string, (string emoji, Brush color)> EmojiMap = new()
        {
            // Status emojis
            ["loading"] = ("⏳", Brushes.Orange),
            ["ready"] = ("✅", Brushes.Green),
            ["error"] = ("❌", Brushes.Red),
            ["warning"] = ("⚠️", Brushes.Orange),
            ["info"] = ("ℹ️", Brushes.Blue),
            ["success"] = ("🎉", Brushes.Green),
            
            // Action emojis
            ["start"] = ("🚀", Brushes.LimeGreen),
            ["pause"] = ("⏸", Brushes.Orange),
            ["resume"] = ("▶️", Brushes.Green),
            ["stop"] = ("⏹", Brushes.Red),
            ["retry"] = ("🔁", Brushes.Blue),
            
            // Hardware emojis
            ["cpu"] = ("🖥️", Brushes.Blue),
            ["gpu_nvidia"] = ("🎮", Brushes.Green),
            ["gpu_intel"] = ("🔷", Brushes.Blue),
            ["detect"] = ("🔍", Brushes.Purple),
            
            // File emojis
            ["file"] = ("📄", Brushes.Gray),
            ["folder"] = ("📁", Brushes.Gold),
            ["save"] = ("💾", Brushes.Blue),
            ["export"] = ("📤", Brushes.Green),
            
            // Progress emojis
            ["progress"] = ("📊", Brushes.Blue),
            ["timer"] = ("⏱", Brushes.DarkBlue),
            ["stats"] = ("📈", Brushes.Green),
            
            // MST emojis
            ["mst"] = ("📋", Brushes.DarkBlue),
            ["check"] = ("✅", Brushes.Green),
            ["fail"] = ("❌", Brushes.Red),
            ["processing"] = ("🔄", Brushes.Orange),
            
            // Debug emojis
            ["debug"] = ("🐛", Brushes.Purple),
            ["log"] = ("📝", Brushes.Gray),
            ["console"] = ("💻", Brushes.Black),
            
            // Captcha emojis
            ["captcha"] = ("🔐", Brushes.Orange),
            ["robot"] = ("🤖", Brushes.Blue),
            ["human"] = ("👤", Brushes.Green),
            
            // Network emojis
            ["api"] = ("🌐", Brushes.Blue),
            ["download"] = ("⬇️", Brushes.Green),
            ["upload"] = ("⬆️", Brushes.Orange),
            
            // Misc emojis
            ["settings"] = ("⚙️", Brushes.Gray),
            ["help"] = ("❓", Brushes.Blue),
            ["about"] = ("ℹ️", Brushes.Blue),
            ["exit"] = ("🚪", Brushes.Red)
        };

        /// <summary>
        /// Get emoji with color for TextBlock
        /// </summary>
        public static void SetEmojiText(TextBlock textBlock, string emojiKey, string additionalText = "")
        {
            if (!EmojiMap.TryGetValue(emojiKey, out var emojiData))
            {
                textBlock.Text = additionalText;
                return;
            }

            textBlock.Inlines.Clear();

            // Add colored emoji
            var emojiRun = new Run(emojiData.emoji)
            {
                Foreground = emojiData.color,
                FontSize = textBlock.FontSize * 1.2 // Make emoji slightly larger
            };
            textBlock.Inlines.Add(emojiRun);

            // Add additional text if provided
            if (!string.IsNullOrEmpty(additionalText))
            {
                var textRun = new Run(" " + additionalText)
                {
                    Foreground = textBlock.Foreground
                };
                textBlock.Inlines.Add(textRun);
            }
        }

        /// <summary>
        /// Get emoji with color for Label (overload)
        /// </summary>
        public static void SetEmojiText(Label label, string emojiKey, string additionalText = "")
        {
            if (!EmojiMap.TryGetValue(emojiKey, out var emojiData))
            {
                label.Content = additionalText;
                return;
            }

            var textBlock = new TextBlock();

            // Add colored emoji
            var emojiRun = new Run(emojiData.emoji)
            {
                Foreground = emojiData.color,
                FontSize = label.FontSize * 1.2
            };
            textBlock.Inlines.Add(emojiRun);

            // Add additional text if provided
            if (!string.IsNullOrEmpty(additionalText))
            {
                var textRun = new Run(" " + additionalText);
                textBlock.Inlines.Add(textRun);
            }

            label.Content = textBlock;
        }

        /// <summary>
        /// Get emoji with color for Button
        /// </summary>
        public static void SetEmojiButton(Button button, string emojiKey, string buttonText)
        {
            if (!EmojiMap.TryGetValue(emojiKey, out var emojiData))
            {
                button.Content = buttonText;
                return;
            }

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // Add colored emoji
            var emojiTextBlock = new TextBlock
            {
                Text = emojiData.emoji,
                Foreground = emojiData.color,
                FontSize = button.FontSize * 1.1,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 5, 0)
            };
            stackPanel.Children.Add(emojiTextBlock);

            // Add button text
            var textBlock = new TextBlock
            {
                Text = buttonText,
                VerticalAlignment = VerticalAlignment.Center
            };
            stackPanel.Children.Add(textBlock);

            button.Content = stackPanel;
        }

        /// <summary>
        /// Get emoji with color for Label
        /// </summary>
        public static void SetEmojiLabel(Label label, string emojiKey, string labelText)
        {
            if (!EmojiMap.TryGetValue(emojiKey, out var emojiData))
            {
                label.Content = labelText;
                return;
            }

            var textBlock = new TextBlock();
            
            // Add colored emoji
            var emojiRun = new Run(emojiData.emoji)
            {
                Foreground = emojiData.color,
                FontSize = label.FontSize * 1.1
            };
            textBlock.Inlines.Add(emojiRun);
            
            // Add label text
            if (!string.IsNullOrEmpty(labelText))
            {
                var textRun = new Run(" " + labelText);
                textBlock.Inlines.Add(textRun);
            }

            label.Content = textBlock;
        }

        /// <summary>
        /// Create status text with multiple emojis and colors
        /// </summary>
        public static void SetStatusText(TextBlock textBlock, params (string emojiKey, string text, Brush? textColor)[] parts)
        {
            textBlock.Inlines.Clear();

            for (int i = 0; i < parts.Length; i++)
            {
                var (emojiKey, text, textColor) = parts[i];
                
                // Add separator if not first item
                if (i > 0)
                {
                    var separator = new Run(" | ")
                    {
                        Foreground = Brushes.Gray
                    };
                    textBlock.Inlines.Add(separator);
                }

                // Add emoji if exists
                if (EmojiMap.TryGetValue(emojiKey, out var emojiData))
                {
                    var emojiRun = new Run(emojiData.emoji)
                    {
                        Foreground = emojiData.color,
                        FontSize = textBlock.FontSize * 1.1
                    };
                    textBlock.Inlines.Add(emojiRun);
                }

                // Add text
                if (!string.IsNullOrEmpty(text))
                {
                    var textRun = new Run((EmojiMap.ContainsKey(emojiKey) ? " " : "") + text)
                    {
                        Foreground = textColor ?? textBlock.Foreground
                    };
                    textBlock.Inlines.Add(textRun);
                }
            }
        }

        /// <summary>
        /// Get hardware emoji based on device type
        /// </summary>
        public static string GetHardwareEmoji(string deviceType, string deviceName)
        {
            return deviceType.ToLower() switch
            {
                "nvidia" or "cuda" => "🎮",
                "intel" => "🔷", 
                "cpu" => "🖥️",
                _ => "🔧"
            };
        }

        /// <summary>
        /// Get hardware color based on device type
        /// </summary>
        public static Brush GetHardwareColor(string deviceType)
        {
            return deviceType.ToLower() switch
            {
                "nvidia" or "cuda" => Brushes.Green,
                "intel" => Brushes.Blue,
                "cpu" => Brushes.DarkBlue,
                _ => Brushes.Gray
            };
        }

        /// <summary>
        /// Create progress stats text with colored emojis
        /// </summary>
        public static void SetProgressStats(TextBlock textBlock, int ok, int fail, int processing, int total)
        {
            textBlock.Inlines.Clear();

            // Success count
            var successRun = new Run("✅" + ok)
            {
                Foreground = Brushes.Green,
                FontWeight = FontWeights.Bold
            };
            textBlock.Inlines.Add(successRun);

            // Separator
            textBlock.Inlines.Add(new Run(" "));

            // Fail count  
            var failRun = new Run("❌" + fail)
            {
                Foreground = Brushes.Red,
                FontWeight = FontWeights.Bold
            };
            textBlock.Inlines.Add(failRun);

            // Separator
            textBlock.Inlines.Add(new Run(" "));

            // Processing count
            var processingRun = new Run("🔄" + processing)
            {
                Foreground = Brushes.Orange,
                FontWeight = FontWeights.Bold
            };
            textBlock.Inlines.Add(processingRun);

            // Total
            textBlock.Inlines.Add(new Run($" / {total}"));
        }

        /// <summary>
        /// Add emoji to existing text
        /// </summary>
        public static string AddEmoji(string emojiKey, string text = "")
        {
            if (EmojiMap.TryGetValue(emojiKey, out var emojiData))
            {
                return string.IsNullOrEmpty(text) ? emojiData.emoji : $"{emojiData.emoji} {text}";
            }
            return text;
        }

        /// <summary>
        /// Get all available emoji keys
        /// </summary>
        public static IEnumerable<string> GetAvailableEmojis()
        {
            return EmojiMap.Keys;
        }
    }
}
