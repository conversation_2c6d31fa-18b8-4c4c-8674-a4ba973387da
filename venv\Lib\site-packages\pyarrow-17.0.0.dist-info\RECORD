benchmarks/__init__.py,sha256=EVfaECqIP6zwm3-KeFxrnNB3-C4waxZUgr3MmUMTU6k,801
benchmarks/__pycache__/__init__.cpython-38.pyc,,
benchmarks/__pycache__/array_ops.cpython-38.pyc,,
benchmarks/__pycache__/common.cpython-38.pyc,,
benchmarks/__pycache__/convert_builtins.cpython-38.pyc,,
benchmarks/__pycache__/convert_pandas.cpython-38.pyc,,
benchmarks/__pycache__/io.cpython-38.pyc,,
benchmarks/__pycache__/microbenchmarks.cpython-38.pyc,,
benchmarks/__pycache__/parquet.cpython-38.pyc,,
benchmarks/__pycache__/streaming.cpython-38.pyc,,
benchmarks/array_ops.py,sha256=fnGMFS4WHKqIr77Ru_IHJmf6N9K4lJhv3NNZt25oBQQ,1203
benchmarks/common.py,sha256=GW_daAgA_P9dnKO-AqbEL2tKSqblJx_62iiRdCGsq8I,12921
benchmarks/convert_builtins.py,sha256=sVjsT6lE4QsJhgTGCGKxlkPQep-4jhJWLucFHt-WwE8,2745
benchmarks/convert_pandas.py,sha256=mZtgKT2-bPxzytFE7AZWYvG9MBzNQETCMS0QZsiJAHQ,3439
benchmarks/io.py,sha256=QvbspEX2Gl3SwsxaWsgJ8RXbh1KcaOZdJ_3lyq6V7iM,2763
benchmarks/microbenchmarks.py,sha256=Dyytgq33Y_4Rj7xYjbnWu9o8voAOUUw2WilEy3nh82w,1633
benchmarks/parquet.py,sha256=GK8AeDJ2KHGn9_nyHkPXBQStnlcy9YsDutFRW6Z76Xc,4483
benchmarks/streaming.py,sha256=-cWrsbpOR9YT04rEgRPjXCW6kipBo6kRCrNvgK7eH7c,2610
cmake_modules/AWSSDKVariables.cmake,sha256=lTgh7IFRVjKOgr4Hn01yagFNrFnFxfkwL0VOvyISFJA,11826
cmake_modules/BuildUtils.cmake,sha256=CohQ3FaxqsBmMG5kZoQOg7QEvTZZK-wznQLazQrHlkY,35407
cmake_modules/DefineOptions.cmake,sha256=NHV3bNmGRqGVQyY_dHGgnfC0R5OuB7qB9xiLg_uOL1I,29911
cmake_modules/FindAWSSDKAlt.cmake,sha256=STgJf9O3jWm6KzgMDJBYtQJJY5MpmWqLLiMxD9EBNCM,1924
cmake_modules/FindAzure.cmake,sha256=Bn8qsSdpT3BlwM77zaSuPO_qK9PKgRPfU9bFzR9Y1yc,1673
cmake_modules/FindBrotliAlt.cmake,sha256=6BQmJrU7xVmlngrZXcENhRqRGmqTl60Z54srqkG7YwQ,6965
cmake_modules/FindClangTools.cmake,sha256=2Tm64rjw05GgSz8qV_IaSddnNoALPbB0PTQTpBlWz74,4525
cmake_modules/FindGTestAlt.cmake,sha256=4QwufPBXoGXPQesUkbG3ZlYnKl0IEng2o7k7uAWeHK4,2868
cmake_modules/FindInferTools.cmake,sha256=WVB7oXUKjQDreZXHlz3VbOQI6HV1jLUch9GCv6XO7j4,1534
cmake_modules/FindLLVMAlt.cmake,sha256=6Bq7WwAYu0fFZMr1_LhIoQQunEe3oq-s-RFn-n3er3I,4767
cmake_modules/FindOpenSSLAlt.cmake,sha256=HuViKdCxt9N2Ig-MIFaZe4IXUylLCtbnFq4qXxGHQtU,1901
cmake_modules/FindProtobufAlt.cmake,sha256=V-E3U506gjIxk_bBkBmp5eljG63xJJEgRZLRQRnBkE8,2267
cmake_modules/FindPython3Alt.cmake,sha256=FnWJ0Z0CuJO4RMpcxe8z79rUv6ne-UglGJR4O-Jpw7A,3362
cmake_modules/FindRapidJSONAlt.cmake,sha256=A9rQdg4XisQnnE3DoOZg_HppcR3u5cEA_JDc_v0uyWA,3660
cmake_modules/FindSQLite3Alt.cmake,sha256=GF3gRSs25n3YyfgzfhMCHXlA27pArdsoZHcbNqLjynI,1782
cmake_modules/FindSnappyAlt.cmake,sha256=B9RribOpBM2BlXxm6TStXBSNiskI9pYwHw9ut1erhKg,3765
cmake_modules/FindThriftAlt.cmake,sha256=ROhNWWynY0Y7z0n3fd9nXr6giuT4Sf9Mxjfd833-M7s,7240
cmake_modules/Findc-aresAlt.cmake,sha256=ka4dOcXahmfjeYPTriOFzBO7BpyYzooc3iJF-v398eQ,2818
cmake_modules/FindgRPCAlt.cmake,sha256=9tF5ApMMsBZeFf5wHPDymslxzUd-0jXtkpdlCMAfQgs,4412
cmake_modules/FindgflagsAlt.cmake,sha256=mWmqELfL9oS4EHkmbW6SHzeHxYtL8lH-JekiQmeocFg,2186
cmake_modules/FindglogAlt.cmake,sha256=DfKu-77QoFzwayc65P3w29jT-xK55zSA2wYwkA10evc,2462
cmake_modules/FindjemallocAlt.cmake,sha256=0Hv5CX0_5d-Q4jyMRJX6O0cQ8PdpHYSIJxQgNhvQgmQ,3745
cmake_modules/Findlibrados.cmake,sha256=6zreikkUXeBwzHei8A77P2nlVFzLA85VT47AwaJgJz4,1501
cmake_modules/Findlz4Alt.cmake,sha256=8wDCw6yQA70XJXJkceVW8B7TdsWWJ3AirTpOWFH-psw,3580
cmake_modules/FindorcAlt.cmake,sha256=zDRmWUZnwS5a0cnmU3v4ITOlDDgY2tDuVYRXLwJUSoI,2595
cmake_modules/Findre2Alt.cmake,sha256=q8GFQZONJldvTPWOCvYVkm7FmLOUTlx74jsYb1ftBs4,3354
cmake_modules/Findutf8proc.cmake,sha256=WnmVmlvjSbPkUzw-CaPhuLmyhIyzkGbTks-OT-Td4Ds,4696
cmake_modules/FindzstdAlt.cmake,sha256=M8qY93ZaMHRLcOixQzJw7BF6861tqAbEHCloYsp89sE,5219
cmake_modules/GandivaAddBitcode.cmake,sha256=pASjHSMl-tStKmbF-ETTg2F2-iFuTY_5Id0L8Qy_GnE,2859
cmake_modules/SetupCxxFlags.cmake,sha256=N-bkc4OvyPrSLyll-L7TEq2mG24FbWh_Qefox8-DPBo,36013
cmake_modules/ThirdpartyToolchain.cmake,sha256=UI2LNmtgfHCsaDsX0OjHolVfYckN4Km3-EccJIRHtxw,228611
cmake_modules/UseCython.cmake,sha256=Y7Db_WF9sSH126QcRzPBj-dLbd8b_-z5Q2BmkSjouW8,6969
cmake_modules/Usevcpkg.cmake,sha256=PKyu7lr5WMVG3c_3IUrdDX4WNatqk0GDEJFmWmcGbmI,9304
cmake_modules/aws_sdk_cpp_generate_variables.sh,sha256=z_ACxeveqcdajzbsUZmkmo_56Ln0036_H0Co_nKnNFw,2563
cmake_modules/san-config.cmake,sha256=Wdd4ziTZfG7Hzw0AVC-4jZjxmsEu-kvxiIumZ10twhU,5548
cmake_modules/snappy.diff,sha256=MCL1yuEkToIPPGyr5R7ACOy5peyb8AbdBp77K6xlgpY,1083
examples/dataset/__pycache__/write_dataset_encrypted.cpython-38.pyc,,
examples/dataset/write_dataset_encrypted.py,sha256=666cKfg2I7qbMrAPb-iJNFF9QXfSuKViImy68NhqhYw,3486
examples/flight/__pycache__/client.cpython-38.pyc,,
examples/flight/__pycache__/middleware.cpython-38.pyc,,
examples/flight/__pycache__/server.cpython-38.pyc,,
examples/flight/client.py,sha256=nwcrKGyvzfNrelD-RIP88ntAa1Mr1ljsXd4x9Avh4Kc,6980
examples/flight/middleware.py,sha256=-mAMiCFF-Qrk9r6ggqrIN3TyaKJpO2k_ZB5489QgBxc,5576
examples/flight/server.py,sha256=mnPcVyWF3OYZtlU5uorMKRlBUK-Lv6uOjYYvk__38t0,5930
examples/minimal_build/Dockerfile.fedora,sha256=hk9Lp3FDUtiNqTBPv8PG3RpQ1nLP0pTMrP9sbXhEJ_0,1108
examples/minimal_build/Dockerfile.ubuntu,sha256=2OAN4MR-xcnGCJgB8aoTtwDrhgfudWnXRXnRLx1NUKQ,1357
examples/minimal_build/README.md,sha256=bCRXdmUq-CT0lSUWDFs28s_Kjtg8nQa7Ba_MFS3yEyE,2895
examples/minimal_build/build_conda.sh,sha256=k8mQ2TKIyxORok7LFASYbpYbUg7lyQCxIBzcsfrCNJc,3194
examples/minimal_build/build_venv.sh,sha256=qKDjDN0H5AnIaAr4rZRmZSFHkpv2u7hXulBbp7p00fM,2515
examples/minimal_build/docker-compose.yml,sha256=Z4c-mI26Y0n8AverpvCoTQ2TLOrMNCzRS0t9p2DSC-s,1563
examples/parquet_encryption/__pycache__/sample_vault_kms_client.cpython-38.pyc,,
examples/parquet_encryption/sample_vault_kms_client.py,sha256=wXehDc4o2ht8rB1v_m-RfLL2eJ6OUgiVkFhUKr3P5hc,6148
pyarrow-17.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyarrow-17.0.0.dist-info/LICENSE.txt,sha256=zF8eiToZoMNYEZ1FWDtXs4zjokeX32t4BVMcRMRsORc,112684
pyarrow-17.0.0.dist-info/METADATA,sha256=nwM3aGVbITr-zErIHGxAaJj0gNHMEv2Jyi3x7UhtciE,3408
pyarrow-17.0.0.dist-info/NOTICE.txt,sha256=07TWhvlB92gfMko8Yd0DwYLQfaGM6aSnyPQTgNGLFf0,3116
pyarrow-17.0.0.dist-info/RECORD,,
pyarrow-17.0.0.dist-info/WHEEL,sha256=Wb4yjwIXVKEpht4JWFUZNCzpG7JLBNZnqtK2YNdqLkI,100
pyarrow-17.0.0.dist-info/top_level.txt,sha256=o6YNEP8A-TwT0n3Icw5U4s5nr_n4ICXxaC4EVfz1wMM,60
pyarrow/__init__.pxd,sha256=eC5b2a7fm-SmomDxOM02JS_5jrwnBTFc1tzxA7nLrYM,2237
pyarrow/__init__.py,sha256=VIBPf4AMc2UPC0ZcVuiRyntRwuhPpd3WzDOjdjIsjLM,18323
pyarrow/__pycache__/__init__.cpython-38.pyc,,
pyarrow/__pycache__/_compute_docstrings.cpython-38.pyc,,
pyarrow/__pycache__/_generated_version.cpython-38.pyc,,
pyarrow/__pycache__/acero.cpython-38.pyc,,
pyarrow/__pycache__/benchmark.cpython-38.pyc,,
pyarrow/__pycache__/cffi.cpython-38.pyc,,
pyarrow/__pycache__/compute.cpython-38.pyc,,
pyarrow/__pycache__/conftest.cpython-38.pyc,,
pyarrow/__pycache__/csv.cpython-38.pyc,,
pyarrow/__pycache__/cuda.cpython-38.pyc,,
pyarrow/__pycache__/dataset.cpython-38.pyc,,
pyarrow/__pycache__/feather.cpython-38.pyc,,
pyarrow/__pycache__/flight.cpython-38.pyc,,
pyarrow/__pycache__/fs.cpython-38.pyc,,
pyarrow/__pycache__/ipc.cpython-38.pyc,,
pyarrow/__pycache__/json.cpython-38.pyc,,
pyarrow/__pycache__/jvm.cpython-38.pyc,,
pyarrow/__pycache__/orc.cpython-38.pyc,,
pyarrow/__pycache__/pandas_compat.cpython-38.pyc,,
pyarrow/__pycache__/substrait.cpython-38.pyc,,
pyarrow/__pycache__/types.cpython-38.pyc,,
pyarrow/__pycache__/util.cpython-38.pyc,,
pyarrow/_acero.cp38-win_amd64.pyd,sha256=CYCYB9WHxgynG-PoKQsABnRF9kZlUTtoRZj72WGgEFw,202240
pyarrow/_acero.pxd,sha256=rjCRAq1oo6OyT0c1ISd3_RYOLmo99UEhk88J5Efn730,1484
pyarrow/_acero.pyx,sha256=76KvzLcAUp0NJIZmexnvVGTJefEUGQ-GFDhDiiFQjWw,21909
pyarrow/_azurefs.pyx,sha256=CT699jQ_QQ1Z36CPUFBGzuy6Juq9qOPrYgjZmyy4amA,6043
pyarrow/_compute.cp38-win_amd64.pyd,sha256=2_bxEVCkrF8vyEoRDwgfVJdyUfGLbh1Ry3PkOtY__wQ,822784
pyarrow/_compute.pxd,sha256=Jb3ubCa2FCs635Sk5AzuzJ06Pk7Lm7rEkD_viK2bkgA,2019
pyarrow/_compute.pyx,sha256=f2FqY53e-QoyhIYFVTSc27LEL4heksHwtWFRoHdQnFQ,110338
pyarrow/_compute_docstrings.py,sha256=2KRhcMXk12GS_zihOVWsIjyU9tgPBpPNHUi-GpQPZ6I,1763
pyarrow/_csv.cp38-win_amd64.pyd,sha256=9zuxPnwTQZXFyCVbIyuCZVJ7kBhHH0WMFxculsnmAgY,262144
pyarrow/_csv.pxd,sha256=rI6eCBX4P1-C6wwhRySHK3vWvjy2dorVeAPaSdROeZQ,1693
pyarrow/_csv.pyx,sha256=Dn8E4v2-iHHzEDfd5KPg7e1Wp_6HyGDL9ibgEDzPG9Y,56247
pyarrow/_cuda.pxd,sha256=S9wJXbxxM8MO-oAgWHkblKMdQr9zuGoNfKGkIKVXw0c,1989
pyarrow/_cuda.pyx,sha256=b6xa9mLH8rUC-wavdJJx1C7iXmcF-IzHJyDOYZ21oos,35710
pyarrow/_dataset.cp38-win_amd64.pyd,sha256=g5GZ3y8EsufKmOLZc5_cr4ca0oR1FMPIw8qLllydPuM,709120
pyarrow/_dataset.pxd,sha256=yakY7xxTWXmy6DCnXqxp2BH2X5_LzFb1Aj8p4eRJHEM,5127
pyarrow/_dataset.pyx,sha256=9p4f86QGm5M4xDiDnWX_s1hWvxVcBzUDkyTb6rgQsvk,160422
pyarrow/_dataset_orc.cp38-win_amd64.pyd,sha256=OiOa5DTPNapzL1Hu4S6NYb4609NsOqnVLzoKC8fh8SE,62464
pyarrow/_dataset_orc.pyx,sha256=vgB9AQ_DjKvXddUNoksC2HqtlzLNmi-dqTEncd6DrCQ,1550
pyarrow/_dataset_parquet.cp38-win_amd64.pyd,sha256=4IdZNfMKd3p9fAtqJ9RmvIqR9NNHuIugbFcAL_3rhxw,252416
pyarrow/_dataset_parquet.pxd,sha256=ayCJyRcMOIVVKgtCYF2SgdGKHrTi-jfCrgJcDJMW4Mo,1615
pyarrow/_dataset_parquet.pyx,sha256=Af2HBYwEC5Ywbb25LbSZEy0u32SJXPeQ_SRF2NU3O1k,39969
pyarrow/_dataset_parquet_encryption.cp38-win_amd64.pyd,sha256=YdVR_7ePA8e4jgTpMJ3ATriKsDeMFkpuZ6ueR5UbFEs,85504
pyarrow/_dataset_parquet_encryption.pyx,sha256=q5FVop8IERFxVChOUnHxplGjdVs9nR2o-S47dUARWEU,7407
pyarrow/_dlpack.pxi,sha256=wMZLifeA03bfnmKcbMfkkkBlCi2hhdLo2q6kg0IL6-0,1878
pyarrow/_feather.cp38-win_amd64.pyd,sha256=unhBBht5Ej1F0jPti-Uy6slfE6r5SOFIOba5XYDSuJo,84480
pyarrow/_feather.pyx,sha256=uMhjMFDUvcfTVBaPCoydbmcRtbzP7Rz6C3v7T9bahGc,3890
pyarrow/_flight.cp38-win_amd64.pyd,sha256=22YhhGFxPEvCOjrhLKA6yYapvwvd3F9cIgrcymhrTeE,851968
pyarrow/_flight.pyx,sha256=VcUQF0hD_PyLbFN57hFM5X-bwn1k4G_slzqrgGY8M8g,113781
pyarrow/_fs.cp38-win_amd64.pyd,sha256=vdm3MYOJIvoKu3EDayqjB4RYsefG5XfiUSjJHx5WBbw,365056
pyarrow/_fs.pxd,sha256=GckaT7VHRMB0si-WFHIfpLAcs5XjOLbbD1zSoI3KwdQ,2530
pyarrow/_fs.pyx,sha256=MUZMGQlj3aJ4z4ucdA8qs0hrDft-0we_lrp9QVmW9J0,54107
pyarrow/_gcsfs.cp38-win_amd64.pyd,sha256=-dB2CsDI-kvAk6oh-EKtmdZflazxBccPAzjNOwPPIRk,90624
pyarrow/_gcsfs.pyx,sha256=WPCd1V7cnB87JkM1nmfyywudv6eS6vISp_o9BZaWuQs,9333
pyarrow/_generated_version.py,sha256=28fAbcmjX27Y85XqXFHmJPu2TDbEtDt1QbgWUqhHsko,429
pyarrow/_hdfs.cp38-win_amd64.pyd,sha256=80D71GF3ilRAmLR3GbjUgHHKCXwNWan0ivzjBLS1Ja4,89088
pyarrow/_hdfs.pyx,sha256=2Vey2jd5TzKfDjkxvg_gIGKNB4t6wMnraHsFc7RUICM,6045
pyarrow/_json.cp38-win_amd64.pyd,sha256=QnM2796LBy4PFOLlET_5RP6u-S6lgdxylSyjrM-WsD4,81920
pyarrow/_json.pxd,sha256=jAfiARHEM1qJ2mAuLPa_KrKAbU5YNFLWD5Rhj4P5c1U,1242
pyarrow/_json.pyx,sha256=dLTlsySLmTMmdVOKMqtzDvbVWl72R0OLE0sPlCnAtb4,10170
pyarrow/_orc.cp38-win_amd64.pyd,sha256=Z-Zv1tyI9T58xIQHLKkcKaWnh9dpYcG6r3qsnloet3I,150016
pyarrow/_orc.pxd,sha256=G5jSZUTqyt_stuKn0y56L9lyG8oRqf8NzMtzYSD1qaw,5823
pyarrow/_orc.pyx,sha256=ZfgbW4OQnYshV8nQQCXculGpbmLGsKwzwRwNY59QWq8,16001
pyarrow/_parquet.cp38-win_amd64.pyd,sha256=UCsvaher4xZdDJKty38E1hfxSbgLBXlHck55zePXtCA,398336
pyarrow/_parquet.pxd,sha256=2udwGwlCwb-_cNQZn8KjeuPEcKM7BuTG9aQlUOs1W7E,27413
pyarrow/_parquet.pyx,sha256=YLpvcIu5geLgUkyghDreHuuWVbRJEF_nd3cY35Z9YgY,76166
pyarrow/_parquet_encryption.cp38-win_amd64.pyd,sha256=ESxN7rJpjOWkZ_IveUzj0hfdXkBgTIo0YAj4Ib_56Qc,176640
pyarrow/_parquet_encryption.pxd,sha256=WnrnVuKMmf7MhxDESBulUuWztko4HeNdadkOve_pCLQ,2642
pyarrow/_parquet_encryption.pyx,sha256=49cvRHwQG63kmD86PFXrKKGfGWWfwVZBjHJ94eArTTU,19109
pyarrow/_pyarrow_cpp_tests.cp38-win_amd64.pyd,sha256=azDnFWyWZSPVbBVVzBQn4PVpFA1bbr8MTnjlZhcGXuU,67072
pyarrow/_pyarrow_cpp_tests.pxd,sha256=L_cgXkO1M54NUhRpzHPqLA3-h5qkqTjcxt_OfSlF01I,1232
pyarrow/_pyarrow_cpp_tests.pyx,sha256=cXM3tGxJyLqr-U2oqTKzdWVEsr7d7FLbrSzjw6Aagl0,1815
pyarrow/_s3fs.cp38-win_amd64.pyd,sha256=1PiDahOHOR_Bunt5JICPqmMp_SqkeSMW4O4qyo-iqfA,161792
pyarrow/_s3fs.pyx,sha256=Lx8pQgL9alKsKIgA-3Cn64ZCXgWXFtsCaj7NMEFT0x8,20185
pyarrow/_substrait.cp38-win_amd64.pyd,sha256=No1oD2mHboeUIyiZXcd6buz9FqvcjnBpaTxhtGHFnkk,122368
pyarrow/_substrait.pyx,sha256=xzVLBNsUHFcjGWOB3vuUiqtUd0fEEMtX0sHXxwojLR4,11979
pyarrow/acero.py,sha256=KlT7jPJtPbv5GQNRDCLsjCVQyFZvKJPnl1IMZ9o8ORE,15511
pyarrow/array.pxi,sha256=mSIMX8rg-L4wondDtCldDS5Ke7WSukkguDi4phvvT7g,149974
pyarrow/arrow.dll,sha256=KSAjMlunWCo6U-SdnS47zXaB7jJi9o7W_CY40e9DDrg,29548544
pyarrow/arrow.lib,sha256=FRB_NYqyrLGhL1lKnfFBf5nPnBWcEPUj_141ViXrRMQ,4012184
pyarrow/arrow_acero.dll,sha256=x-ncyVmfFdaJvQBLic_2pSHEInrUnBwQrW7Va-sTlC0,1244672
pyarrow/arrow_acero.lib,sha256=J6yAZubfdZxDkXq4zcQHHJZXS0TW1_r1ekVnBzLg7YY,263354
pyarrow/arrow_dataset.dll,sha256=OOlYpxvoGO_qxVhCBMijHcwEgoYnyEybqgUg6iS6nVg,1302528
pyarrow/arrow_dataset.lib,sha256=59-ZL1KliodVDhGO1fVDoPrgLivjOLPOWNWCUsLcyas,412104
pyarrow/arrow_flight.dll,sha256=fHzaT1iYP6ivpg6x4dnzYdRSfgFQLLyKdPUsJJzhbdc,10605056
pyarrow/arrow_flight.lib,sha256=v6W3Pyjawn3BErNOfJewwhyiybg09WWFjE-YFj6sYvY,358000
pyarrow/arrow_python.dll,sha256=RSRNhXCjAWq7q8VOXTPVPEwWsGS5mcdz52rvS1Xs1m4,1270272
pyarrow/arrow_python.lib,sha256=C3ybjB75N9AwUFtH7K9CIKGjp2WreHeMoft9eeUZ3JQ,174466
pyarrow/arrow_python_flight.dll,sha256=rdmeaQLjVAWAQq3AxT1luHx4fZktdz9HwiwVulWpRBI,62976
pyarrow/arrow_python_flight.lib,sha256=-_LWdSuyXJDCvOn1UnD1vX_AsaEQeR8AikwTopifQZY,58738
pyarrow/arrow_python_parquet_encryption.dll,sha256=z9t4tAwYrPefq2_MrBzwVB81IVOkXaahFruxtk0qr7A,31232
pyarrow/arrow_python_parquet_encryption.lib,sha256=JCoauZeOVTQAFoO0F3hqFi9dAUXgkdzDoP0EA9S0Nis,18398
pyarrow/arrow_substrait.dll,sha256=wy4juFQznauerxtfB3aKjhDyCq8tjDY6B0GBdCKPYQM,2356736
pyarrow/arrow_substrait.lib,sha256=vQ-qz8tV3RrhAUot2XpfHXXsKoc7dx0s1iZNIZQ32mg,111500
pyarrow/benchmark.pxi,sha256=mrrnfpGQcc4Dxw7EOo7vqzxW8NBcQ3DmPIAvp76U4aM,889
pyarrow/benchmark.py,sha256=i-yvjV5wmnxC-IMo1xAjys6mmM2ea1yyys6m7FzJt6g,877
pyarrow/builder.pxi,sha256=BokDKuRrTEEBPA0P2JLw9Ikd_JhAVZp-ZVJaKugdjOc,4765
pyarrow/cffi.py,sha256=xRVBRPsxbd8yd5jE4dk4r_oCUonAei8tLBkUbKP-cCk,2477
pyarrow/compat.pxi,sha256=GGSfRvXAMyzcIu6jr6Gt_a7-a0kied3Sn9BwMY8quGY,1991
pyarrow/compute.py,sha256=vHZZoUHOrV0ropDEepTXp77KYew3P8-OrzMElIEkoxo,23913
pyarrow/config.pxi,sha256=XrfYecOH_SEfZ6GfKF12G6xcpzZ5DGoDplyRF-8G2J8,3187
pyarrow/conftest.py,sha256=hI-CtGNqXDvxPVTQTHA7GdNwpHRnj8x75E9V2Dwgio4,9978
pyarrow/csv.py,sha256=YdN28bUXVQVe03NbWzJxT6P_mf43pv0pbv463MJQ-Wc,996
pyarrow/cuda.py,sha256=NJdOLaIiUFOnTIeRCaxNttBJDqwe-hbSdww5ls5xSJM,1112
pyarrow/dataset.py,sha256=5iLj8yG9X5k7ypCjnp3KMgeGMVhSYxhTtkpB3GHfFt8,41267
pyarrow/device.pxi,sha256=5NLRe8M9tV6yqbq7gEPGsiTfQj3a_BT0UU6_AtDqd9s,5548
pyarrow/error.pxi,sha256=Bl9lR06XhW7mbCz99INvc4Hrc14jlXsGbE4foHPmpqU,9183
pyarrow/feather.py,sha256=MpUtZP2SGLWNf7UUuWOt9iSta3QJhIzFdK-xKxYnfzc,10236
pyarrow/flight.py,sha256=MLoZiSHzbRBjS7CkR_Na-D8LIBBUR9sOQyx8fDr41zg,2246
pyarrow/fs.py,sha256=N8-xcNWCyB2WjdLEYlShniaqdLZGHpkW_GJiIi-0xq8,15330
pyarrow/gandiva.pyx,sha256=5eyKAfCNoBqqvldXDsqqRE_CT3niqreSicAxiWify1g,25263
pyarrow/include/arrow/acero/accumulation_queue.h,sha256=vDho4VxjcBfqaWmDvhpdxRxywmo6CttMD02RI0ul3K0,6016
pyarrow/include/arrow/acero/aggregate_node.h,sha256=5h8y__h3Sy9BnpHOxyGYA74ogu5fheTxDMTrWgy04fE,2212
pyarrow/include/arrow/acero/api.h,sha256=ja6ScTS8T_skQGjBaYvgWPX1MBq1brRlYfghWIBsTTk,1183
pyarrow/include/arrow/acero/asof_join_node.h,sha256=hxPcEiCNC1Ssch8dWu3OmpAmOCMCjWd3wp8VGd7qsoc,1531
pyarrow/include/arrow/acero/backpressure_handler.h,sha256=sv-RN2oUGieDG6c9CP_cMVFcq_SYZae7Cce7ksGeQ10,2884
pyarrow/include/arrow/acero/benchmark_util.h,sha256=UE4Xchd9Euwsj5IWtNGYPRzUk4__ld_46yQdaKy8jFw,1991
pyarrow/include/arrow/acero/bloom_filter.h,sha256=hrRmieRIj_zscZqmk4lrUX6_TmFoLGCF77FURerhJ8Q,12343
pyarrow/include/arrow/acero/exec_plan.h,sha256=icuTf4Vj8rbQvs5XcymSGiEkOsDkvoey_8Zs_HQG--Y,36728
pyarrow/include/arrow/acero/hash_join.h,sha256=5StJUr6114itPdemKA7hBC3IOtSJ3VxZl5VzhYr--tM,3078
pyarrow/include/arrow/acero/hash_join_dict.h,sha256=j5ff2lO6XiusJoZBnPDw7RyuGCoC4rNWuPre5FWLQQI,15682
pyarrow/include/arrow/acero/hash_join_node.h,sha256=n5qkvydn8j3eeV_5q7W_Xm2_FyJDPxmXlLfyeFqkBxI,4463
pyarrow/include/arrow/acero/map_node.h,sha256=g7SnGKKpDrDxJAeLVRE2dYiu1ASWWfKAsXOovFURDRc,2709
pyarrow/include/arrow/acero/options.h,sha256=LczrgYkwcuXQi51OG8v8bYN7G4Cb4ZNytvCfE-Nxywg,38128
pyarrow/include/arrow/acero/order_by_impl.h,sha256=Cel0AcoqvQxRHbVFU0nOoqN_omfw_BsD5sQVlo7m7fQ,1747
pyarrow/include/arrow/acero/partition_util.h,sha256=2qHzYIPSCKFf317EuTivC8G1MVvZZcKD1nHyKjipnuA,7595
pyarrow/include/arrow/acero/pch.h,sha256=SCfsZcIE1gMn4HDG0mcisMTc6AY707D_MurvzJNYnKg,1117
pyarrow/include/arrow/acero/query_context.h,sha256=AK7weKm9V7oC5VMvMoAfD6QbYUH3Gwc4QYw5pext4PA,6363
pyarrow/include/arrow/acero/schema_util.h,sha256=krGNXK-XUVhlYvAsLARhGaT5_P3b6jaWt9DF3hiwtMk,8187
pyarrow/include/arrow/acero/task_util.h,sha256=PCgJOqWIHvFm4xEw0KhSb49syeIGlyC3npyFQGfzF7E,3808
pyarrow/include/arrow/acero/test_nodes.h,sha256=wjrgNEl6tIG207vxFVqIO_HbZ5gmwsViKS4az88BhuM,2963
pyarrow/include/arrow/acero/time_series_util.h,sha256=zRkyQY6_JoXV3715mHGxs2c9a9BM8rDTMbBLF6qcCGc,1241
pyarrow/include/arrow/acero/tpch_node.h,sha256=8RTrew_dsrCb5P0ltZ6CRInrjb9R8GqfH_xlI5e83Vc,2736
pyarrow/include/arrow/acero/type_fwd.h,sha256=ywhZdjTAngHGlVcg9SquUKtC-8pZbZaGPQ-vfnbTaXM,1139
pyarrow/include/arrow/acero/unmaterialized_table.h,sha256=TPnMDPsiwq7qr9IwzqhKwqCYxUKiwKzBdly8xgYmj_A,10566
pyarrow/include/arrow/acero/util.h,sha256=PbDe8kT_eRmyzbFcYw-aqRS_sLKebXC2guI3z54nGCY,6299
pyarrow/include/arrow/acero/visibility.h,sha256=H_bhCt00VjBb0WxuiBSf-htwXUF7GWUmcg7k7rUt3xI,1608
pyarrow/include/arrow/adapters/orc/adapter.h,sha256=B7QodNYkYUns8IZhLsygyLlcIDOm47HNRyqAqUf5Qac,11354
pyarrow/include/arrow/adapters/orc/options.h,sha256=vMxdAhBsxRoLUhi1KduGh0B_Hy9l-JmNZI4QQKNJ840,3816
pyarrow/include/arrow/adapters/tensorflow/convert.h,sha256=PBuoNq4ASqWeVqi9cjrTTBc_MOn_FrFUH2uosRg8PIk,3593
pyarrow/include/arrow/api.h,sha256=HajNiML4B--LKCoUN1SX4V_J8nwoFtf7rWcI4YndkM8,2538
pyarrow/include/arrow/array.h,sha256=cAYOBSZOk_RffGZsl994n2Z1utf0U6Xr25K_UbQ24Y4,2030
pyarrow/include/arrow/array/array_base.h,sha256=e3-aC6Be_KyhvgUG-m8E5t3ECI0dELTK7rDVH-HqWaM,12136
pyarrow/include/arrow/array/array_binary.h,sha256=ONvEd_WjWl2pVBD8fiWUyxAnxLTfR6O6K3GgAq5pqNg,11724
pyarrow/include/arrow/array/array_decimal.h,sha256=1qjmbST9xnxPLs4PIiSQPZWkn-a7USGqGU85XbK-mvs,2209
pyarrow/include/arrow/array/array_dict.h,sha256=_rafDEnCRQsrbAA5TncNdF7qp0z0JOn0bHqaB_2yQwQ,7793
pyarrow/include/arrow/array/array_nested.h,sha256=ar-YqIWrA29LrAB3oVI8r9L0Q4m723t1bOCwdvD236Y,38719
pyarrow/include/arrow/array/array_primitive.h,sha256=kgSpFomUzCtto44RzWpderbmZJyNfLJf_IVajemw-PI,7702
pyarrow/include/arrow/array/array_run_end.h,sha256=9ZsbKbROro2Cj3e-QXJyjoDscjbDa15A3eJKXVxT_VY,5234
pyarrow/include/arrow/array/builder_adaptive.h,sha256=z6xhvrZdME7Uc3Yw092xhiSOnA2RGq_vJQXCRtNOq_0,7076
pyarrow/include/arrow/array/builder_base.h,sha256=RQahyZ-iZjrz_UsCu_Ub0ekIe-WHMZhZ6ZuqlCS_FYw,14094
pyarrow/include/arrow/array/builder_binary.h,sha256=AKb9i7gePr1SuQXvhbqrmlRyLzqDo77mLiI88LEW7js,33633
pyarrow/include/arrow/array/builder_decimal.h,sha256=nnfhyhKch9INwIA4Q3vroo8TdDLU0_QlocCDoLxDA3o,3247
pyarrow/include/arrow/array/builder_dict.h,sha256=LkwssiFvf7ihBLPwwBf8il3GiwfiMQ46FDNzHUVzGMY,28792
pyarrow/include/arrow/array/builder_nested.h,sha256=U1f6CmdoCZkbdSwTqoZXfpVTQ7mQQZRkHnhEA1ivotY,32106
pyarrow/include/arrow/array/builder_primitive.h,sha256=9qPc758H5LdCxdLfxcWDo2qjJy_0NGH7SDnbvQJ_XPY,21364
pyarrow/include/arrow/array/builder_run_end.h,sha256=XLX6GrEjnYcVul_Tp_c3NGProE0OpogU-xeI55nhtlk,11719
pyarrow/include/arrow/array/builder_time.h,sha256=L7-Kb-5xOUYkBp98Y7vBMCPyQba5qXb5oChSgMtvaSM,2614
pyarrow/include/arrow/array/builder_union.h,sha256=QXRVqMhp3oHs-qU6xl0y1-KjrYq2LdG2XbbzGN3tQMM,10398
pyarrow/include/arrow/array/concatenate.h,sha256=h1ARgmXdwWv7yQ1Zuf5ARTlYJ8UZeo1G70VuE00XVSo,1394
pyarrow/include/arrow/array/data.h,sha256=AjfcSIFDyz_7JiUVs5tYwpdtwvMevuDCuBT4gbm96cM,24888
pyarrow/include/arrow/array/diff.h,sha256=vGHW50FpxoMfYMVRvzHcxx3tw-02letyoXkR5yoxgr4,3420
pyarrow/include/arrow/array/util.h,sha256=T48nkLcn4eUkvFWe40eIIBG4ZiB221snra0gACpEIjo,3748
pyarrow/include/arrow/array/validate.h,sha256=9O3KehNl2kWHQwLz6A7izxqW2McmpVcmOE2nz1glSBY,1766
pyarrow/include/arrow/buffer.h,sha256=n53ASBQrtrKMIqDL9Ncsmpm3Zttp_7X4Wpdlmor0L98,23679
pyarrow/include/arrow/buffer_builder.h,sha256=N678fzVzUCBXJL-vLIYmwECm-0Yx0crNmuaEhzHZJLM,17855
pyarrow/include/arrow/builder.h,sha256=MNcxVdcYID5LCq_U59nwuObUVshQiEB3_5ij65s8JVI,1579
pyarrow/include/arrow/c/abi.h,sha256=mWwxc7znJ8OLzXYMFGhNYJDiWu8GIoNwqIDsi_gsJ4Q,8108
pyarrow/include/arrow/c/bridge.h,sha256=SpEK5TrGvwGx_x7s_tLVzftO5UnCDl-B6EaBDLkJqeA,18541
pyarrow/include/arrow/c/dlpack.h,sha256=1pvKCU6HiHs0RFUYLbsU8iQu4ALce0aE1I9D3sqKzPE,1868
pyarrow/include/arrow/c/dlpack_abi.h,sha256=8QAPIpADH7bOkgcAu5V4np2r7Hgr4mrf4sYNk_6CMws,10221
pyarrow/include/arrow/c/helpers.h,sha256=K87ufXnopM9P9KLlI2r70-E35F-RnaVXV_bzZWtT0-s,6457
pyarrow/include/arrow/chunk_resolver.h,sha256=cpEd4lQXs92LKvHVrOk5XlZYyQPBqDGFsw5UDpxo70A,12258
pyarrow/include/arrow/chunked_array.h,sha256=xVA95SNPpWI0HTIzL-PSnjCLtLrBSXGuzKuXuXyty_E,10603
pyarrow/include/arrow/compare.h,sha256=4c1ulUqzm4gqkh8Rz1i7Iydgg3_vZmxSvfiIg1pvj24,5700
pyarrow/include/arrow/compute/api.h,sha256=qcAMa_sJ1XIW_j6JA3HHQz8geNBDvWBtHwYNV1nQkBY,2124
pyarrow/include/arrow/compute/api_aggregate.h,sha256=xDizGHmwCGOpTxa1-eq7njauaL1b7aC8XIEhAeBu0Q8,17639
pyarrow/include/arrow/compute/api_scalar.h,sha256=5VNy-n79y_0V_qgMgvyrR8x1CJ1nrbWWL_0nHstUOJ4,68262
pyarrow/include/arrow/compute/api_vector.h,sha256=9vEfoKaLMkc-Q8oFuTENwq_csgr0E4p1OgsxvcZJjsM,29831
pyarrow/include/arrow/compute/cast.h,sha256=aCqOeB_N7h2qGChv9yAi9NqUZ_33FptkCoIFCmOieog,4379
pyarrow/include/arrow/compute/exec.h,sha256=P_A3UzDgQWz-kxSK_iSBI7sNpkK_7Q5IZy4NtL9qV-E,18458
pyarrow/include/arrow/compute/expression.h,sha256=mL4N8rcRrz7XCm_x92BGo4cFMn3_j1ZR0CM1NworjzQ,11479
pyarrow/include/arrow/compute/function.h,sha256=L5oWje4X82sATTQrd2HL4Db1-gg6DwH8zOyw0odnYFM,16754
pyarrow/include/arrow/compute/function_options.h,sha256=FnCqSjeZG13AUwDx65pOj8i3ZbHPhzL4hcpyRgpB9Gw,3169
pyarrow/include/arrow/compute/kernel.h,sha256=9BEXBwzW4v4SEufE0APBHped4qipAS1iEtB48dsQtDU,32110
pyarrow/include/arrow/compute/ordering.h,sha256=ja1S3q-ywF2y02-5-jPcG1KVNbx_qLRJ5Q9NIr8mxco,4249
pyarrow/include/arrow/compute/registry.h,sha256=kZ4rf8OIM0F-0wlfO66KMIDctZpDdt9asfCDt-6tRcg,4963
pyarrow/include/arrow/compute/row/grouper.h,sha256=HYVOXnhoDUEyp8rpyMmIUBhpxtygr-DvRvzzCbBID_0,7177
pyarrow/include/arrow/compute/type_fwd.h,sha256=LIJjpXcGor3HhT78jevXJ-WtLedokkR0GAoF6TCCC_Y,1595
pyarrow/include/arrow/compute/util.h,sha256=9dzT6937S_i_o8Bny4JhnDBCre131rU_sHuOj5W0S2I,9384
pyarrow/include/arrow/config.h,sha256=_-TrLaku5IaDU8KR1K3Y1gmLJKhHv6teKgZHIFC9nEk,3142
pyarrow/include/arrow/csv/api.h,sha256=mrySGIM81ziJhlG3AutB28biUs42PNvpERB63vFfXQM,929
pyarrow/include/arrow/csv/chunker.h,sha256=KYhQi-hLLmFIhC3BRfZRbIOpZMsTbnWHS08jy4tcCl8,1207
pyarrow/include/arrow/csv/column_builder.h,sha256=tvZJ71f-fylYtrEmkWwIxgb_s2WLiUnPwyBpBLCHdjU,2968
pyarrow/include/arrow/csv/column_decoder.h,sha256=1Mfwma0yKt5cYVQ-jb4kaHk6VzrnyrI5Pg397R-Mm3E,2422
pyarrow/include/arrow/csv/converter.h,sha256=eBnvOyrqWZ7bexpZc35jBFouaVFFn0NyaAM0P0chcfQ,2871
pyarrow/include/arrow/csv/invalid_row.h,sha256=QI1O1M00RItEnFeMYTvJCUOQ88n-GyO0IantTDu73T4,1944
pyarrow/include/arrow/csv/options.h,sha256=xmUJXmLQVePc7alEXt1vaWWVEU2HpdDOr1SyzvqrgAk,8200
pyarrow/include/arrow/csv/parser.h,sha256=rT4dfOfzKBFAbe5WQmrWtKbZ_DtFRGN4VaMcHhHVc_Y,8844
pyarrow/include/arrow/csv/reader.h,sha256=njP5lhKW0XptbU_Zz3LakWZdvrg7Eko2N9i0vKEKBcg,4718
pyarrow/include/arrow/csv/test_common.h,sha256=dw0MWN5occOCyrIoIOzppsWMY83Br2FMgTDGcWC-EFk,2027
pyarrow/include/arrow/csv/type_fwd.h,sha256=udWl775X1Yx5QBgfmXluDDmgI2NStrjmur91H8FNvg8,1012
pyarrow/include/arrow/csv/writer.h,sha256=9cBNYlJRIdqJ6RlWhjpbVq0pBPVwgIdkmc4up9Cah3s,3639
pyarrow/include/arrow/dataset/api.h,sha256=vpz2rNX9Xtk3XAeYP0kmByCi1dcYqFEeenJrIPsQGic,1353
pyarrow/include/arrow/dataset/dataset.h,sha256=7E8LC3H6_oq3bPUNjDtgsn2y6GRIjUJTVF53cUDCK24,20311
pyarrow/include/arrow/dataset/dataset_writer.h,sha256=eq88v7MOtVt44fg54_1bo1vzdz8v4RPJGuLVY_ai1UU,4692
pyarrow/include/arrow/dataset/discovery.h,sha256=W1SwsuLp-H9IzogC7Ew4_DbatXPH0AERyY0uzDVbtaY,11511
pyarrow/include/arrow/dataset/file_base.h,sha256=62StSlXAvcHfXU8bBbz_8v1RgkIorwiUsG_D9z091MQ,20698
pyarrow/include/arrow/dataset/file_csv.h,sha256=MCt6eRH9CKrfmMPmZUiQcJePXAT8ynI4qeLcnhV6pHg,5160
pyarrow/include/arrow/dataset/file_ipc.h,sha256=FaCnXdg2YihUDxcdBoCvSmA-MTk-VKhD-rN5T2WqEkU,4206
pyarrow/include/arrow/dataset/file_json.h,sha256=JH-9JGSg351WTLG6ET6YCQ30w-Kfcd1Uwa09nAQX0LA,3621
pyarrow/include/arrow/dataset/file_orc.h,sha256=i5In5ilw82h0wil4IbSJpFfSU239_6bFsZZAx3e4vQI,2527
pyarrow/include/arrow/dataset/file_parquet.h,sha256=3eER9_1QPIQmJWElYxcBrgBhySxSbfzm4pJI5cC0oHw,17112
pyarrow/include/arrow/dataset/parquet_encryption_config.h,sha256=8wPZEYEs9-95TaL8hwZcsAmmzldvg20lqBFLT4YMwNA,3500
pyarrow/include/arrow/dataset/partition.h,sha256=P9uWiM2sCk8LWEl3vfzxBwDZHdKGtjehzALCctBq3As,17247
pyarrow/include/arrow/dataset/pch.h,sha256=3vmHGXlgxa9tBmj1KBA3bJxV1dy_Vp_32vQDeivp5iM,1221
pyarrow/include/arrow/dataset/plan.h,sha256=e3HxDV55B9606UzmQfJOchsZYvxNjjSRW1sSeqRdA38,1214
pyarrow/include/arrow/dataset/projector.h,sha256=Gl7qon2MGuQdAXFC_VL7lDmekOoaIYpkDbqvhHaOmtg,1167
pyarrow/include/arrow/dataset/scanner.h,sha256=-_w1EQO07v4zgIdV0JTH8hHoG685zYw8mFzpesPypME,25181
pyarrow/include/arrow/dataset/type_fwd.h,sha256=3E2qO5bZAg-AAtFGZGGpWP_jidXMKyxgsZL7KhlquvE,3283
pyarrow/include/arrow/dataset/visibility.h,sha256=R-pIFvth6L8hVVOgZAi35xKZFmjnnLmpa-ikB8nEmug,1578
pyarrow/include/arrow/datum.h,sha256=GVovTOIXiD3KCISaqs_-o4HmlOVPED4fVKxkGb08aUs,11727
pyarrow/include/arrow/device.h,sha256=3q0tZRKznF1ll2FqL3ojPGEVB9RUIrFcnp5osrlr4lc,16093
pyarrow/include/arrow/engine/api.h,sha256=fgaw48YHO1VT7cqs5htHz--cpk4Zh9nAsn23ySIMx4I,908
pyarrow/include/arrow/engine/pch.h,sha256=SCfsZcIE1gMn4HDG0mcisMTc6AY707D_MurvzJNYnKg,1117
pyarrow/include/arrow/engine/substrait/api.h,sha256=cet1Nuy9YHB_TC87wqBbljw95HcuOABH9cBumBtrhLY,1105
pyarrow/include/arrow/engine/substrait/extension_set.h,sha256=XK9PFvRhyYUCR5aiOjnpDimCDDLeQEd_PNnGLJXW9rs,22033
pyarrow/include/arrow/engine/substrait/extension_types.h,sha256=XuG6iwVFe8MtMGSOyAgL1HnPrHOS3O6Nvcf79Ew4zr8,3165
pyarrow/include/arrow/engine/substrait/options.h,sha256=pYixPsZpfJoc_2DK6ludyaAjZtGsWIh8D4VQ-UEnREY,5955
pyarrow/include/arrow/engine/substrait/relation.h,sha256=YwP9-cQSQXp6YbHN5tuO-KgiL1Pu1GRhUAAlV_9V2mo,2456
pyarrow/include/arrow/engine/substrait/serde.h,sha256=CDi5lFowebB2mlxr8JvfpgNFV2x1fiYiXtrEf5O4QSo,16859
pyarrow/include/arrow/engine/substrait/test_plan_builder.h,sha256=rb4YPCPcFnzYKBX3q0Ar-ceTdtGwFpkOqCK3IsJn4e4,3079
pyarrow/include/arrow/engine/substrait/test_util.h,sha256=TGz1HkUVePC7x29UW1sqc2mP1F49n4kGVdTeLJab3gQ,1562
pyarrow/include/arrow/engine/substrait/type_fwd.h,sha256=aUfGCwouyEBJget9lsYmu6H_Je2AmKG_w0R7n18breI,1060
pyarrow/include/arrow/engine/substrait/util.h,sha256=CacFeBx3Zb_tuS4lL1Oo6Mu2xbH9VpMxEWavuw2HQh8,3653
pyarrow/include/arrow/engine/substrait/visibility.h,sha256=bD-ZKB2Osii0g2hSS_XTmUyyXFB68zJ2eb_DXMgeCrs,1734
pyarrow/include/arrow/extension/fixed_shape_tensor.h,sha256=zN884peNGEHyqO5HxMW3NT8yTShpQ-CTfp4Q_6W4Y5Y,5724
pyarrow/include/arrow/extension_type.h,sha256=OtLG-6Fb01aRQFbpTkfO_OUeAm3JKJ3iD9uQzCKxIYQ,6796
pyarrow/include/arrow/filesystem/api.h,sha256=23lICR1xlVH01tx7vWkfOpqysE7Xm0aEltBHDMaMBv4,1411
pyarrow/include/arrow/filesystem/azurefs.h,sha256=5pjLj0vpSMYzCUt8NBCoNVi6cV4qQlZZXG_MA0UvLzk,15474
pyarrow/include/arrow/filesystem/filesystem.h,sha256=Qo3fHPF9RjM5gFMBqsQ0rsMnMwXqCACpzvq_PtboDhg,30308
pyarrow/include/arrow/filesystem/filesystem_library.h,sha256=CiZ55rYBcdvZltIjjbre4f-b8OlSZ0Xf_RMIYRSYgqI,1764
pyarrow/include/arrow/filesystem/gcsfs.h,sha256=huyEUsU_piNZI-BCh3e5R1pfHSGvoEdZHt0bbI3wMFs,10779
pyarrow/include/arrow/filesystem/hdfs.h,sha256=aewVrtV752VmzKUmbXsV2WidwyFuwm9hFpGQPghUMpg,4250
pyarrow/include/arrow/filesystem/localfs.h,sha256=xRwJh4SNFE5p5ZEHolvOH5YrCZoUWXfk68YpR3gr4Wg,5104
pyarrow/include/arrow/filesystem/mockfs.h,sha256=mPQsmYNbOHYIpWeqnJGsUWROxqagJyxjYaDgHH5NLc0,4902
pyarrow/include/arrow/filesystem/path_util.h,sha256=r7xAj9YOEnkxmgACJTFHjjp21brC2cg5oF4NDZeChy0,5876
pyarrow/include/arrow/filesystem/s3_test_util.h,sha256=JDOyoemtw-21n0UNJ7Q7GsGQLE-JOTO7pNNQ-dy_UWQ,2868
pyarrow/include/arrow/filesystem/s3fs.h,sha256=GOhUabJ3IeSePQHXZ7_JOUKVSZ6tQHk5bokxeaKRQVw,16069
pyarrow/include/arrow/filesystem/test_util.h,sha256=yxwK8yUS-82wHCaMJITgyqes-lb1YRrHGNR8fEX71Hc,11440
pyarrow/include/arrow/filesystem/type_fwd.h,sha256=Wa3zB9amkp6XJARokB7oVDFUadSK6hdsc9wRlswnqms,1515
pyarrow/include/arrow/flight/api.h,sha256=qsYeE0gW9Ja7_AiuRPcSjPG9Za8LhvnIx8xNEgGXJw0,1287
pyarrow/include/arrow/flight/client.h,sha256=xQknZ10AdnooMBi4U5C8hfM7SPc0wsZxSPRG9XRpwAc,18222
pyarrow/include/arrow/flight/client_auth.h,sha256=S8w0hGHthx5trhh4Hy2IIUZhlMv-5hadI9vZr61caDw,2278
pyarrow/include/arrow/flight/client_cookie_middleware.h,sha256=1fsK1K4nsZya2Lcu7ObLFmy8MySWAndeOLqbtELH33w,1237
pyarrow/include/arrow/flight/client_middleware.h,sha256=qtGBiIz-xPDGTXqywbFkpnDHI1zMbaGzg5nAFPt3axA,3026
pyarrow/include/arrow/flight/client_tracing_middleware.h,sha256=htiyzC8DGhxUxP4mT2BtC2Z7YbyKIEOPMshLh98W1MA,1251
pyarrow/include/arrow/flight/middleware.h,sha256=D-QPVX66oYEtTqlhvkoKwwXJOkl46CWQ78QHMvNSEio,2329
pyarrow/include/arrow/flight/otel_logging.h,sha256=bj0_NNe8Ha-bYOLdqL3lVutfekmp4ic9QE5Md-ESzHc,1166
pyarrow/include/arrow/flight/pch.h,sha256=Dd_7inDS5gHboIRsPdHm-CdyiyGfyaJWs4A8QIPZlG4,1218
pyarrow/include/arrow/flight/platform.h,sha256=rPiik2bUsHHIQET6loV2jyToNOnznD7U9mk__cDEqXs,1238
pyarrow/include/arrow/flight/server.h,sha256=Uid3mgf9cpE6BoNHq8V2CtbvKvnGFh5DI6nz_0I1o8I,13512
pyarrow/include/arrow/flight/server_auth.h,sha256=7eHHEWnRYSyYzRBszpaRGKiaV2HvOG4lT1HatSXd3JE,5554
pyarrow/include/arrow/flight/server_middleware.h,sha256=LYT0eXlnFdoyym9O7ftDPoOQZra7FfKouCAFSJ-LgJY,4421
pyarrow/include/arrow/flight/server_tracing_middleware.h,sha256=4Ovp-oUJe9MT5bM_mlN9MoYLXB_fySwUTy7C6kw9k_Q,2254
pyarrow/include/arrow/flight/test_definitions.h,sha256=qWbmwGsj8f2vJKEw_dYkKRARXwJj0fxtGXMKW_4X4ls,13339
pyarrow/include/arrow/flight/test_util.h,sha256=-YnJgUFKZK-xvDl4UO7jisDnkIHyZUuiNmxm27r6AZ4,9684
pyarrow/include/arrow/flight/transport.h,sha256=PeBIjWQuc4oaeHG6OytcrbCS0kKuZYV5PFyr8WUELf8,12479
pyarrow/include/arrow/flight/transport_server.h,sha256=u_0y80TFC5hbZ2TKU9FD7UKbvaXZxf4YbzkyPnBlQRs,5401
pyarrow/include/arrow/flight/type_fwd.h,sha256=1dpmGmV-kYZFcGJSt2_B7yPUHRKeTSgEHz_6sEZaAew,1862
pyarrow/include/arrow/flight/types.h,sha256=gwt_HTYiSM7iQGokDQ4SQexyLenw9RXXfZgA-4AmvGg,42015
pyarrow/include/arrow/flight/types_async.h,sha256=gTu4fBjhT1vcIBzV8U_4N4I4cpjJjtcvzoDHMKI1Miw,2675
pyarrow/include/arrow/flight/visibility.h,sha256=YVJnD0qyQloY0Oy4oxWCaOtfdHBTcLHabDMMrrZLEJw,1586
pyarrow/include/arrow/io/api.h,sha256=Dv-t-VQoHXkWlCaBqhV4JNRpbr5VPPSkidR3OuX64fQ,1021
pyarrow/include/arrow/io/buffered.h,sha256=F_RN4E9ctK86Jq6feBs2oDrR2C0Wio-K_CqIjBZOzts,6012
pyarrow/include/arrow/io/caching.h,sha256=jzGdwSoJa6xM0g2pFC4Cy45k-qP7SU02GAMH8A-eDE0,6865
pyarrow/include/arrow/io/compressed.h,sha256=54vWktZDHbUgxil3pxn40RbcEE9UXCCXmgUOXAO8xrs,3898
pyarrow/include/arrow/io/concurrency.h,sha256=rDbsAE2oDU5I9-QOMRNL57h0MLqVINGS_Oll196m8ZQ,8223
pyarrow/include/arrow/io/file.h,sha256=mud1ILk2zK3C_jap1m0X3SWa2t_fJKi_PIDIYIkWQnI,7846
pyarrow/include/arrow/io/hdfs.h,sha256=_aRZhApwTn9FEUzDGM0eAjt358DIQhxJjHDtIkv3Adc,8843
pyarrow/include/arrow/io/interfaces.h,sha256=knb9p5UmNQlh59Pu0RNf9Vb_-H_STZdxOCCTg3P89uM,13790
pyarrow/include/arrow/io/memory.h,sha256=BbBbrj0gckpb_ZgQpGLImHQE7vc_LC9GGJsl7MA6yc4,7261
pyarrow/include/arrow/io/mman.h,sha256=pQ1W1RONae-hoWai-h3JSYrWodOS03h1JWEPDlEqYGM,4278
pyarrow/include/arrow/io/slow.h,sha256=E0RbN0tQQRJRIUgM--NznPmI6fLH0bxnWct1jsn370o,4060
pyarrow/include/arrow/io/stdio.h,sha256=FhklhNBsYt0fZZKBmgBfMJ_y_QPcEBkD62KQqedy7Qk,2177
pyarrow/include/arrow/io/test_common.h,sha256=XUeJfeGQ_TCo0vyp0LrLtmCVkB6rCtU_p50ECeIG3B8,2179
pyarrow/include/arrow/io/transform.h,sha256=99VKVlOO8M4HF5121bJ5iAyi0eUMqg0ndafvZ6F4y_M,1950
pyarrow/include/arrow/io/type_fwd.h,sha256=ebFvHA60xc-jpcNt7AzadAzwO-LWmFXMx6AMZMgtcIM,2392
pyarrow/include/arrow/ipc/api.h,sha256=XX2g6bMVdnN9pup7-ad7YI876hxfEOPk_5KWO_TM0w4,1032
pyarrow/include/arrow/ipc/dictionary.h,sha256=RWQcycPKBl5aREIPi90Yd8jPqLcw6ioztlHAHpj1W0c,6281
pyarrow/include/arrow/ipc/feather.h,sha256=LcTc9nldeLieMbtMidRgdvM9X25X4hEaVjZOHAYeofI,5068
pyarrow/include/arrow/ipc/json_simple.h,sha256=GHLMKo5y6d25ysYJEeEKInguimCyl_md7HFZ3Yl-x7I,2526
pyarrow/include/arrow/ipc/message.h,sha256=sAAyRIrgsRX3L8WMoQ2YqBg_mkBaUazvPjaR3nKYMnE,20576
pyarrow/include/arrow/ipc/options.h,sha256=Bs9rSLnjM3YU_O1zf1ii1_kv2YIEC4uR9YmicDTYH8U,7066
pyarrow/include/arrow/ipc/reader.h,sha256=mV4_2RaFhhQFUKfrOQtbrYpFGT-qYHKGxiLLIUhipUE,24744
pyarrow/include/arrow/ipc/test_common.h,sha256=liNj_qIlWtRQYI9RS-OGiVi9r_GE4Z-opglFLHofPoQ,6374
pyarrow/include/arrow/ipc/type_fwd.h,sha256=0_J9-Ph_UwBFvwOLRi2yxfVp0Ir3IeLY5YV43Rn6cuk,1508
pyarrow/include/arrow/ipc/util.h,sha256=xJ1KaQe_wnYd9zfzVgJlLgGTVQQqxvb0xnZYutiHkfg,1455
pyarrow/include/arrow/ipc/writer.h,sha256=tLFVvlkKzo55PpYELoiEqXGRolGj2rRUknLTQhaTyZM,19345
pyarrow/include/arrow/json/api.h,sha256=QD-9FK6Ad10h03vRS5PvQWBX3jn1TQ2pTBhLv3kSRm8,900
pyarrow/include/arrow/json/chunked_builder.h,sha256=F9WcNFIXy_q9_Uc6-c37RvGgDFVPGo6bhh_OYIyPU68,2433
pyarrow/include/arrow/json/chunker.h,sha256=HPPfHXfhjAj8rtU-RmseUhEqzKtwP64UQdiikMw4jAg,1154
pyarrow/include/arrow/json/converter.h,sha256=0Iwxxsr0ACxdQlpmFujSwVeF43N3RhZ_oSrea9YV8M0,3228
pyarrow/include/arrow/json/object_parser.h,sha256=M-KYzI5UnXpMHVzLhFAcARHmX_E0Py56v7hgVHKfxyQ,1681
pyarrow/include/arrow/json/object_writer.h,sha256=Ad90l1v7OAkkyGiAP2SqTdqyAqG8GmUpz8dxn4bYIdw,1477
pyarrow/include/arrow/json/options.h,sha256=pQRNifTwZ63y6pOBumz6Z8IWHXDUAUQKBPS991Kou7U,2301
pyarrow/include/arrow/json/parser.h,sha256=uLkYKfAOeTDhHgM7FTlSdVxUWne0t39CdgBgHwlZeks,3490
pyarrow/include/arrow/json/rapidjson_defs.h,sha256=Yze4FHiC1DXZsWFSM5D_IPCQhxALR5ELSmE7DR_CRwY,1511
pyarrow/include/arrow/json/reader.h,sha256=3RyTYLnpcB9ceSGP8GoKWXqd-ZHZFXto2Fdf0nZ2ul4,5330
pyarrow/include/arrow/json/test_common.h,sha256=5Ejg6ZkZ0MdkX_-UJiKisM7evoNi62SolnDteknG2ys,11204
pyarrow/include/arrow/json/type_fwd.h,sha256=FWkyYf2wb91GygRRLXTTIFvvoztO1hfxNDq8TtivOWs,968
pyarrow/include/arrow/memory_pool.h,sha256=1JFtQF8U3BP8168oxI-kLBGLLSIjP94t413s6z-35ms,11360
pyarrow/include/arrow/memory_pool_test.h,sha256=tK0L93rXajpMlwHuhd36chOUMMeC4X7p3HiZ2GeXI2s,3461
pyarrow/include/arrow/pch.h,sha256=09MmEJKJaxyGuyDLA8Cs2uDcK-6oUa-OFwj9hLnihiU,1316
pyarrow/include/arrow/pretty_print.h,sha256=xYwSPO02dCLPT1YlHgA_1T0FWfvhHPLG-E7y_fjnarE,5686
pyarrow/include/arrow/python/api.h,sha256=biQIS5I3je_V8G078KUnuhQfdUp1lYZSkqlrNvm9I_A,1252
pyarrow/include/arrow/python/arrow_to_pandas.h,sha256=N-Bl98Z-Y7OrkYzI8pJ5bhR6i-HoCom-Mk1sYCosRAo,5707
pyarrow/include/arrow/python/async.h,sha256=15MshOaSHLSsfKlQw4LCY338khPideC74IpLOFCygDE,2412
pyarrow/include/arrow/python/benchmark.h,sha256=OqCsqRe5osY2lhe2ecN8cXrdkmSFgLp6fXcf4_N9Fik,1228
pyarrow/include/arrow/python/common.h,sha256=k24Fr3kOWfeygcXp-dFrNTSKXdEZVgTybqZnqfNJt_U,14870
pyarrow/include/arrow/python/csv.h,sha256=7yixEpKzdR_wvjfG0rlkmtYpSyjEuNb2cPLueyKFa0s,1439
pyarrow/include/arrow/python/datetime.h,sha256=LRUq2ByJ05aoQl3wh6KSktPKW7AJ_wNVX-QVxTprB5Y,8158
pyarrow/include/arrow/python/decimal.h,sha256=BAt3laodbJkc9CsIEN0Qi1l2Ra9Fuznv1wNc5d4r8gw,4854
pyarrow/include/arrow/python/deserialize.h,sha256=GWCwSyz_ldSfaDz9ObTs5sXom09iDntaMCB2W3Ow5Ss,3995
pyarrow/include/arrow/python/extension_type.h,sha256=ovyhXsE-iornOx99_iOmQjZbdqIIpzUfiT7dTvVjQro,3266
pyarrow/include/arrow/python/filesystem.h,sha256=bnXcXSssY90VwD784m3CqQuNnZdwZxKpc8MltD4zJyY,5256
pyarrow/include/arrow/python/flight.h,sha256=CV9HoK-GlwD5sCCn2YVByTfbUET0cRghn9GQGpLLlLI,14619
pyarrow/include/arrow/python/gdb.h,sha256=VN8aDtc1iFPIKsjFQ29RKQOkClvKAAI_KeTy_LFZcZg,1001
pyarrow/include/arrow/python/helpers.h,sha256=uBgiOl8b-gklCa4AxKOA-8HFZkKN9WQy7n1Pa0qIAA0,5651
pyarrow/include/arrow/python/inference.h,sha256=_44xuq5q-qktVBIigUbwmlgzOMJ4vHuauXQu-pXG1bg,2102
pyarrow/include/arrow/python/init.h,sha256=Dfdy6kMtFHc_NW6EhionR0ZWr-S6XpaICdu1PzTl-E0,974
pyarrow/include/arrow/python/io.h,sha256=byOtFjW3NuetpJIErXFG62bsim0ZsWIAPgbes_JvI3k,3979
pyarrow/include/arrow/python/ipc.h,sha256=lHD6Yl4f_ARUIK6SlA4Jy75i7_K940NIGg6vHauLypc,2331
pyarrow/include/arrow/python/iterators.h,sha256=7JRvhoQL0V7lOPGxhCk_1j_wUl_Vc9jDIOf5nhU1Yig,7387
pyarrow/include/arrow/python/lib.h,sha256=_XgqZ_3T5W9PqqlxWG6Mkekxgy-j7YV7LdBBQzpc6gg,4631
pyarrow/include/arrow/python/lib_api.h,sha256=3YSCQN-GF_-nWfkpEkQmR1Q-Lrk2XZUqk8o1_ttBkgw,19487
pyarrow/include/arrow/python/numpy_convert.h,sha256=JVUlnUuzWE4OwsoHPl4hvzi5_nkDjHR_wO5Iu8ZEVkU,4992
pyarrow/include/arrow/python/numpy_interop.h,sha256=Spr_TZme4Bb6r22_xSXrQyy9YWQY9hc9AJQlCeUgOTI,3495
pyarrow/include/arrow/python/numpy_to_arrow.h,sha256=-JhqPEg-tOBKhwRDhurxrAYQ6I-ws4CoghILzj9R-ms,2832
pyarrow/include/arrow/python/parquet_encryption.h,sha256=OhPqZ11MVJ12pQWA2CzvT6pP4s0wYYE2Tg_AL_zKT74,4951
pyarrow/include/arrow/python/pch.h,sha256=cuGs6XHl1WaiL-RuVk8stRK6hquHo5mjD6z0QsGQ_uo,1153
pyarrow/include/arrow/python/platform.h,sha256=2C243DF3SvJD7Jlb4D6_oTNtXV_qFRgsXTMBRcEQU74,1447
pyarrow/include/arrow/python/pyarrow.h,sha256=Pt2advP3jdTBRb5y2TtyDLVBACieOKI2IZblGu6kPMc,2850
pyarrow/include/arrow/python/pyarrow_api.h,sha256=uPrpVqzif4NlQkJvj-tZCIeBLBLSNE7ekGjKlCPCZxw,886
pyarrow/include/arrow/python/pyarrow_lib.h,sha256=D1QHgdorjjbTko9iMJLaFwykjnjIRBDLyuQaskohSmw,882
pyarrow/include/arrow/python/python_test.h,sha256=kdEUk3TAKggBdER-tT8y-fIcO2UnfEh2K8XFrnDYLYk,1237
pyarrow/include/arrow/python/python_to_arrow.h,sha256=r6iWOjXDn6gO6Qxo9mAlnm6f94jdAE8EmW0D23UJgac,2601
pyarrow/include/arrow/python/serialize.h,sha256=0aG_RcujeI5nSeUhGSRCIAmPFgCjsUimY1PV0EK44tQ,4540
pyarrow/include/arrow/python/type_traits.h,sha256=bhn_U-tmE9OX4GEJfFqz41Lf3tBwQPfl5wB4X3BHFQs,10443
pyarrow/include/arrow/python/udf.h,sha256=gLQGE1vYHlYJ_ceN1Kb0upw6cP90juYZ4b30VvuUPPM,3185
pyarrow/include/arrow/python/visibility.h,sha256=4FcBJfEIEsf7s36wakgQL0UK0tFdHDckk3oWrOGF0eA,1378
pyarrow/include/arrow/record_batch.h,sha256=oFyL015_GLHjjFriDvQyxASBTzCzZEet1uTJHUPf0n8,18288
pyarrow/include/arrow/result.h,sha256=33tiy9vHezPnzKsKApq5ZL2DN9nzhP_5z5xdo_q9Ge8,18247
pyarrow/include/arrow/scalar.h,sha256=KzYzPhqdheRWBOIOaGh15QREtlb6B7CG49053WxUpQc,37336
pyarrow/include/arrow/sparse_tensor.h,sha256=wlyVEvtE_a0ERgsR7OHRt2VqOP2Y6e-tbe2nkqbx1p0,25822
pyarrow/include/arrow/status.h,sha256=PeMKl0K9YhnjSO_v_VwhnYMCnOnIPPwPCrqtaNKqO-M,16858
pyarrow/include/arrow/stl.h,sha256=jQo12sZ3e7sbnuDwRNRh_1kJiZ825Lcfsa54bxtlowU,18626
pyarrow/include/arrow/stl_allocator.h,sha256=L7G6TMjgleD13a4fl1cC9VyXUDQ-2fkXe54gf26PfIU,5120
pyarrow/include/arrow/stl_iterator.h,sha256=XSt7wFb2Yy-hoDVebEIX7sCQyTGFChDAVNyy8CkV6mo,10267
pyarrow/include/arrow/table.h,sha256=hXW8OovSUEIazqfs5QW0KwI2fjCQTsoFSoR7Af8Q2b0,15016
pyarrow/include/arrow/table_builder.h,sha256=kTA3F-72RoNR_YZNmxQmatxC2cUW5feh-cvJEmi0ZQ8,3870
pyarrow/include/arrow/tensor.h,sha256=6NiMQ6l6TiPCze5vIVgbr_3QibZXqd2a8XU2gs2fuaQ,9343
pyarrow/include/arrow/tensor/converter.h,sha256=BTiYEWpV34uKMP1hgVxdoeyBlh-cqpmVaaBeIc2ARig,2958
pyarrow/include/arrow/testing/async_test_util.h,sha256=miNSGn91JvXE1NojQusPWGTINiJkk5D5xurkPNwyBWk,2341
pyarrow/include/arrow/testing/builder.h,sha256=MV0S4sm51CGu5c4HA-90PeZlm-HrVfpJJpSuSZhMxto,8787
pyarrow/include/arrow/testing/executor_util.h,sha256=3WdGxQ-F5NfkJVCR0jv7hYG5k0YtdXuOoxACY3bmY7Y,1940
pyarrow/include/arrow/testing/extension_type.h,sha256=4rOtI9QMhl-lajX22tHgbdiJGEIUI2N91AVN3P1YFO8,7639
pyarrow/include/arrow/testing/fixed_width_test_util.h,sha256=bcBgIZw5MhP7JeqHA39Xqu6FBJPoqdKs-89I-bQAocc,3167
pyarrow/include/arrow/testing/future_util.h,sha256=CyeXP1q0nDBKhFD2JpzHFOKhWhLa7ItvQ6OdQgm82Zg,6388
pyarrow/include/arrow/testing/generator.h,sha256=5pqCw0oF1GnTbA3HvR4vpqYEob0BdZNa2uyO011eF8M,12345
pyarrow/include/arrow/testing/gtest_compat.h,sha256=bKnz-VP8y7QTV4Rqa3I-hWY-EfBqO8CwPrdN_WgGE7o,1332
pyarrow/include/arrow/testing/gtest_util.h,sha256=Kz13k6BHVi_k6v4peyV6B6FESnOmSWckDSlvckBbARg,24913
pyarrow/include/arrow/testing/matchers.h,sha256=DYYP4Zfhmh6sufC561VJkD_RLFxENx8iSZSa9YZTJTg,17319
pyarrow/include/arrow/testing/pch.h,sha256=CDXIaNX-5nbOaUj6udZil-7sI8UBGDaUk7ytXjRHH5w,1189
pyarrow/include/arrow/testing/random.h,sha256=ubiDDutWnOixyBjJ9H4pIfEJ1QMp03-xkkcSuCUmepE,35766
pyarrow/include/arrow/testing/uniform_real.h,sha256=8Us2dKOV8LhsB-MawOoCBPc7eLzP35KKWVemK0UZoRE,3054
pyarrow/include/arrow/testing/util.h,sha256=v-X3FX9vmMHsq6wuTb4sNnpkj40sJDweqLnKSQX6qQ0,5531
pyarrow/include/arrow/testing/visibility.h,sha256=Rf9HfEuXNziHZQw8CW8IZ5AEDNeVWCArO0PlyHK4bos,1596
pyarrow/include/arrow/type.h,sha256=1y2tlWXDVBBFURRjvq0K8BuKYwzPN1DED_PYqt7wFjo,96199
pyarrow/include/arrow/type_fwd.h,sha256=WieuL8wtOdGT5PJNFPRRf8Ckmk8S7bYfmVEobpps-PI,22538
pyarrow/include/arrow/type_traits.h,sha256=Lb53HOcqpZdTXCC1mWpft-oIAENDAJ6EowDW-zTD4fA,54610
pyarrow/include/arrow/util/algorithm.h,sha256=okcbieN_LA2UJ29QIEQ8LcF9EblZvyu4iO9r_QK8deY,1262
pyarrow/include/arrow/util/align_util.h,sha256=0ZSxphiZMkkYipVQ8940h8jjB-0B7oKbrGGLzltPZCw,10890
pyarrow/include/arrow/util/aligned_storage.h,sha256=2lBntZb0Ni6VAJQ6GB7Db4Qw_yAH98t9laqC6Ir-Lu8,5132
pyarrow/include/arrow/util/async_generator.h,sha256=JckeB8mFvotSp3zSpUua0fCSzSLlnSm0_QIgU1NKlHk,79762
pyarrow/include/arrow/util/async_generator_fwd.h,sha256=a2jJSFwcbyEem7skH2_OZUCElg3K0c-pM08WOdvnpag,1808
pyarrow/include/arrow/util/async_util.h,sha256=FvGoRiKle-dv8lJrVYHr172NFJ8X-c49dGZM4YDN2oY,20219
pyarrow/include/arrow/util/base64.h,sha256=ihRHtpYtb8xH71Zu2goxkm7LKIAnQkzlsSgSUQu7BlE,1130
pyarrow/include/arrow/util/basic_decimal.h,sha256=32QsCHaBYuGTLgG1YUqosULntCGB92hJ-Gz5Duv67Ps,19285
pyarrow/include/arrow/util/benchmark_util.h,sha256=M2D-6zHAMywExxYOVZu-HskXP2tP47z8QD8OPMI3tEM,7852
pyarrow/include/arrow/util/binary_view_util.h,sha256=42ZjUl3DWtm8ZV4fyiBN4SeXBnpx9TRQ-G0if634crc,3924
pyarrow/include/arrow/util/bit_block_counter.h,sha256=HR4DVUkayHHvzTQbBjAM6TCwSWJzFu8UJaE1YAffvqk,20732
pyarrow/include/arrow/util/bit_run_reader.h,sha256=xOv8udorWzpetz5KtUaFBSkva-Hps75HLRKnbc_OrbA,17131
pyarrow/include/arrow/util/bit_stream_utils.h,sha256=286boBYpECvWzBpb6Pax9tKgUJBgxsoYsBhwORJ0dDU,17854
pyarrow/include/arrow/util/bit_util.h,sha256=0G6SuobUlU_mKjhAz1d4kAq-FZQlpCixkmF60yzd_HE,12481
pyarrow/include/arrow/util/bitmap.h,sha256=TYFzImZOknDPY07kmxd0rSw4a7Ew830hUjSiuBufHWg,17928
pyarrow/include/arrow/util/bitmap_builders.h,sha256=lkBf2Ej19bmQxeFklmUV7Z4j7-F7JmRLfGC0JiY-uFE,1606
pyarrow/include/arrow/util/bitmap_generate.h,sha256=LS3946GllKz4xNShS8VwA-v12rVNgvSLCY4nViapFC0,3773
pyarrow/include/arrow/util/bitmap_ops.h,sha256=40TmVx6C1ETGX8OBcETQTno46XyA0_rBAJXWBzzIcSQ,10994
pyarrow/include/arrow/util/bitmap_reader.h,sha256=T8oLs0pEclxX1n-dlkUJY7JEsehtB4j46OmA8giXRmk,8626
pyarrow/include/arrow/util/bitmap_visit.h,sha256=PU07JCo3_618ZWJMxiTIZW6dfETceXgbjfU1YeEfyYE,3558
pyarrow/include/arrow/util/bitmap_writer.h,sha256=iznccf-wENmUl2C4qa5P9tyCUnarQKYqpoGiYcB7DR4,9669
pyarrow/include/arrow/util/bitset_stack.h,sha256=10k45pz7ZzWrJwTuGqcLztVLg71wfFoabykodNsnGBw,2865
pyarrow/include/arrow/util/bpacking.h,sha256=e4TwSlBz3V6YjsWGUSMwi0por6GtEN07t7LtjuSopeU,1209
pyarrow/include/arrow/util/bpacking64_default.h,sha256=Ov2SA9sQmohXSHw0Rb_zytyVQ54TJK61V_smIZMdkac,202632
pyarrow/include/arrow/util/bpacking_avx2.h,sha256=WKb37t4ch1eOkeNGySTiKAG1lfCN8Cvtnjy8aFHt9Jg,1037
pyarrow/include/arrow/util/bpacking_avx512.h,sha256=H3iqF8SNMT4szyzvv6gSwdXMmNitTqN76tDELLYG9vo,1039
pyarrow/include/arrow/util/bpacking_default.h,sha256=OjfkZlZhuOYn0rXQmABEFctBSK6TQDDATrUUAktYBGE,108011
pyarrow/include/arrow/util/bpacking_neon.h,sha256=aukHePChI9vXwUjSCXZ7YMjQmHh7xCtxm4iHKlVs7zU,1037
pyarrow/include/arrow/util/byte_size.h,sha256=Z9HxkPG4DAMSJ5bxpglqUP3UP-vnMViRb9KgTrOhh_s,4085
pyarrow/include/arrow/util/cancel.h,sha256=uG0bRqLbXBSwyAU_eQrZ8UCElaQO7SboyJOZNi0ZJqI,3769
pyarrow/include/arrow/util/checked_cast.h,sha256=0XkAxq3kuBxU_pMZ_r6rmgovb5zoFHhq9nZNuWKkgVQ,2137
pyarrow/include/arrow/util/compare.h,sha256=aslVAwRy9uF49L9neBjVmMOVANewLp_GgadkieX7rWs,2044
pyarrow/include/arrow/util/compression.h,sha256=Rs2N_GisDUu13jxt9sbaz5GutlqFKwEqRxxb2ocqXe4,8668
pyarrow/include/arrow/util/concurrent_map.h,sha256=hmRw2luX9OVbYwIyMXc3fEgof4An4owLnWrAsKgXKpM,1843
pyarrow/include/arrow/util/config.h,sha256=XU-Hq0LN6tFjCaxGhvXRZrbBTeXrxq7PKZkCfAnchPs,2384
pyarrow/include/arrow/util/converter.h,sha256=9Wl7M4FSGKwj_jZnVDELWwkkNcd8rJmlsAeOwZF3W1Q,15048
pyarrow/include/arrow/util/counting_semaphore.h,sha256=pjZ6Sq7cAbmTVwIeSoCS6je4ru00KKXHzhtOUpxLItE,2311
pyarrow/include/arrow/util/cpu_info.h,sha256=Cg7Alr-NR4E89JZO9KlAo5bH60gVUcvpqBOKZ_yUF3Y,4078
pyarrow/include/arrow/util/crc32.h,sha256=mbAGpVjfRlJS1npQaAgDBWJYe6WeflXM6J-ZBO9MSTQ,1373
pyarrow/include/arrow/util/debug.h,sha256=Y8UN-qs-hijFAWr-LKoNq1dpI2ol-krDuy1xTCVYgb4,1000
pyarrow/include/arrow/util/decimal.h,sha256=awp4v4gsZT-hcq7cs4fN7FGlmh16mE1Ihrz7L6sR_rE,11748
pyarrow/include/arrow/util/delimiting.h,sha256=ZFc1v-DOeBEBfWYcN4-cnxvQMToxeGJeJZ63Z0nrWgc,7498
pyarrow/include/arrow/util/dict_util.h,sha256=p-_WX8G1NM-WB158LBV-aFfd7s1l9rlZi6a--dLStEk,1014
pyarrow/include/arrow/util/dispatch.h,sha256=_-IP_k9Cdw6p58XAiuxjXE9G9-na6vb9HoYXBP-WRl4,3350
pyarrow/include/arrow/util/double_conversion.h,sha256=AMG-JfVeM9bRTe43j1iGNPJDU1NNZbEwwtE_E-KmuMM,1275
pyarrow/include/arrow/util/endian.h,sha256=Pa0TMreunmNBxjK0bsfm0eyMNhfsy4wnMRpevW9R_FI,8359
pyarrow/include/arrow/util/float16.h,sha256=VoXU-bBM2zpCJ_bj8inzTzq5MCukFV8kZKG70sLytQE,7627
pyarrow/include/arrow/util/formatting.h,sha256=--bXusz3T1MlH2duRQ6JeOsUGHwrt2T4TB8zY6vQN5g,22844
pyarrow/include/arrow/util/functional.h,sha256=vVBnLo4584uiCYaRQSA-2BzzsNSsyjWbEdgsF6L0a8c,5772
pyarrow/include/arrow/util/future.h,sha256=kWHhE5nOrI6qt0rJJCFQdVOP8yCf-Px7oo4TSEKYmRg,33178
pyarrow/include/arrow/util/hash_util.h,sha256=qMwNgyct19kOpUX10ULZIZcEhGi1_nYymcardJP1h10,1980
pyarrow/include/arrow/util/hashing.h,sha256=Y9_1Im0QWGjIxqecrCGEEGCr9Jvv2ttpCmjMGXkE8ZY,33834
pyarrow/include/arrow/util/int_util.h,sha256=1l6c85GqyP_k5ZOsOyqu0mgaA9YBo_JJe4fv_4eNEJA,4996
pyarrow/include/arrow/util/int_util_overflow.h,sha256=2z44sPGDtAVXvWgVOI85H70cKo_FlUeue-c91aeIfWs,5013
pyarrow/include/arrow/util/io_util.h,sha256=NsCW80ixzL9tEkPUAy-J1YGWwWulcQVSV665fRfnNOg,14157
pyarrow/include/arrow/util/iterator.h,sha256=DENtDLLCYrZBygsxOCKfbpWeT36oisDASVeyjPW9njY,18662
pyarrow/include/arrow/util/key_value_metadata.h,sha256=m-o5pRI-eyo_32FVsr4byAdE2zcUKul9ROsqE2Oj9Ug,3689
pyarrow/include/arrow/util/launder.h,sha256=SSTWzD4H-Oy0TExl13n4sWqHiRLlKtBxT22r7JZZ7ZI,1081
pyarrow/include/arrow/util/list_util.h,sha256=qO0VnbXcxWfgMuZ4IOYsQk1Jo83EQI64MP1uK-gyWgs,2083
pyarrow/include/arrow/util/logger.h,sha256=eVuRNynFM-JQsjjPs_pvWkAEM9tTiscm1MFhQ15gLUE,6841
pyarrow/include/arrow/util/logging.h,sha256=wra-XZc9GkmGMRs-CQqjaig1E0PjvgBTKNuMj8EvTU0,9380
pyarrow/include/arrow/util/macros.h,sha256=EkyXt4LeEPsIGUf0gWJKUmgpX5kXi200-Yyhtu8tzsw,9408
pyarrow/include/arrow/util/map.h,sha256=dRoAZDkMFDHZ4rUlGGxilCN0qVW969S8IbNDYMhmXpw,2539
pyarrow/include/arrow/util/math_constants.h,sha256=QIrNIKzMlaDKVOIe6tJWkPo3JtK2rDG0Ux8z7in98x0,1138
pyarrow/include/arrow/util/memory.h,sha256=wXr9qPvbRTNJNYDq92iEVGgxSzWEqAu4Eu-Kb1qHRVw,1609
pyarrow/include/arrow/util/mutex.h,sha256=jANHLdRtEDUTIoCYa7kBFRr2fTQlhkvabm1xCKkrFSY,2639
pyarrow/include/arrow/util/parallel.h,sha256=Iuj_mOxLxyuiEJRAbjrS5M8BL3W1r_lN22LHnKgwOio,3718
pyarrow/include/arrow/util/pcg_random.h,sha256=Wb4huSnThKxOkUJTPjIUZBebkjs-FlzDVbjZjK_bTzc,1285
pyarrow/include/arrow/util/print.h,sha256=0HAu2Eq3Adpb9IpAPdHcl2aaClY4Ur9I8ZlF0h27P0E,2521
pyarrow/include/arrow/util/queue.h,sha256=vs-xE_jGsLwiWsCiSIshMMPxm-XKxThG6fRLoghFEYQ,1046
pyarrow/include/arrow/util/range.h,sha256=lx2Pwl1BaYNQSJg4VI2ej4JkCRV2ecYDt8W3ac-4AUA,8784
pyarrow/include/arrow/util/ree_util.h,sha256=WCM3vA6YQTbzcO6IP4TeOwUJxdddZ-nMogPQBBVGbUE,22977
pyarrow/include/arrow/util/regex.h,sha256=9Ilm_WKJawvl0MbRXYFU6nCHqiAnctxjEXZDgTK99FQ,1793
pyarrow/include/arrow/util/rle_encoding.h,sha256=8Mg809mEITQznimO39q2K3pkp1X-FQUkzQ2H0zaQCTU,31861
pyarrow/include/arrow/util/rows_to_batches.h,sha256=5foVd7Rxoqf7-g5U_yiMyRcVb4zJEgBNrXqpqsoCtXk,7283
pyarrow/include/arrow/util/simd.h,sha256=DkJcg2fA0D35dAJjWejqo_OykNy_an2MFCnHHPweqdE,1271
pyarrow/include/arrow/util/small_vector.h,sha256=aP9xti38tT1JxLqyyTfa1iE-K8UUFitVkz9GEnOtPJk,14932
pyarrow/include/arrow/util/sort.h,sha256=z6QTFJTKranZSLz3ov_7cgdAUUCRBPOVpz6juqPsmiY,2544
pyarrow/include/arrow/util/spaced.h,sha256=GxDNNOKr_wFJ77bmwFPzxLFQxT-EHqreUOYTVkqiHEs,3665
pyarrow/include/arrow/util/span.h,sha256=0d6h8cjbYcYLbtBCw0b2dtp08WpEmLoFmTjyGEs_Bd8,5707
pyarrow/include/arrow/util/stopwatch.h,sha256=wjaUvLLMmPxAMo010wde0aDkSutbaRuptkvxlD0_PHM,1449
pyarrow/include/arrow/util/string.h,sha256=GtYiM48M13N6d2miXFdkTD4q1RFNA3R-YBcvdKxu8rQ,5927
pyarrow/include/arrow/util/string_builder.h,sha256=KG1Kq6wAqqkKklEN7ttczbkmckvesmuQLjkABVEP85A,2530
pyarrow/include/arrow/util/task_group.h,sha256=ndcevZLV91JlhUjTy0STS6vgfrnlNIds6PH9CogPbY8,4468
pyarrow/include/arrow/util/tdigest.h,sha256=5WA74rC1mYHVQGMUZLm3y8mAkEQuR4nLCvoG_t8z11c,3156
pyarrow/include/arrow/util/test_common.h,sha256=di1zrj8WqO82vFlPxZicFqLgm45YD6npiruZrVGEWxM,2927
pyarrow/include/arrow/util/thread_pool.h,sha256=MjtMl8RcVIMo-vo4vNzIHAQ3RtrNxageR59WewTVt2M,25044
pyarrow/include/arrow/util/time.h,sha256=Qas0nobSX9zpGDHTVkEy4NiRTZmapE6huDafS4B-b9w,3071
pyarrow/include/arrow/util/tracing.h,sha256=-7AEJ2qsSlJ3IMwQ4Gw_5KV0R1UPAmF-YHfFJchO4po,1331
pyarrow/include/arrow/util/trie.h,sha256=B9ei9sN4XgL857xEyF0Ho5EGnqjrNKxJDgkXqqHcS8U,7364
pyarrow/include/arrow/util/type_fwd.h,sha256=aMDQZLABKRHYvi47sOhV8Q-G0WQ-7WfeKFpyGrmBaks,1591
pyarrow/include/arrow/util/type_traits.h,sha256=s1CoxKll91XgTlUJLQObKZHN3vJM-VX2WakWoXvzJZk,1777
pyarrow/include/arrow/util/ubsan.h,sha256=41dnucf_qNFn_wAtv9iaThXr8se_JycYDg7GE_U7X7c,2852
pyarrow/include/arrow/util/union_util.h,sha256=Er6Skg48w3vcVZJmBHkWqU9sfeRyyrSO7A9JUU-qc-w,1242
pyarrow/include/arrow/util/unreachable.h,sha256=z23Cp8loRsJJFdIx__AJRBzQzN8dAOnRmktsMIqW9x0,1100
pyarrow/include/arrow/util/uri.h,sha256=7DAZEXo-_N4bLm9dyPlpCge1WgbLRbm35Zvhu_T9Fzs,4005
pyarrow/include/arrow/util/utf8.h,sha256=Bon6sFrfWHTRc-aTxE7AIznWG1UMLsr0iukjvg209lM,2090
pyarrow/include/arrow/util/value_parsing.h,sha256=B6Vj7vDau1onUrUWLCDGgd77RlfSNZYW2uv_wyf5Ns0,30940
pyarrow/include/arrow/util/vector.h,sha256=YyO_8DvK7HBCTLJjSuFMOlwyRXgTd2cTe872wys3yzg,5845
pyarrow/include/arrow/util/visibility.h,sha256=huC_F98sSA2oFSEDRRSw4t4QyW54ciBxtHmWveFgP7o,2769
pyarrow/include/arrow/util/windows_compatibility.h,sha256=w7iycch25GX2vssuvuqrIzM3DHYKDgSd2KZIucHIESg,1264
pyarrow/include/arrow/util/windows_fixup.h,sha256=aJSvi7BbCvJtXul8F5otHPZMq8BPIhSaQ1qGshbB2yU,1431
pyarrow/include/arrow/vendored/ProducerConsumerQueue.h,sha256=kQtEadAfKO3qYjALi1ywgCaE0ov2aGQBUFdMS1qQC48,6318
pyarrow/include/arrow/vendored/datetime.h,sha256=at3-JuEjiMAFkuwGUq4dRkC5oBSG0GOGJYbDBJ9JTGk,1043
pyarrow/include/arrow/vendored/datetime/date.h,sha256=QxmdtvxBSDcgMo0s2iOQiQMq1GXnsqkRdA_Z6t5xB_4,245819
pyarrow/include/arrow/vendored/datetime/ios.h,sha256=I_2bJeHES2eSm9BuNxZCMHtboJTVlp8676k7yc0Lj44,1732
pyarrow/include/arrow/vendored/datetime/tz.h,sha256=1Pez22WMH9lINXtVcILwO7tCkMhL4DzCOOtYWuSM7U8,87668
pyarrow/include/arrow/vendored/datetime/tz_private.h,sha256=g_zzjyLdFUfPP0xrQ_rNa2VrYhlE2prmLXLwVZ8WZkU,11067
pyarrow/include/arrow/vendored/datetime/visibility.h,sha256=js46GSqWXi_VKSUTEYCFTuzHLFzL_4-yCW0l_IZGO3Y,977
pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h,sha256=ug8vS5I3hPBzzWy8oNbgFxI2TMfB5lH7h2GqA7feh5M,4444
pyarrow/include/arrow/vendored/double-conversion/bignum.h,sha256=2JlGD5vJLfiFtxZPhQ_cxgAp9HslZxsCv75mott_vtI,6103
pyarrow/include/arrow/vendored/double-conversion/cached-powers.h,sha256=0ngMtGe0Z2w-rRx02TXZce4oRwGkUIyqTTXaWDQ3uzc,3145
pyarrow/include/arrow/vendored/double-conversion/diy-fp.h,sha256=wDWYQCswEEMKvIqSrmNV6c8rKuRwEhxjuJfG1D3vMM4,5227
pyarrow/include/arrow/vendored/double-conversion/double-conversion.h,sha256=gxyJNQw6njGyORh7oKiRbeMXOTG7wFhaGLNNf2p4SCE,1838
pyarrow/include/arrow/vendored/double-conversion/double-to-string.h,sha256=LgPPmQstU7ODW1sYBQqPiGqqE9rn9cXXhv4NepiLcMQ,24397
pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h,sha256=mp-y_ztzGDBZpXrXob3GxoJlVQ2d72HraY3HrBhfySg,4212
pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h,sha256=G8xnzmSUSewcLO_31lSDFwGwtYX-UNrPfBnFET9CQBE,2886
pyarrow/include/arrow/vendored/double-conversion/ieee.h,sha256=PkmBx5i6U4mxaVNAHigdSzFSXwy5HnQVToquqqn6wjE,15730
pyarrow/include/arrow/vendored/double-conversion/string-to-double.h,sha256=qVlN6Ipax1SxNPpBLDgwVWiwf9hP03WhQ20V07cHC7U,11146
pyarrow/include/arrow/vendored/double-conversion/strtod.h,sha256=NJ9MHQpDfEtOBOeb75_p6LFmOnkDoe83Lyt7MyR-XxU,3162
pyarrow/include/arrow/vendored/double-conversion/utils.h,sha256=-4-s2NNi2xpcVkQ1FC21SelDLJoDM_O0xPelwq9mevk,16034
pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp,sha256=XtzT6-JHWmGxRuuMnDM9_0AXbOb4lzGWPyTXqdH2mGI,20433
pyarrow/include/arrow/vendored/pcg/pcg_random.hpp,sha256=79Y3YTo5rbOcOJlxfzbA08IrwVXRjQyNwaTw1LSuo3M,75455
pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp,sha256=pUPqr7_xnyJ7kfoQJPzRHwPAoPEX3XRW5Fou82CCeYE,29419
pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h,sha256=nco_jVnzpnDASOnTnPFE8CCj9uwOWhyV-kBgoBmnf8o,3164
pyarrow/include/arrow/vendored/portable-snippets/safe-math.h,sha256=D8WFFrtN64lU8XdNOdfSKwMMfIHxsqqbbATfuvSA1wg,49239
pyarrow/include/arrow/vendored/strptime.h,sha256=6IG4iT_rLr1Z-r7FcitdiZyEQ28slbb8r8ty5ik57N4,1247
pyarrow/include/arrow/vendored/xxhash.h,sha256=ExWtVPlfaed4_G728m0ZVyo_x78z9lx0E_-aZ_MB0l4,862
pyarrow/include/arrow/vendored/xxhash/xxhash.h,sha256=K0vb7lnmVPp4CV0BbzcTaZgk1Z-QybZFg4dljxt8JBA,259869
pyarrow/include/arrow/visit_array_inline.h,sha256=UtSCAHQQfOIMqAD-Q8gXRqqSOJbDF7hbIcgXx1KHcNE,2510
pyarrow/include/arrow/visit_data_inline.h,sha256=hG9zSU37e32ny7kgpOXFvDG19WtFbI_lTs9HgKyS18Y,12741
pyarrow/include/arrow/visit_scalar_inline.h,sha256=hxfWYgeRl22v7iANSNoIR8RDCJWAYlRRSfhbEIPotoI,2487
pyarrow/include/arrow/visit_type_inline.h,sha256=aUjWouFFSb0UAsvrvRDqe-n5RcxXIe79uGbnPgOgRY8,4514
pyarrow/include/arrow/visitor.h,sha256=Y0OSuutIV6YNy71plD4yU_0S4Cz9pjNTwvsOhqZssFs,8557
pyarrow/include/arrow/visitor_generate.h,sha256=ao-HLsQtcUBxfbL3sx3QXdOlAgQx37ZRMcNdRc9A3wI,3297
pyarrow/include/parquet/api/io.h,sha256=D0-NRwdb1FrXH8DKZsjZ8MtfZjjUaboR_lv1iFLbr2A,867
pyarrow/include/parquet/api/reader.h,sha256=njU4e2U-9SWf-XtxKdwRqezF-hLF5dAKIWUZFmyawa0,1239
pyarrow/include/parquet/api/schema.h,sha256=rDl6YZ_-iwqcmp7t_rSADxNtiJqJtdl_bHlbOJfCtE0,876
pyarrow/include/parquet/api/writer.h,sha256=wR783UqPiqunq9PY90ae-3lCkuQlScWdFbanzal8NpI,1032
pyarrow/include/parquet/arrow/reader.h,sha256=JFH85i1CACh_H-MceD_5QpD24pQGXMHSwhVAInuVio0,15684
pyarrow/include/parquet/arrow/schema.h,sha256=EBNwRSV2xGo9YQcBQm7lrGrsRoKSy6dkflJlVxxjSKc,6388
pyarrow/include/parquet/arrow/test_util.h,sha256=HumBjbZ9o8LS4CR_U-Ymz_m5R5g3xoo_5cuy0R_mtWA,20825
pyarrow/include/parquet/arrow/writer.h,sha256=VtiNgPnZIvYfT8g5vjR5ZdDLmH31qHTMw4QzxdTbjdw,8143
pyarrow/include/parquet/benchmark_util.h,sha256=Fxmv-42Qnr_0kfzWsD0dkufPSc-iYnoltFBquI8ahqo,1803
pyarrow/include/parquet/bloom_filter.h,sha256=xLJvykyOuOUXWH06bRVIT5guFYfxuaZvf2Lji9FSSQY,15350
pyarrow/include/parquet/bloom_filter_reader.h,sha256=rYjN1t6FheFqk0_M_uRUw5SVjbZdiF8BDRcjkkXom7Y,2960
pyarrow/include/parquet/column_page.h,sha256=uskFJWIgU06BKaSao0i1S0gWiFhnuZQjo5kkPE4FrZs,6697
pyarrow/include/parquet/column_reader.h,sha256=sl8ewt-juhDQzWzmAD6fv4bdd61C5rkHGPttGjf-O1Y,22050
pyarrow/include/parquet/column_scanner.h,sha256=kEe6sFLctNQdYHWSe8kZMJn2FFwPpQ9YV-VsHFZYuWo,9127
pyarrow/include/parquet/column_writer.h,sha256=8eVsfKa4n_0AODG8_a_7hFr30IrV2orXqcRCKWQ21oE,12895
pyarrow/include/parquet/encoding.h,sha256=1rX9ZWj-qiWC4aJ0iSvUE_9eDnQjbyb_pgCcWHFAh8c,17617
pyarrow/include/parquet/encryption/crypto_factory.h,sha256=76pz4LIN4VN7hXr3n9TUmYQhCM7tq4W9z_pWvrOr1U8,7209
pyarrow/include/parquet/encryption/encryption.h,sha256=f6INZCbs0ZVuyD0T4Y3uJLdKbxEE5bY3Oe8dZ-i4JlI,20360
pyarrow/include/parquet/encryption/file_key_material_store.h,sha256=8HQG0om1FEes8bOWBnRlVh9ZBjOIkRysBzix8-JLp2Q,2257
pyarrow/include/parquet/encryption/file_key_unwrapper.h,sha256=6m9OPhdKPaLQ2dk_-ntCKWf1TzQITrEXLZMW1D0KyJw,4729
pyarrow/include/parquet/encryption/file_key_wrapper.h,sha256=UX5QR7SdyidywA8V8m-zcorleRmXMqFNQmJ8iG0Eg_4,3846
pyarrow/include/parquet/encryption/file_system_key_material_store.h,sha256=S16ZOAAZnH2q-k4oqm2bQAzR8TmdCIWOVAunaDIMeVw,3662
pyarrow/include/parquet/encryption/key_encryption_key.h,sha256=iXagnT2x08O3md_vKPmYzwrZqXdEutkIUGVpO4drTIw,2289
pyarrow/include/parquet/encryption/key_material.h,sha256=FLjsSciNllhH2rfuh6xNGSvwPuNpHE2dTeFeAdy1cEw,6350
pyarrow/include/parquet/encryption/key_metadata.h,sha256=9dLFh5ppO4icbYkJMNpyT1s43wfAY0UIMdgNkzfiWA8,4094
pyarrow/include/parquet/encryption/key_toolkit.h,sha256=7zisONi_O_qn-N9Qc1Ady-X43BJMktV7SHz1Y6h0j-k,4683
pyarrow/include/parquet/encryption/kms_client.h,sha256=0AzqB7Wgr31XPiPihxFGVIYcFo44oz-ydPuZcMMn2SU,3244
pyarrow/include/parquet/encryption/kms_client_factory.h,sha256=OnnMzSQewEckWA1vpsb2g4n7LBQ9zzDueMJi988dV6g,1331
pyarrow/include/parquet/encryption/local_wrap_kms_client.h,sha256=V0XBLFK9V8AeJ0yRvnB17mBF9movGmkItBnVylLPvbY,4048
pyarrow/include/parquet/encryption/test_encryption_util.h,sha256=YjOgyMRAaNDhAsaM5-LFu8PnHiOI1Jx4ODbM7prs_JQ,5342
pyarrow/include/parquet/encryption/test_in_memory_kms.h,sha256=-Whn4W6ydKU0UN6DNf9qmZ-Z6RZX-H4H6H9XefQu_sQ,3615
pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h,sha256=vBfZbv_lmyUxr9O0fbj7-SubspRAkxalwxIaApVJ5SM,5232
pyarrow/include/parquet/encryption/type_fwd.h,sha256=Y8CtkdgEEkelQiGOHiFxNm0jAtUl84UQS7ucBOK8tmA,983
pyarrow/include/parquet/exception.h,sha256=vPPNX8_oD-VruhpC7H6x97gwQlyBki42uDRPtVvgqJc,5755
pyarrow/include/parquet/file_reader.h,sha256=ikQj2QuTlPZdjmEPUIGJf42jkxNliykbtsYAJ7-Q8Nk,9895
pyarrow/include/parquet/file_writer.h,sha256=iY1zWKySLKxJoDDn2BjOTSE0H8Wj2OSRVzO3nEBpxlw,9588
pyarrow/include/parquet/hasher.h,sha256=bDhLFfpDeTO3zK6hpb8o3Of_iCx_YooSlRrx2N64gg0,5358
pyarrow/include/parquet/level_comparison.h,sha256=drcd3Q01X1fOv5RezyxondOVPt_vQkc4X003dUzrhig,1344
pyarrow/include/parquet/level_comparison_inc.h,sha256=3Lfyhh_cbYweQL6zCLA6TXA_yjDx5GsstRlcOmKqMKc,2553
pyarrow/include/parquet/level_conversion.h,sha256=xWMMG8DFqxIx4EVevtiFdLtiZhKT3KajEd3EhIJv7lM,9648
pyarrow/include/parquet/level_conversion_inc.h,sha256=1IWeljPps-5dEITS8dpXNOo0j_pwUR5Xd6jYCd1sFkk,14501
pyarrow/include/parquet/metadata.h,sha256=QornVlzOOOkOplRs4gqpjNavJ3pro0E5mjv-TTDD_IY,21731
pyarrow/include/parquet/page_index.h,sha256=KE0Dja_dkBizzzUvY3zv-s5Bhov_akKUmq6gWynJwHY,16771
pyarrow/include/parquet/parquet_version.h,sha256=2cMwdlHPvg6Ne24zG_ret_YLtwetW6TxTV6qIzLHv_A,1195
pyarrow/include/parquet/pch.h,sha256=GCCeQqUf1FYv0rA70WfR3Q9wS_3STE5un5tTgRuOjEg,1277
pyarrow/include/parquet/platform.h,sha256=4rLk8_IKd6xWVUdNWqWq6YRYRU5HhbswD71QMm9POzk,3932
pyarrow/include/parquet/printer.h,sha256=g4pHHvQ5loK9o1py7wyMJMJ7y1ceDSVY_K44Ymy_Cb0,1586
pyarrow/include/parquet/properties.h,sha256=MU0XNJWyrdaSYR14PhjJIp-VC3Lq2s1Iq1F7TOMOJi8,46859
pyarrow/include/parquet/schema.h,sha256=HrU_kf7NIMMGVmVAYrlEirV8fta0q1gxPBc-_yfu95g,18621
pyarrow/include/parquet/statistics.h,sha256=G8cmUz27HhE9Yi3VOrYhCPEkre3Mk93UdegP5P6-6Tk,15557
pyarrow/include/parquet/stream_reader.h,sha256=sJ_wO06AUopUtr24Y_O5FDCTQHdA_5A35HIx2Uq8AWs,9094
pyarrow/include/parquet/stream_writer.h,sha256=k7qykhypcl_c394biPzy3RaAxIDEcZNWCTwKGnQrMHk,7748
pyarrow/include/parquet/test_util.h,sha256=-AZcXyBGxuzTBRGl7yRgqzQPe9qk11-qsZi8dev-ns4,32014
pyarrow/include/parquet/type_fwd.h,sha256=iKV2IASYV7lasNzhZen70BQr4-YDC7Pcd7BlIXxWYP4,3137
pyarrow/include/parquet/types.h,sha256=FDTPuV62hv5iitmC5UYqPDep180U349LCjsf3n0x1u0,26302
pyarrow/include/parquet/windows_compatibility.h,sha256=UqmjdtDSUHYXXSN7FfO5KqS-92ukyDG6iKOThvjcZ14,918
pyarrow/include/parquet/windows_fixup.h,sha256=tZ2zUcBzQ68HXyAn3jryfHbTEq0V0ytonzajDgt5Iic,1073
pyarrow/include/parquet/xxhasher.h,sha256=xEbgF2yqriXsdUJBP3KdjMb_0AfpTzx7z6ZlmRLeIOs,2124
pyarrow/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/includes/common.pxd,sha256=4Rlp5M-cwsI--GULMQCeyGy8aCuUrC_ROg7qXeomnBg,5627
pyarrow/includes/libarrow.pxd,sha256=8jis7uSn2ye4c4oeHAlEzIn2Uflrtq4hwcAyCUBLqXU,116476
pyarrow/includes/libarrow_acero.pxd,sha256=Es8B0rG40J8Od1YkSfUBF1g-n4PlsD0GEe6h-zDTXg0,5416
pyarrow/includes/libarrow_cuda.pxd,sha256=aLQJdjvhFE3eIOPUXQ3AYZjs4hNNjUYZ49ZVPrTsACE,4948
pyarrow/includes/libarrow_dataset.pxd,sha256=4pcViKHYXXnvYBjl1u1ZfpVdJbHhLJpiQbBg6D7bujw,17199
pyarrow/includes/libarrow_dataset_parquet.pxd,sha256=uo2ZVaCCvcDzWK7jS7yW66Ap0JafdUMxHrsCfMOFIjA,4641
pyarrow/includes/libarrow_feather.pxd,sha256=hIVe5CqIhUjc2nvfq3vjo1XoJm5qaXNZ3a1RWw8zS_U,2190
pyarrow/includes/libarrow_flight.pxd,sha256=3E_bfNfOy8rF5bCBC0uq-__bp56V0-8C1ru6XZY-yms,25482
pyarrow/includes/libarrow_fs.pxd,sha256=YGJ_FQ4My0I8fNTGgNlv84-06h9AFprm2Cnn-tssozE,15330
pyarrow/includes/libarrow_python.pxd,sha256=Cnjxad5xHpXSssbS_DHZBowfYg_U-_BSIgfaFTe4uP8,12681
pyarrow/includes/libarrow_substrait.pxd,sha256=cKiKhYLb5OXBdD53QXVWP9wYwrUYMOOhycP9hx1922I,3273
pyarrow/includes/libgandiva.pxd,sha256=WYxsELoA8UxY6LJ5aVyaxUzsQDxFvwcxQJ8mVv0i6zI,11836
pyarrow/includes/libparquet_encryption.pxd,sha256=ePyhBM-vE3aVQKS6ccpbDbwnhc9IJP-x7IxFsPXBWT8,6028
pyarrow/interchange/__init__.py,sha256=hFVl1PV1F4Ovb92_NbtKgDsrqRquxi82guDjtkK0CQE,865
pyarrow/interchange/__pycache__/__init__.cpython-38.pyc,,
pyarrow/interchange/__pycache__/buffer.cpython-38.pyc,,
pyarrow/interchange/__pycache__/column.cpython-38.pyc,,
pyarrow/interchange/__pycache__/dataframe.cpython-38.pyc,,
pyarrow/interchange/__pycache__/from_dataframe.cpython-38.pyc,,
pyarrow/interchange/buffer.py,sha256=YANe1dybRIzNzd5x3FGoQb0DPfmzyzZOfvFHDLXF5CI,3466
pyarrow/interchange/column.py,sha256=jmhpwkBMvQLRM5Br3jQxIv7fZY929OzqBv-Ewm04WFI,19899
pyarrow/interchange/dataframe.py,sha256=tOTq0H1TBOfzFrEN10GPAJnF4iyopd1jF_zKamvMqRQ,8622
pyarrow/interchange/from_dataframe.py,sha256=lGy4ZHtP61gfCJQScVx4HwalvqjIhQU-Ar6saDJsE5Y,20323
pyarrow/io.pxi,sha256=OFLpIFeT_MlfZ-WwXvNwb_feDY_Gypck91y5tBOWCoo,89535
pyarrow/ipc.pxi,sha256=Iy0bUc7H3AWt6OferICnP5qqAYCygIT0OivDsxkB-JI,42484
pyarrow/ipc.py,sha256=0pKl7G5ZpygvQZifFriOpzTkS0jB4qbGvUksGVVNYg4,10392
pyarrow/json.py,sha256=WxkWLvsFNvLvhTKM1s_kRFJfPR23LiJkh3RZoQ2lFoA,877
pyarrow/jvm.py,sha256=hOBj7jSA1E45ZdqMMehr91Tgvqwo-XFHEPKttESHWO0,9928
pyarrow/lib.cp38-win_amd64.pyd,sha256=UjA54EFdRxKG77hTqU3Q6k8SpHzYUWRBu2lHlPTMTLc,3557376
pyarrow/lib.h,sha256=_XgqZ_3T5W9PqqlxWG6Mkekxgy-j7YV7LdBBQzpc6gg,4631
pyarrow/lib.pxd,sha256=MVSaPKVObcEn9Ucv5HHev_ApQBOBWQl3NhSUPNAQ8E4,18161
pyarrow/lib.pyx,sha256=WWTtEAOnUwy4mcFjAvfd61IWcKWuLcr7-vQ3SlwLalo,6139
pyarrow/lib_api.h,sha256=3YSCQN-GF_-nWfkpEkQmR1Q-Lrk2XZUqk8o1_ttBkgw,19487
pyarrow/memory.pxi,sha256=6qNamHBm8o72PSkAwGOJQk9Mg91gwX_KyiCHrMBCiuM,8503
pyarrow/msvcp140.dll,sha256=EN3_SNcExgB-TC1T-0hWteXnlHlQM2YjYkajI6qnbp0,585096
pyarrow/orc.py,sha256=y2UvVsf7xgkUVk447bHn14x-m5uZX3UE8jIQapOA4DU,13002
pyarrow/pandas-shim.pxi,sha256=boEl3pinxDiJo8V3LnSNvfVdn0WXktNDSExjnIydYkU,8439
pyarrow/pandas_compat.py,sha256=g-cY2ZncIJ9Aslo4BOfWGj-br6u2dndbpDrqroEBdRo,43244
pyarrow/parquet.dll,sha256=0zRkvMAHz-77_1IgtO1cnjdlURMQdGXdLuTE4069huw,5965824
pyarrow/parquet.lib,sha256=iIzPsBTsx1beP6lPfnbh9tRqLL5hVk3thwcHZCC4TYs,717980
pyarrow/parquet/__init__.py,sha256=MP8hmY5y1-LXoEUg6VinrGfThZkw1HAwUIL4ddrSAxQ,842
pyarrow/parquet/__pycache__/__init__.cpython-38.pyc,,
pyarrow/parquet/__pycache__/core.cpython-38.pyc,,
pyarrow/parquet/__pycache__/encryption.cpython-38.pyc,,
pyarrow/parquet/core.py,sha256=B6RG_awcR_flaAPylnLxKOp_SRBqZO_C1DW4JOfuAWY,92815
pyarrow/parquet/encryption.py,sha256=8XjYgPw3KhU7eYuXj23pO3dJArm97v-1AAHt4WCfA3A,1176
pyarrow/public-api.pxi,sha256=2yOv-cqtiTpTyqZoicKtoOBjlkNx0dDrnNRUqqddnIo,13889
pyarrow/scalar.pxi,sha256=Ts42rd84Kl48DtNbp87yO14TsW6cpaD4oLEPclYUb-g,35721
pyarrow/src/arrow/python/CMakeLists.txt,sha256=eDpRoeeAadQQ6zb930iuhKhcE9sPK0zxJFrn3usEFbo,846
pyarrow/src/arrow/python/api.h,sha256=biQIS5I3je_V8G078KUnuhQfdUp1lYZSkqlrNvm9I_A,1252
pyarrow/src/arrow/python/arrow_to_pandas.cc,sha256=dV1usJovo4-JD0nPvstwnvtKoDnXP5UUPtwVxK73sQc,98138
pyarrow/src/arrow/python/arrow_to_pandas.h,sha256=N-Bl98Z-Y7OrkYzI8pJ5bhR6i-HoCom-Mk1sYCosRAo,5707
pyarrow/src/arrow/python/arrow_to_python_internal.h,sha256=DbOoJJai8YeS1JKBCloQlgzztwv1Kkjv3rF86nUK7ww,1789
pyarrow/src/arrow/python/async.h,sha256=15MshOaSHLSsfKlQw4LCY338khPideC74IpLOFCygDE,2412
pyarrow/src/arrow/python/benchmark.cc,sha256=-bPstUW6eQ843nOduN6dFYUfYaWeJlL8PctHJZkRQYI,1331
pyarrow/src/arrow/python/benchmark.h,sha256=OqCsqRe5osY2lhe2ecN8cXrdkmSFgLp6fXcf4_N9Fik,1228
pyarrow/src/arrow/python/common.cc,sha256=1ohE6hwR_6wF55NRu8GmVzTqwCq0u3YBwVH6QqfWKmA,7837
pyarrow/src/arrow/python/common.h,sha256=k24Fr3kOWfeygcXp-dFrNTSKXdEZVgTybqZnqfNJt_U,14870
pyarrow/src/arrow/python/csv.cc,sha256=sosW1I3yNLLmoScv8o8ECYzLUB8ZiEboeDAstC8SVZg,1865
pyarrow/src/arrow/python/csv.h,sha256=7yixEpKzdR_wvjfG0rlkmtYpSyjEuNb2cPLueyKFa0s,1439
pyarrow/src/arrow/python/datetime.cc,sha256=gAQFEQ3d_YmX7vH-yS79-dHNUqoyl4TCKv8Kkad3uQk,23664
pyarrow/src/arrow/python/datetime.h,sha256=LRUq2ByJ05aoQl3wh6KSktPKW7AJ_wNVX-QVxTprB5Y,8158
pyarrow/src/arrow/python/decimal.cc,sha256=XiB7dAy4laD_731vkCwSFprFxigYS8q1uYuIdxzclxI,9094
pyarrow/src/arrow/python/decimal.h,sha256=BAt3laodbJkc9CsIEN0Qi1l2Ra9Fuznv1wNc5d4r8gw,4854
pyarrow/src/arrow/python/deserialize.cc,sha256=N74AdbDuMzqu1-fa4rfvxowDGRtdTZC9loggkoxy5i4,19440
pyarrow/src/arrow/python/deserialize.h,sha256=GWCwSyz_ldSfaDz9ObTs5sXom09iDntaMCB2W3Ow5Ss,3995
pyarrow/src/arrow/python/extension_type.cc,sha256=6CdWbNCAZ0DsUqSYYHU9vsUxH4w5nPx2H3rl4WVBGps,7077
pyarrow/src/arrow/python/extension_type.h,sha256=ovyhXsE-iornOx99_iOmQjZbdqIIpzUfiT7dTvVjQro,3266
pyarrow/src/arrow/python/filesystem.cc,sha256=TF2i8-laHGZJgDizwsMMBxPi0gTJEiC8RB9A6MK_zfw,6358
pyarrow/src/arrow/python/filesystem.h,sha256=bnXcXSssY90VwD784m3CqQuNnZdwZxKpc8MltD4zJyY,5256
pyarrow/src/arrow/python/flight.cc,sha256=eZwukZYFDP04wyf0h3MpcWar8bbMTnypIqZ0BDET1M4,14383
pyarrow/src/arrow/python/flight.h,sha256=CV9HoK-GlwD5sCCn2YVByTfbUET0cRghn9GQGpLLlLI,14619
pyarrow/src/arrow/python/gdb.cc,sha256=NMWC1rHZTwcvtwVYsjh4oOwaClbNrCusiHDnVlZ276E,23863
pyarrow/src/arrow/python/gdb.h,sha256=VN8aDtc1iFPIKsjFQ29RKQOkClvKAAI_KeTy_LFZcZg,1001
pyarrow/src/arrow/python/helpers.cc,sha256=y00P6ChBNaETYpNDboK564ElKDDOx1zTnHjnqRODsh0,16558
pyarrow/src/arrow/python/helpers.h,sha256=uBgiOl8b-gklCa4AxKOA-8HFZkKN9WQy7n1Pa0qIAA0,5651
pyarrow/src/arrow/python/inference.cc,sha256=i4JAwt3J6NZ85BE9hSK38_4zMThnC7cuD3uH1Vffq0g,25065
pyarrow/src/arrow/python/inference.h,sha256=_44xuq5q-qktVBIigUbwmlgzOMJ4vHuauXQu-pXG1bg,2102
pyarrow/src/arrow/python/init.cc,sha256=MtNxfuYH5pC7JhjMGC5jZYVJatC5kYZBTdEeLRhPiPI,1046
pyarrow/src/arrow/python/init.h,sha256=Dfdy6kMtFHc_NW6EhionR0ZWr-S6XpaICdu1PzTl-E0,974
pyarrow/src/arrow/python/io.cc,sha256=ezdgnbs-wM492lG3YE1Sgb3awDjj93l4bqVMnFmAZGI,12323
pyarrow/src/arrow/python/io.h,sha256=byOtFjW3NuetpJIErXFG62bsim0ZsWIAPgbes_JvI3k,3979
pyarrow/src/arrow/python/ipc.cc,sha256=0j0rCBxCObGLNKEksCFxnuLqJVtscnpMeOS_kyT7Jus,4607
pyarrow/src/arrow/python/ipc.h,sha256=lHD6Yl4f_ARUIK6SlA4Jy75i7_K940NIGg6vHauLypc,2331
pyarrow/src/arrow/python/iterators.h,sha256=7JRvhoQL0V7lOPGxhCk_1j_wUl_Vc9jDIOf5nhU1Yig,7387
pyarrow/src/arrow/python/numpy_convert.cc,sha256=lzklAQcX-PybvcWelQy4ocZzQcfbOYiO8JG-56SdG4Y,21460
pyarrow/src/arrow/python/numpy_convert.h,sha256=JVUlnUuzWE4OwsoHPl4hvzi5_nkDjHR_wO5Iu8ZEVkU,4992
pyarrow/src/arrow/python/numpy_internal.h,sha256=P8rFvhCopB5MA-rIWjiScI_6wM1Hs3GTUOD3h0Oj__0,5254
pyarrow/src/arrow/python/numpy_interop.h,sha256=Spr_TZme4Bb6r22_xSXrQyy9YWQY9hc9AJQlCeUgOTI,3495
pyarrow/src/arrow/python/numpy_to_arrow.cc,sha256=e00I1oS9EFWZ7WpHLw6KoR-JWnXjSlFNXsapTgjh5EM,30998
pyarrow/src/arrow/python/numpy_to_arrow.h,sha256=-JhqPEg-tOBKhwRDhurxrAYQ6I-ws4CoghILzj9R-ms,2832
pyarrow/src/arrow/python/parquet_encryption.cc,sha256=RFWN7nVzsScOor6yQWkGHK9YCeDIXOrHa4bGBdyNGBE,3665
pyarrow/src/arrow/python/parquet_encryption.h,sha256=OhPqZ11MVJ12pQWA2CzvT6pP4s0wYYE2Tg_AL_zKT74,4951
pyarrow/src/arrow/python/pch.h,sha256=cuGs6XHl1WaiL-RuVk8stRK6hquHo5mjD6z0QsGQ_uo,1153
pyarrow/src/arrow/python/platform.h,sha256=2C243DF3SvJD7Jlb4D6_oTNtXV_qFRgsXTMBRcEQU74,1447
pyarrow/src/arrow/python/pyarrow.cc,sha256=0xL6yyKbEsll_71rP0wkOLjpdnfMWaY0Ogg9S1XS58E,3777
pyarrow/src/arrow/python/pyarrow.h,sha256=Pt2advP3jdTBRb5y2TtyDLVBACieOKI2IZblGu6kPMc,2850
pyarrow/src/arrow/python/pyarrow_api.h,sha256=uPrpVqzif4NlQkJvj-tZCIeBLBLSNE7ekGjKlCPCZxw,886
pyarrow/src/arrow/python/pyarrow_lib.h,sha256=D1QHgdorjjbTko9iMJLaFwykjnjIRBDLyuQaskohSmw,882
pyarrow/src/arrow/python/python_test.cc,sha256=-U6pUyBb2_3tcTRqNaa_lZ9h44WzT802H-OguwAYUQo,33277
pyarrow/src/arrow/python/python_test.h,sha256=kdEUk3TAKggBdER-tT8y-fIcO2UnfEh2K8XFrnDYLYk,1237
pyarrow/src/arrow/python/python_to_arrow.cc,sha256=1mM7vsnxU0NIe4hdFb91etwvg3Df4_UUi4OED_ltLYE,48201
pyarrow/src/arrow/python/python_to_arrow.h,sha256=r6iWOjXDn6gO6Qxo9mAlnm6f94jdAE8EmW0D23UJgac,2601
pyarrow/src/arrow/python/serialize.cc,sha256=UVzreKB-U92ovU885lMwHGkkFZ8xmlBIChz9I8V8bwc,33465
pyarrow/src/arrow/python/serialize.h,sha256=0aG_RcujeI5nSeUhGSRCIAmPFgCjsUimY1PV0EK44tQ,4540
pyarrow/src/arrow/python/type_traits.h,sha256=bhn_U-tmE9OX4GEJfFqz41Lf3tBwQPfl5wB4X3BHFQs,10443
pyarrow/src/arrow/python/udf.cc,sha256=_2-XtEhXZCcY66jSXrHwIqWIY2nRwVz01XYnDIM-PHc,31421
pyarrow/src/arrow/python/udf.h,sha256=gLQGE1vYHlYJ_ceN1Kb0upw6cP90juYZ4b30VvuUPPM,3185
pyarrow/src/arrow/python/visibility.h,sha256=4FcBJfEIEsf7s36wakgQL0UK0tFdHDckk3oWrOGF0eA,1378
pyarrow/substrait.py,sha256=5pbjbLOIroLCocCLUF_dhBgibDcsjHOgGT5Uh6-ecKU,1181
pyarrow/table.pxi,sha256=9kC-XUgh-ROvgYBQxZK8723GNES1zzTUgWDJlSB3Uc4,205027
pyarrow/tensor.pxi,sha256=lGROtGEWaPmwuOY_WbK5yozgB-etQRBfjiWNlBeidPA,42732
pyarrow/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/tests/__pycache__/__init__.cpython-38.pyc,,
pyarrow/tests/__pycache__/arrow_16597.cpython-38.pyc,,
pyarrow/tests/__pycache__/arrow_39313.cpython-38.pyc,,
pyarrow/tests/__pycache__/arrow_7980.cpython-38.pyc,,
pyarrow/tests/__pycache__/conftest.cpython-38.pyc,,
pyarrow/tests/__pycache__/pandas_examples.cpython-38.pyc,,
pyarrow/tests/__pycache__/pandas_threaded_import.cpython-38.pyc,,
pyarrow/tests/__pycache__/read_record_batch.cpython-38.pyc,,
pyarrow/tests/__pycache__/strategies.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_acero.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_adhoc_memory_leak.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_array.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_builder.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_cffi.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_compute.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_convert_builtin.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_cpp_internals.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_csv.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_cuda.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_cuda_numba_interop.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_cython.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_dataset.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_dataset_encryption.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_deprecations.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_device.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_dlpack.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_exec_plan.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_extension_type.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_feather.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_flight.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_flight_async.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_fs.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_gandiva.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_gdb.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_io.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_ipc.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_json.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_jvm.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_memory.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_misc.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_orc.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_pandas.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_scalars.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_schema.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_sparse_tensor.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_strategies.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_substrait.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_table.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_tensor.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_types.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_udf.cpython-38.pyc,,
pyarrow/tests/__pycache__/test_util.cpython-38.pyc,,
pyarrow/tests/__pycache__/util.cpython-38.pyc,,
pyarrow/tests/arrow_16597.py,sha256=4MtaNkHRXf-ae8UPc_W0Sy7PQOSA38L2znT2LE0YSxM,1391
pyarrow/tests/arrow_39313.py,sha256=6iDq6Ci3q_SFDjA7RtZ27cnwmlyqGQQJgzThU5YPjG8,1478
pyarrow/tests/arrow_7980.py,sha256=W3Gbd_XbB9vC87k4VDIjyJgGfUurz6PKkFJn1pNdR4I,1124
pyarrow/tests/bound_function_visit_strings.pyx,sha256=QgKbbEM5Tm7ZNP4TTiEwpSut-sP0KEE7eT3K0KgRTw8,2093
pyarrow/tests/conftest.py,sha256=qakVlaK1C2E-PtAaMcf5wrGgxx83a8T6ozLp3IG0QNI,10199
pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather,sha256=qzcc7Bo4OWBXYsyyKdDJwdTRstMqB1Zz0GiGYtndBnE,594
pyarrow/tests/data/orc/README.md,sha256=6J34Nh2eK930SY4vBkZbTlTnyC7ImMmVuJOFRIY8yDI,954
pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz,sha256=xLjAXd-3scx3DCyeAsmxTO3dv1cj9KRvYopKe5rQNiI,50
pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc,sha256=zj0579dQBXhF7JuB-ZphkmQ81ybLo6Ca4zPV4HXoImY,523
pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz,sha256=kLxmwMVHtfzpHqBztFjfY_PTCloaXpfHq9DDDszb8Wk,323
pyarrow/tests/data/orc/TestOrcFile.test1.orc,sha256=A4JxgMCffTkz9-XT1QT1tg2TlYZRRz1g7iIMmqzovqA,1711
pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz,sha256=oWf7eBR3ZtOA91OTvdeQJYos1an56msGsJwhGOan3lo,182453
pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc,sha256=nYsVYhUGGOL80gHj37si_vX0dh8QhIMSeU4sHjNideM,30941
pyarrow/tests/data/orc/decimal.jsn.gz,sha256=kTEyYdPDAASFUX8Niyry5mRDF-Y-LsrhSAjbu453mvA,19313
pyarrow/tests/data/orc/decimal.orc,sha256=W5cV2WdLy4OrSTnd_Qv5ntphG4TcB-MyG4UpRFwSxJY,16337
pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet,sha256=YPGUXtw-TsOPbiNDieZHobNp3or7nHhAxJGjmIDAyqE,3948
pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet,sha256=7sebZgpfdcP37QksT3FhDL6vOA9gR6GBaq44NCVtOYw,2012
pyarrow/tests/data/parquet/v0.7.1.parquet,sha256=vmdzhIzpBbmRkq3Gjww7KqurfSFNtQuSpSIDeQVmqys,4372
pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet,sha256=VGgSjqihCRtdBxlUcfP5s3BSR7aUQKukW-bGgJLf_HY,4008
pyarrow/tests/extensions.pyx,sha256=C_usHZiHuxDWirlx8tMNZ4BEIz1828hipX8uIBrT6_E,3140
pyarrow/tests/interchange/__init__.py,sha256=EVfaECqIP6zwm3-KeFxrnNB3-C4waxZUgr3MmUMTU6k,801
pyarrow/tests/interchange/__pycache__/__init__.cpython-38.pyc,,
pyarrow/tests/interchange/__pycache__/test_conversion.cpython-38.pyc,,
pyarrow/tests/interchange/__pycache__/test_interchange_spec.cpython-38.pyc,,
pyarrow/tests/interchange/test_conversion.py,sha256=bZ81xWhZKYBToTRcndBa4HH-IRQk1ZBH6KQK3f8BYYI,19011
pyarrow/tests/interchange/test_interchange_spec.py,sha256=e5qPZH3FO0umeulfiU-GzYZLX-spzFgnR5GlMPx6CPw,9489
pyarrow/tests/pandas_examples.py,sha256=rMH-mMWhDC9s56E9k9qH59IvN3NhhcVHR2sZaC1GNk0,5287
pyarrow/tests/pandas_threaded_import.py,sha256=TkyeVQWGZNoRUgbx39KC5ENTV8q3bMmo9wJxIIxso0Y,1473
pyarrow/tests/parquet/__init__.py,sha256=HBk5_BESLXOn2axHI96jkaiO9dkNzfXHZAUPf0sXbgM,955
pyarrow/tests/parquet/__pycache__/__init__.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/common.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/conftest.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/encryption.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_basic.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_compliant_nested_type.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_data_types.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_dataset.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_datetime.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_encryption.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_metadata.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_pandas.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_parquet_file.cpython-38.pyc,,
pyarrow/tests/parquet/__pycache__/test_parquet_writer.cpython-38.pyc,,
pyarrow/tests/parquet/common.py,sha256=0c42XOkdX-g6pv-39_A4kAHOmqokzWEGSDcHuA3ixJg,6017
pyarrow/tests/parquet/conftest.py,sha256=jIbNv3yFslR-4l-B0qtaGCFBSc28HY8-6Ad91s0zEBw,2734
pyarrow/tests/parquet/encryption.py,sha256=0Zoz4i_eIEF7SepbogLZg8dWKprEw0IjgRmh-xu_vNk,2582
pyarrow/tests/parquet/test_basic.py,sha256=yGcns097N6D4PRsTjpMz-vDH1Et5nldElS5vMe2yI6k,37438
pyarrow/tests/parquet/test_compliant_nested_type.py,sha256=gL9xcmd1IlVPXF68-pULYfryIrGcJI40NOd2btKH3mM,4010
pyarrow/tests/parquet/test_data_types.py,sha256=VFM9g_11FsdH5ixWu6irR0D5a_6X3rbs9YSsKL1Ehv8,16177
pyarrow/tests/parquet/test_dataset.py,sha256=0b-iBK8Pj91eZn47-p0LihAbhBmBUUDSue9nNMNz5M4,43302
pyarrow/tests/parquet/test_datetime.py,sha256=9rAHvRibjMUkp4jEMj8HBUvEU0bd-FwA8CfdM31AXy8,16813
pyarrow/tests/parquet/test_encryption.py,sha256=Szv05z9MC5BrHc7ufYOj4FtmCuuut_o2Rhu0D2T6Yc4,22671
pyarrow/tests/parquet/test_metadata.py,sha256=xCF_pNx_DZ_ntj_64BnoBWDlCjnlTuSQ-IxwAQT4EZM,27451
pyarrow/tests/parquet/test_pandas.py,sha256=xJGYpu6k-TvUTGRSe4-M_-c3ZgH552azvUIJS7QW0w0,23432
pyarrow/tests/parquet/test_parquet_file.py,sha256=Qy-KUp7xwW6DLxuQNnjgQHmXK085yLY8VHq-wa6tf20,10245
pyarrow/tests/parquet/test_parquet_writer.py,sha256=HQINViOYlxCMfwacXHVlczHN9Y_GbEBDMo5Rp56AlLo,12096
pyarrow/tests/pyarrow_cython_example.pyx,sha256=Qgd7BEHzrxDVpdMzLplpD-XAkqN0Vu6-N75KGhLXkpQ,2176
pyarrow/tests/read_record_batch.py,sha256=J8MvpGBAv7rjvoseM5OnQyU9BL_3f2Ffdc0cYFvLh60,978
pyarrow/tests/strategies.py,sha256=ERJcV737WfVsPlXAXAgBj-ZgKtvNUxYSFOvwojaWYKU,14295
pyarrow/tests/test_acero.py,sha256=b8a7xEVrlUw4GMFEdJmmaoUAziFMz7ud90Zfz3d2_F8,15416
pyarrow/tests/test_adhoc_memory_leak.py,sha256=tn3uWObaOgx-XdrqrHSohfEQ3p1Op62ne1iSxcO77ZE,1453
pyarrow/tests/test_array.py,sha256=llouzdhPW_NiZpuWhiTdtJlvUehPRba8XGD-T1MOn28,142256
pyarrow/tests/test_builder.py,sha256=bBM7uiqDcd3eb7tkXEm47_Bt7dGJqJYdK7q5giFAPB4,2889
pyarrow/tests/test_cffi.py,sha256=Z6N9Ja-e_4jeMcRfT7Yo90XnFlzWXMFGsw2TEdKaCKs,27144
pyarrow/tests/test_compute.py,sha256=cOO_aJMxP4PIOq9WZKKLukzbbmpdDvYznIbceVjzTro,145530
pyarrow/tests/test_convert_builtin.py,sha256=mZ4yh8dljfDkqk-2mJfR1yicuBMooj880cJdyPePvJI,82462
pyarrow/tests/test_cpp_internals.py,sha256=4CK6YtKrAHGm0ew3570uxU9GdM1j43PtWmqAqc8TUoA,1812
pyarrow/tests/test_csv.py,sha256=m48lao45CXiKRHPSIPxXhC8EUXd0WcIekTJgZ6xZWxI,78530
pyarrow/tests/test_cuda.py,sha256=1aGhhIEv-L_ygtD4CJ76ubkCAHqHdEsFyaBTBesATbg,35969
pyarrow/tests/test_cuda_numba_interop.py,sha256=MIdgdCZ5715r0tuklCRgv1TWSvnGf2QjJUKgGiEZVnk,8965
pyarrow/tests/test_cython.py,sha256=rb-8DSs0canYbrsZqRKZedVWKQC9GH2cXuQlhV7NpWk,7153
pyarrow/tests/test_dataset.py,sha256=c5CWjddF6f13sEfqgkkNangeiXfif1CQ2qM2B9xJJa4,213428
pyarrow/tests/test_dataset_encryption.py,sha256=KwmFd3tEZ8-lzkqAcIEPnVeRccNUcxqP8Vm1DcPr4o4,7775
pyarrow/tests/test_deprecations.py,sha256=qGsfYqoc2Ty5l3fIKL7zCM_IeGFgquPPv_5OWRAA6UM,914
pyarrow/tests/test_device.py,sha256=SK3I_CmSs_4DvoZuIK07gC32M4nqTgSk82rMYTaOXVA,1697
pyarrow/tests/test_dlpack.py,sha256=Hb13_WbcFSI8U5vhA7mGS9dwzus4mLuFIaa6BAKaAK8,4850
pyarrow/tests/test_exec_plan.py,sha256=bdEJdN7i5hc6qRKA-GTt5p9KHRqrsfaGjZCQL_XB9Qw,10433
pyarrow/tests/test_extension_type.py,sha256=kJwBmV_lUkyNnE0JZh9zFqtEcF_GCDD5zP1hMODUrYk,58283
pyarrow/tests/test_feather.py,sha256=ASGfqGeEAV7iSxNg1Bo_c-yZK6dI1uS4PR_bm_8ah58,25799
pyarrow/tests/test_flight.py,sha256=K_pnWrZFcPfLtSbBV-J3Jpr1YIIK87CJGm6avdxPyiw,88732
pyarrow/tests/test_flight_async.py,sha256=MnSTBpMoxj-c53A1JXe3ocIqysNtt55DJyL-ZOU8zs0,2966
pyarrow/tests/test_fs.py,sha256=wwaxZZcpVBgnIuXkAG3_uzN59hGtsEEKDRqztdZeSRc,66049
pyarrow/tests/test_gandiva.py,sha256=9DgDgJyjl7EnXAMYBgOqABUbCeDc723W6lmLmfzft84,16057
pyarrow/tests/test_gdb.py,sha256=RqURE3pll6RmplKd4FmWJiwztynytsmCERSPpdlkyjA,45996
pyarrow/tests/test_io.py,sha256=FFc_YgZAcmPk-66PLZBTMu5CvJtEph2PuGc8xHk3wWo,65710
pyarrow/tests/test_ipc.py,sha256=l5m-kgghGxYLUUpNgVX5-IwcFKDUfTkFy3zUEmr3EIU,43973
pyarrow/tests/test_json.py,sha256=EwVT62BaNHVW3_dMFapCaB_LckDmACBnDgjLNhqKYe4,13408
pyarrow/tests/test_jvm.py,sha256=IahX2XEcMmktktfWhak8eAjMMBit3JSJY5A6i4QYt3E,15942
pyarrow/tests/test_memory.py,sha256=o-YSebICUo4TWnwwyIkWGFdSAe71iOmMpOL8oP6K7yU,9099
pyarrow/tests/test_misc.py,sha256=wOOEw4qlNBz-hiUMXNdf6Kky20u7CpVFZd_ffWZvK-0,7365
pyarrow/tests/test_orc.py,sha256=uCY21Beq5I4lqEGNfjxVqNmMzhuiFnlIL0BTRFsYhjw,19958
pyarrow/tests/test_pandas.py,sha256=DTzNIeBrL_MVJNqbP-TSJaeTLjE5hS_dNBhNtHC0Brk,193347
pyarrow/tests/test_scalars.py,sha256=Oquhsyy6GOpGLO0lhdWZjmN04CDDK1v5fEdvAFoCK8Y,27367
pyarrow/tests/test_schema.py,sha256=_9tU1K5iCIXP7hzU15b0QGBbQAfgIqe_ICRZ_ScsPY0,22497
pyarrow/tests/test_sparse_tensor.py,sha256=0obkMMgbJSrmG8o-B8eCC_jZpB4Q5Ebz23-LcsVzqKw,17927
pyarrow/tests/test_strategies.py,sha256=6JJifQKQVyQpJB0LqGEcQ-Qv8NyxseAMuDs-3_oKNX8,1800
pyarrow/tests/test_substrait.py,sha256=QQDzXM6wklG1OsZsI0-NZCXYyeqPMQhGd0_3TqEM704,31673
pyarrow/tests/test_table.py,sha256=XsP6rIprMIOdHijpXjktshy4f7-cFOGmMet8OHHGXbM,104590
pyarrow/tests/test_tensor.py,sha256=AY-Q0eLI009wc0oug6jK5hKZyq-uoKG8kcTt2zW082Q,6802
pyarrow/tests/test_types.py,sha256=8rzi1vXjpta2WJsjdAi84NXy8fzm7fiH-GaqHcbPHew,42930
pyarrow/tests/test_udf.py,sha256=awhn_CM3iy1xkxHQL8Xjv8yUxS6Iy2joFnN1T_aaOJI,29456
pyarrow/tests/test_util.py,sha256=NAENcrCOT9uTSpoU8KqW67e1H6tJP5COkGlAZeAAX48,5259
pyarrow/tests/util.py,sha256=aVPEOgOZIGsTsFa1czreozztJhmqHKYzxjRjdSyzFJo,14502
pyarrow/types.pxi,sha256=F4qRqff0RKzZJ3XkgN-G7IeQT9fvp8eNE188ina4qS4,154306
pyarrow/types.py,sha256=Sc41IyKIbYCSH_VaeGg5hlmDpX3GuJvDBVUNRQEd4Nk,7554
pyarrow/util.py,sha256=BNlr4bQhp0Wtg4zG0jih0T2WqQXcqpKHzGwIh6yHsOs,8228
pyarrow/vendored/__init__.py,sha256=EVfaECqIP6zwm3-KeFxrnNB3-C4waxZUgr3MmUMTU6k,801
pyarrow/vendored/__pycache__/__init__.cpython-38.pyc,,
pyarrow/vendored/__pycache__/docscrape.cpython-38.pyc,,
pyarrow/vendored/__pycache__/version.cpython-38.pyc,,
pyarrow/vendored/docscrape.py,sha256=QgFpJRG3lyDx6Q0sUuqkkowQ23up97bT_LlgkqG5K40,23691
pyarrow/vendored/version.py,sha256=cr57fm516rcswYHhTfyRDXt-yDWq1EAlRTrZz3mVHo8,14890
scripts/__pycache__/run_emscripten_tests.cpython-38.pyc,,
scripts/__pycache__/test_imports.cpython-38.pyc,,
scripts/__pycache__/test_leak.cpython-38.pyc,,
scripts/run_emscripten_tests.py,sha256=RO6dexlgyV_-_lYZRDnX5ejrHW7Jo0769gaJ0D6lh0I,12024
scripts/test_imports.py,sha256=CxfztUGX1nK-N5othM7udNXomiOv0i5903OnOiFBKK8,883
scripts/test_leak.py,sha256=sOaY0KqZouXFig0Y4XT1G7AZVt-DRG4orq3PRo_1gmM,3632
