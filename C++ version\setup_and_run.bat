@echo off
echo ========================================
echo    C++ Version Setup and Run Script
echo ========================================
echo.

REM Check if executable exists
set EXE_PATH=%~dp0build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\bin\kiem_tra_mst_qt.exe

if exist "%EXE_PATH%" (
    echo ✅ Executable found: %EXE_PATH%
    echo.
    
    echo 🔧 Setting up Qt6 environment...
    set PATH=D:\PROGRAMS_D\Qt\6.9.1\mingw_64\bin;D:\PROGRAMS_D\Qt\Tools\mingw1120_64\bin;%PATH%
    
    echo 🚀 Running C++ application...
    echo.
    "%EXE_PATH%"
    
    echo.
    echo ✅ Application finished.
    
) else (
    echo ❌ Executable not found!
    echo Expected location: %EXE_PATH%
    echo.
    echo 📋 To build the application:
    echo 1. Open Qt Creator
    echo 2. Open kiem_tra_mst_qt.pro
    echo 3. Configure with Qt 6.9.1 kit
    echo 4. Build → Build All (Ctrl+Shift+B)
    echo.
    echo 🔧 Or copy DLLs and try again:
    echo Run: copy_qt_dlls.bat
    echo.
)

pause
