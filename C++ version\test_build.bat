@echo off
echo Testing build process...

REM Clean previous build
echo Cleaning previous build...
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug  
if exist Makefile.Release del Makefile.Release
if exist bin rmdir /s /q bin
if exist build rmdir /s /q build

REM Test qmake
echo Testing qmake...
qmake --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: qmake not found! Please install Qt6 and add to PATH.
    pause
    exit /b 1
)

REM Generate Makefile
echo Generating Makefile...
qmake kiem_tra_mst_qt.pro
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

REM Check if Makefile was created
if not exist Makefile (
    echo ERROR: Makefile not created!
    pause
    exit /b 1
)

echo Makefile generated successfully!
echo You can now run: make release
echo Or use: build_qmake.bat for full build

pause
