using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace KiemTraMST
{
    public partial class MainWindow : Window
    {
        // Constants
        private const string MST_COUNT_PREFIX = "📊 Số MST trong bảng: ";
        private const string VERSION = "5.0.13";

        // State variables (like Python version)
        private List<string>? _outputLinesFull;
        private List<int>? _updateIndices;
        private List<string> _danhSach = [];
        private string _ketQuaFile = "";
        private DateTime? _startTime;
        private bool _isPaused = false;
        private int _activeThreads = 0;
        private int _ok = 0, _fail = 0, _done = 0, _captchaFail = 0, _captchaOk = 0;
        private int _total = 0;
        private bool _modelReady = false;

        // Timers and threading
        private readonly DispatcherTimer _timer = new();
        private readonly DispatcherTimer _updateTimer = new();
        private CancellationTokenSource? _cancellationTokenSource;

        // Debug flags
        private Dictionary<string, bool> _debugFlags = new()
        {
            ["save_response"] = false,
            ["wrong_captcha"] = false,
            ["always_fail"] = false
        };

        public MainWindow()
        {
            InitializeComponent();
            SetupState();
            SetupUI();
            SetupConnections();

            // Initialize like Python version
            Dispatcher.BeginInvoke(() => UpdateHardwareInfo(), DispatcherPriority.Background);
            LoadModelBackground();
            CheckSettingFile();
            UpdateMstCount();
        }

        private void SetupState()
        {
            // Initialize state variables like Python version
            _outputLinesFull = null;
            _updateIndices = null;
            _danhSach = [];
            _ketQuaFile = "";
            _startTime = null;
            _isPaused = false;
            _activeThreads = 0;
            _ok = _fail = _done = _captchaFail = _captchaOk = 0;
            _total = 0;
        }

        private void SetupUI()
        {
            // Set window title like Python version
            Title = $"Tra cứu thuế v{VERSION} (TNT)";

            // Setup placeholder text
            InputTextBox.Text = "Dán danh sách MST vào đây...";
            InputTextBox.GotFocus += (s, e) =>
            {
                if (InputTextBox.Text == "Dán danh sách MST vào đây...")
                    InputTextBox.Text = "";
            };
            InputTextBox.LostFocus += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(InputTextBox.Text))
                    InputTextBox.Text = "Dán danh sách MST vào đây...";
            };
        }

        private void SetupConnections()
        {
            // Setup timers like Python version
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;

            _updateTimer.Interval = TimeSpan.FromMilliseconds(500);
            _updateTimer.Tick += UpdateTimer_Tick;

            // Connect button events
            StartButton.Click += StartButton_Click;
            PauseButton.Click += PauseButton_Click;
            RetryButton.Click += RetryButton_Click;
            OpenResultButton.Click += OpenResultButton_Click;
            OpenFolderButton.Click += OpenFolderButton_Click;
            DebugButton.Click += DebugButton_Click;
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            if (_startTime.HasValue)
            {
                var elapsed = DateTime.Now - _startTime.Value;
                TimeLabel.Content = $"⏱ {elapsed:hh\\:mm\\:ss}";
            }
        }

        private void UpdateTimer_Tick(object? sender, EventArgs e)
        {
            // Update stats like Python version
            UpdateStats();
        }

        private void UpdateStats()
        {
            if (_total > 0)
            {
                var percentage = (double)_done / _total * 100;
                ProgressBar.Value = percentage;

                StatsLabel.Content = $"✅{_ok} ❌{_fail} 🔄{_activeThreads} | {_done}/{_total} ({percentage:F1}%)";
                ProgressLabel.Content = $"Đã xử lý: {_done}/{_total}";
            }
        }

        private void UpdateMstCount()
        {
            var count = GetMstList().Count;
            MstCountLabel.Content = $"{MST_COUNT_PREFIX}{count}";
        }

        private List<string> GetMstList()
        {
            if (InputTextBox.Text == "Dán danh sách MST vào đây..." || string.IsNullOrWhiteSpace(InputTextBox.Text))
                return [];

            return InputTextBox.Text
                .Split('\n', StringSplitOptions.RemoveEmptyEntries)
                .Select(line => line.Trim())
                .Where(line => !string.IsNullOrEmpty(line))
                .ToList();
        }

        private void InputTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateMstCount();
        }

        private async void UpdateHardwareInfo()
        {
            try
            {
                HardwareStatusLabel.Content = "🔍 Đang detect phần cứng...";
                HardwareStatusLabel.Foreground = System.Windows.Media.Brushes.Blue;

                // Run hardware detection in background
                await Task.Run(async () =>
                {
                    try
                    {
                        var displayName = await HardwareDetector.Instance.GetDeviceDisplayNameAsync();
                        var deviceInfo = HardwareDetector.Instance.DetermineBestDevice();

                        Dispatcher.Invoke(() =>
                        {
                            HardwareStatusLabel.Content = displayName;
                            HardwareStatusLabel.Foreground = System.Windows.Media.Brushes.Green;
                            Log($"🔧 Phần cứng OCR được chọn: {deviceInfo.DeviceName}");

                            if (deviceInfo.DeviceType == "cuda" && deviceInfo.Properties.TryGetValue("memory", out var memory))
                            {
                                Log($"   💾 VRAM: {memory}");
                                if (deviceInfo.Properties.TryGetValue("compute_capability", out var compute))
                                {
                                    Log($"   ⚡ Compute Capability: {compute}");
                                }
                            }
                            else if (deviceInfo.DeviceType == "cpu")
                            {
                                if (deviceInfo.Properties.TryGetValue("cores", out var cores))
                                {
                                    Log($"   🧠 CPU Cores: {cores}");
                                }
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            HardwareStatusLabel.Content = "❌ Lỗi phần cứng";
                            HardwareStatusLabel.Foreground = System.Windows.Media.Brushes.Red;
                            Log($"⚠️ Lỗi detect phần cứng: {ex.Message}");
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                HardwareStatusLabel.Content = "❌ Lỗi phần cứng";
                HardwareStatusLabel.Foreground = System.Windows.Media.Brushes.Red;
                Log($"⚠️ Lỗi detect phần cứng: {ex.Message}");
            }
        }

        private async void LoadModelBackground()
        {
            try
            {
                ModelStatusLabel.Content = "⏳ Đang tải model...";
                ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Orange;

                // Simulate model loading (replace with actual OCR model loading)
                await Task.Run(async () =>
                {
                    await Task.Delay(3000); // Simulate loading time

                    Dispatcher.Invoke(() =>
                    {
                        _modelReady = true;
                        ModelStatusLabel.Content = "✅ Model sẵn sàng";
                        ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Green;
                        Log("✅ Model TrOCR đã sẵn sàng");
                    });
                });
            }
            catch (Exception ex)
            {
                ModelStatusLabel.Content = "❌ Lỗi model";
                ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Red;
                Log($"❌ Lỗi tải model: {ex.Message}");
            }
        }

        private void CheckSettingFile()
        {
            // Check setting.ini file like Python version
            if (!File.Exists("setting.ini"))
            {
                Log("⚠️ Không tìm thấy file setting.ini");
            }
            else
            {
                Log("✅ Đã tải cấu hình từ setting.ini");
            }
        }

        private void StartButton_Click(object sender, RoutedEventArgs e)
        {
            if (!_modelReady)
            {
                MessageBox.Show("Model chưa sẵn sàng! Vui lòng đợi model tải xong.", "Thông báo", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var mstList = GetMstList();
            if (mstList.Count == 0)
            {
                MessageBox.Show("Vui lòng nhập danh sách MST!", "Thông báo", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Validate MST format like Python version
            for (int i = 0; i < mstList.Count; i++)
            {
                var mst = mstList[i];
                if (mst.Contains(' ') || mst.Contains('\t'))
                {
                    MessageBox.Show($"MST dòng {i + 1} chứa khoảng trắng/tab: [{mst}]", "Lỗi định dạng", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
            }

            StartProcessing(mstList);
        }

        private void PauseButton_Click(object sender, RoutedEventArgs e)
        {
            _isPaused = !_isPaused;
            PauseButton.Content = _isPaused ? "▶️ Tiếp tục" : "⏸ Tạm dừng";
            Log(_isPaused ? "⏸ Đã tạm dừng" : "▶️ Tiếp tục xử lý");
        }

        private void RetryButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Tính năng làm lại từ kết quả cũ sẽ được thêm sau.", "Thông báo", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OpenResultButton_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_ketQuaFile) && File.Exists(_ketQuaFile))
            {
                try
                {
                    Process.Start(new ProcessStartInfo(_ketQuaFile) { UseShellExecute = true });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Không thể mở file: {ex.Message}", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void OpenFolderButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo(Environment.CurrentDirectory) { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Không thể mở thư mục: {ex.Message}", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DebugButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Debug tools sẽ được thêm sau.", "Thông báo", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Log(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logMessage = $"[{timestamp}] {message}\n";

            OutputTextBox.AppendText(logMessage);
            OutputTextBox.ScrollToEnd();
        }

        private async void StartProcessing(List<string> mstList)
        {
            try
            {
                // Setup like Python version
                _danhSach = mstList;
                _total = mstList.Count;
                _done = _ok = _fail = 0;
                _startTime = DateTime.Now;

                // Create output file
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                _ketQuaFile = $"ketqua_{timestamp}.txt";

                // Initialize output
                _outputLinesFull = [];
                _updateIndices = [];

                // Update UI
                StartButton.IsEnabled = false;
                PauseButton.IsEnabled = true;
                OpenResultButton.IsEnabled = true;
                RunningStatusLabel.Content = "Đang chạy...";
                RunningStatusLabel.Foreground = System.Windows.Media.Brushes.Green;

                // Start timers
                _timer.Start();
                _updateTimer.Start();

                Log($"🚀 Bắt đầu xử lý {_total} MST...");
                Log($"📄 File kết quả: {_ketQuaFile}");

                // Process MSTs (simplified version)
                await ProcessMstList(mstList);

            }
            catch (Exception ex)
            {
                Log($"❌ Lỗi: {ex.Message}");
                MessageBox.Show($"Lỗi: {ex.Message}", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ProcessMstList(List<string> mstList)
        {
            var workers = int.TryParse(WorkersTextBox.Text, out var w) ? w : 20;
            var semaphore = new SemaphoreSlim(workers, workers);
            var tasks = new List<Task>();

            foreach (var mst in mstList)
            {
                var task = ProcessSingleMst(mst, semaphore);
                tasks.Add(task);
            }

            await Task.WhenAll(tasks);

            // Finish processing
            _timer.Stop();
            _updateTimer.Stop();

            StartButton.IsEnabled = true;
            PauseButton.IsEnabled = false;
            RunningStatusLabel.Content = "Hoàn thành";
            RunningStatusLabel.Foreground = System.Windows.Media.Brushes.Blue;

            Log($"✅ Hoàn thành! Đã xử lý {_done}/{_total} MST");
            Log($"📊 Thành công: {_ok}, Thất bại: {_fail}");

            // Save results to file
            await SaveResults();
        }

        private async Task ProcessSingleMst(string mst, SemaphoreSlim semaphore)
        {
            await semaphore.WaitAsync();

            try
            {
                Interlocked.Increment(ref _activeThreads);

                // Wait if paused
                while (_isPaused)
                {
                    await Task.Delay(100);
                }

                // Simulate MST processing (replace with actual API calls)
                await Task.Delay(Random.Shared.Next(1000, 3000));

                // Simulate result
                var success = Random.Shared.NextDouble() > 0.2; // 80% success rate

                if (success)
                {
                    Interlocked.Increment(ref _ok);
                    var result = $"{mst}\t✅ Hoạt động\tCông ty mẫu {mst}\tĐịa chỉ mẫu";
                    lock (_outputLinesFull!)
                    {
                        _outputLinesFull.Add(result);
                    }
                }
                else
                {
                    Interlocked.Increment(ref _fail);
                    var result = $"{mst}\t❌ Lỗi\t\t";
                    lock (_outputLinesFull!)
                    {
                        _outputLinesFull.Add(result);
                    }
                }

                Interlocked.Increment(ref _done);
            }
            finally
            {
                Interlocked.Decrement(ref _activeThreads);
                semaphore.Release();
            }
        }

        private async Task SaveResults()
        {
            try
            {
                if (_outputLinesFull != null && _outputLinesFull.Count > 0)
                {
                    var header = "MST\tTrạng thái\tTên công ty\tĐịa chỉ";
                    var lines = new List<string> { header };
                    lines.AddRange(_outputLinesFull);

                    await File.WriteAllLinesAsync(_ketQuaFile, lines, Encoding.UTF8);
                    Log($"💾 Đã lưu kết quả vào {_ketQuaFile}");
                }
            }
            catch (Exception ex)
            {
                Log($"❌ Lỗi lưu file: {ex.Message}");
            }
        }

        private void HardwareStatusLabel_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                var cpuInfo = HardwareDetector.Instance.GetCpuInfoAsync().Result;
                var nvidiaGpus = HardwareDetector.Instance.GetNvidiaGpuInfoAsync().Result;
                var intelGpus = HardwareDetector.Instance.GetIntelGpuInfoAsync().Result;
                var deviceInfo = HardwareDetector.Instance.DetermineBestDevice();

                var info = new StringBuilder();
                info.AppendLine("=== THÔNG TIN PHẦN CỨNG CHI TIẾT ===\n");

                // CPU
                info.AppendLine("🖥️ CPU:");
                info.AppendLine($"   Name: {cpuInfo["name"]}");
                info.AppendLine($"   Architecture: {cpuInfo["architecture"]}");
                info.AppendLine($"   Cores: {cpuInfo["cores"]}\n");

                // NVIDIA GPUs
                info.AppendLine($"🎮 NVIDIA GPUs: {nvidiaGpus.Count}");
                foreach (var gpu in nvidiaGpus)
                {
                    info.AppendLine($"   - {gpu.Name} ({gpu.Memory})");
                }
                info.AppendLine();

                // Intel GPUs
                info.AppendLine($"🔷 Intel GPUs: {intelGpus.Count}");
                foreach (var gpu in intelGpus)
                {
                    info.AppendLine($"   - {gpu.Name} ({gpu.Memory} MB)");
                }
                info.AppendLine();

                // Best device
                info.AppendLine("🏆 Best Device for OCR:");
                info.AppendLine($"   Type: {deviceInfo.DeviceType}");
                info.AppendLine($"   Name: {deviceInfo.DeviceName}");
                info.AppendLine($"   Display: {HardwareDetector.Instance.GetDeviceDisplayNameAsync().Result}");

                MessageBox.Show(info.ToString(), "Thông tin phần cứng", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Không thể lấy thông tin phần cứng:\n{ex.Message}", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _cancellationTokenSource?.Cancel();
            _timer.Stop();
            _updateTimer.Stop();
            base.OnClosed(e);
        }
    }
}
