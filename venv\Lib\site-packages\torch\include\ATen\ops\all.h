#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/all_ops.h>

namespace at {


// aten::all.dim(Tensor self, int dim, bool keepdim=False) -> Tensor
inline at::Tensor all(const at::Tensor & self, int64_t dim, bool keepdim=false) {
    return at::_ops::all_dim::call(self, dim, keepdim);
}

// aten::all.dims(Tensor self, int[]? dim=None, bool keepdim=False) -> Tensor
inline at::Tensor all(const at::Tensor & self, at::OptionalIntArrayRef dim, bool keepdim=false) {
    return at::_ops::all_dims::call(self, dim, keepdim);
}

// aten::all.out(Tensor self, int dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & all_out(at::Tensor & out, const at::Tensor & self, int64_t dim, bool keepdim=false) {
    return at::_ops::all_out::call(self, dim, keepdim, out);
}
// aten::all.out(Tensor self, int dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & all_outf(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & out) {
    return at::_ops::all_out::call(self, dim, keepdim, out);
}

// aten::all.dims_out(Tensor self, int[]? dim=None, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & all_out(at::Tensor & out, const at::Tensor & self, at::OptionalIntArrayRef dim, bool keepdim=false) {
    return at::_ops::all_dims_out::call(self, dim, keepdim, out);
}
// aten::all.dims_out(Tensor self, int[]? dim=None, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & all_outf(const at::Tensor & self, at::OptionalIntArrayRef dim, bool keepdim, at::Tensor & out) {
    return at::_ops::all_dims_out::call(self, dim, keepdim, out);
}

// aten::all.dimname(Tensor self, Dimname dim, bool keepdim=False) -> Tensor
inline at::Tensor all(const at::Tensor & self, at::Dimname dim, bool keepdim=false) {
    return at::_ops::all_dimname::call(self, dim, keepdim);
}

// aten::all.dimname_out(Tensor self, Dimname dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & all_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, bool keepdim=false) {
    return at::_ops::all_dimname_out::call(self, dim, keepdim, out);
}
// aten::all.dimname_out(Tensor self, Dimname dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & all_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & out) {
    return at::_ops::all_dimname_out::call(self, dim, keepdim, out);
}

// aten::all(Tensor self) -> Tensor
inline at::Tensor all(const at::Tensor & self) {
    return at::_ops::all::call(self);
}

// aten::all.all_out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & all_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::all_all_out::call(self, out);
}
// aten::all.all_out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & all_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::all_all_out::call(self, out);
}

}
