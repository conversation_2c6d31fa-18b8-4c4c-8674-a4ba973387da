#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/bernoulli_ops.h>

namespace at {


// aten::bernoulli(Tensor self, *, Generator? generator=None) -> Tensor
inline at::Tensor bernoulli(const at::Tensor & self, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::bernoulli::call(self, generator);
}

// aten::bernoulli.out(Tensor self, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bernoulli_out(at::Tensor & out, const at::Tensor & self, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::bernoulli_out::call(self, generator, out);
}
// aten::bernoulli.out(Tensor self, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bernoulli_outf(const at::Tensor & self, ::std::optional<at::Generator> generator, at::Tensor & out) {
    return at::_ops::bernoulli_out::call(self, generator, out);
}

// aten::bernoulli.p(Tensor self, float p, *, Generator? generator=None) -> Tensor
inline at::Tensor bernoulli(const at::Tensor & self, double p, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::bernoulli_p::call(self, p, generator);
}

// aten::bernoulli.Tensor_out(Tensor self, Tensor p, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bernoulli_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::bernoulli_Tensor_out::call(self, p, generator, out);
}
// aten::bernoulli.Tensor_out(Tensor self, Tensor p, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bernoulli_outf(const at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator, at::Tensor & out) {
    return at::_ops::bernoulli_Tensor_out::call(self, p, generator, out);
}

// aten::bernoulli.Tensor(Tensor self, Tensor p, *, Generator? generator=None) -> Tensor
inline at::Tensor bernoulli(const at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::bernoulli_Tensor::call(self, p, generator);
}

// aten::bernoulli.float_out(Tensor self, float p=0.5, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bernoulli_out(at::Tensor & out, const at::Tensor & self, double p=0.5, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::bernoulli_float_out::call(self, p, generator, out);
}
// aten::bernoulli.float_out(Tensor self, float p=0.5, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & bernoulli_outf(const at::Tensor & self, double p, ::std::optional<at::Generator> generator, at::Tensor & out) {
    return at::_ops::bernoulli_float_out::call(self, p, generator, out);
}

}
