using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using RestSharp;

namespace KiemTraMST
{
    public class MstResult
    {
        public string Mst { get; set; } = "";
        public string Status { get; set; } = "";
        public string CompanyName { get; set; } = "";
        public string Address { get; set; } = "";
        public string Error { get; set; } = "";
        public bool IsSuccess => string.IsNullOrEmpty(Error);
    }

    public class MstChecker
    {
        private static readonly HttpClient _httpClient = new();
        private readonly SemaphoreSlim _semaphore;
        private readonly int _maxWorkers;

        public event Action<string>? LogMessage;
        public event Action<int, int>? ProgressChanged;

        public MstChecker()
        {
            _maxWorkers = ConfigManager.Instance.GetInt("DEFAULT", "max_workers", 20);
            _semaphore = new SemaphoreSlim(_maxWorkers, _maxWorkers);
            
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        }

        public async Task<List<MstResult>> CheckMstListAsync(List<string> mstList, CancellationToken cancellationToken = default)
        {
            var results = new List<MstResult>();
            var tasks = new List<Task<MstResult>>();
            var completed = 0;

            LogMessage?.Invoke($"Bắt đầu kiểm tra {mstList.Count} MST với {_maxWorkers} workers...");

            foreach (var mst in mstList)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var task = CheckSingleMstAsync(mst, cancellationToken).ContinueWith(t =>
                {
                    Interlocked.Increment(ref completed);
                    ProgressChanged?.Invoke(completed, mstList.Count);
                    return t.Result;
                }, cancellationToken);

                tasks.Add(task);
            }

            try
            {
                var taskResults = await Task.WhenAll(tasks);
                results.AddRange(taskResults);
            }
            catch (OperationCanceledException)
            {
                LogMessage?.Invoke("Quá trình kiểm tra đã bị hủy.");
            }

            return results;
        }

        private async Task<MstResult> CheckSingleMstAsync(string mst, CancellationToken cancellationToken)
        {
            await _semaphore.WaitAsync(cancellationToken);

            try
            {
                return await CheckMstWithRetryAsync(mst, cancellationToken);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async Task<MstResult> CheckMstWithRetryAsync(string mst, CancellationToken cancellationToken)
        {
            var retryCount = ConfigManager.Instance.GetInt("DEFAULT", "retry_count", 3);
            var timeout = ConfigManager.Instance.GetInt("DEFAULT", "timeout", 30);

            for (int attempt = 1; attempt <= retryCount; attempt++)
            {
                try
                {
                    using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    cts.CancelAfter(TimeSpan.FromSeconds(timeout));

                    var result = await CheckMstApiAsync(mst, cts.Token);
                    if (result.IsSuccess)
                        return result;

                    if (attempt < retryCount)
                    {
                        await Task.Delay(1000 * attempt, cancellationToken); // Exponential backoff
                    }
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    return new MstResult { Mst = mst, Error = "Đã hủy" };
                }
                catch (Exception ex)
                {
                    if (attempt == retryCount)
                    {
                        return new MstResult { Mst = mst, Error = $"Lỗi sau {retryCount} lần thử: {ex.Message}" };
                    }
                }
            }

            return new MstResult { Mst = mst, Error = "Không thể kiểm tra sau nhiều lần thử" };
        }

        private async Task<MstResult> CheckMstApiAsync(string mst, CancellationToken cancellationToken)
        {
            try
            {
                // Method 1: Try official API
                var result = await TryOfficialApiAsync(mst, cancellationToken);
                if (result.IsSuccess)
                    return result;

                // Method 2: Try alternative API
                result = await TryAlternativeApiAsync(mst, cancellationToken);
                if (result.IsSuccess)
                    return result;

                return new MstResult { Mst = mst, Error = "Không tìm thấy thông tin MST" };
            }
            catch (Exception ex)
            {
                return new MstResult { Mst = mst, Error = ex.Message };
            }
        }

        private async Task<MstResult> TryOfficialApiAsync(string mst, CancellationToken cancellationToken)
        {
            try
            {
                var client = new RestClient("https://masothue.com");
                var request = new RestRequest($"/Ajax/Search", Method.Post);
                
                request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                request.AddHeader("X-Requested-With", "XMLHttpRequest");
                request.AddParameter("application/x-www-form-urlencoded", $"q={mst}", ParameterType.RequestBody);

                var response = await client.ExecuteAsync(request, cancellationToken);
                
                if (response.IsSuccessful && !string.IsNullOrEmpty(response.Content))
                {
                    return ParseMasothueResponse(mst, response.Content);
                }
            }
            catch { }

            return new MstResult { Mst = mst, Error = "API chính không khả dụng" };
        }

        private async Task<MstResult> TryAlternativeApiAsync(string mst, CancellationToken cancellationToken)
        {
            try
            {
                var url = $"https://api.vietqr.io/v2/business/{mst}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                if (!string.IsNullOrEmpty(response))
                {
                    return ParseVietQrResponse(mst, response);
                }
            }
            catch { }

            return new MstResult { Mst = mst, Error = "API phụ không khả dụng" };
        }

        private MstResult ParseMasothueResponse(string mst, string content)
        {
            try
            {
                // Parse HTML response from masothue.com
                var nameMatch = Regex.Match(content, @"<h3[^>]*>([^<]+)</h3>", RegexOptions.IgnoreCase);
                var addressMatch = Regex.Match(content, @"Địa chỉ:\s*([^<\n]+)", RegexOptions.IgnoreCase);

                if (nameMatch.Success)
                {
                    return new MstResult
                    {
                        Mst = mst,
                        Status = "Hoạt động",
                        CompanyName = nameMatch.Groups[1].Value.Trim(),
                        Address = addressMatch.Success ? addressMatch.Groups[1].Value.Trim() : ""
                    };
                }
            }
            catch { }

            return new MstResult { Mst = mst, Error = "Không thể phân tích dữ liệu" };
        }

        private MstResult ParseVietQrResponse(string mst, string content)
        {
            try
            {
                using var doc = JsonDocument.Parse(content);
                var root = doc.RootElement;

                if (root.TryGetProperty("code", out var codeElement) && codeElement.GetString() == "00")
                {
                    if (root.TryGetProperty("data", out var dataElement))
                    {
                        var name = dataElement.TryGetProperty("name", out var nameElement) ? nameElement.GetString() : "";
                        
                        return new MstResult
                        {
                            Mst = mst,
                            Status = "Hoạt động",
                            CompanyName = name ?? "",
                            Address = ""
                        };
                    }
                }
            }
            catch { }

            return new MstResult { Mst = mst, Error = "Không thể phân tích dữ liệu JSON" };
        }

        public async Task<string> ExportResultsAsync(List<MstResult> results, string filePath)
        {
            try
            {
                var lines = new List<string>
                {
                    "MST,Trạng thái,Tên công ty,Địa chỉ,Lỗi"
                };

                foreach (var result in results)
                {
                    var line = $"\"{result.Mst}\",\"{result.Status}\",\"{result.CompanyName}\",\"{result.Address}\",\"{result.Error}\"";
                    lines.Add(line);
                }

                await File.WriteAllLinesAsync(filePath, lines, Encoding.UTF8);
                return $"Đã xuất {results.Count} kết quả ra file: {filePath}";
            }
            catch (Exception ex)
            {
                return $"Lỗi xuất file: {ex.Message}";
            }
        }
    }
}
