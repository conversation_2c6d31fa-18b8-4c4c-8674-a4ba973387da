#!/usr/bin/env python3
"""
Script để finetune TrOCR model với dữ liệu captcha
Sử dụng model có sẵn ở CaptchaData và dữ liệu ở train_data
Không resize ảnh, giữ nguyên kích thước gốc
"""

import os
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import (
    VisionEncoderDecoderModel,
    TrOCRProcessor,
    Trainer,
    TrainingArguments,
    default_data_collator
)
from PIL import Image
import glob
from pathlib import Path
import logging

# Thiết lập logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CaptchaDataset(Dataset):
    """Dataset cho dữ liệu captcha"""
    
    def __init__(self, image_dir, processor, max_target_length=128):
        self.image_dir = Path(image_dir)
        self.processor = processor
        self.max_target_length = max_target_length
        
        # Lấy tất cả file ảnh PNG
        self.image_paths = list(self.image_dir.glob("*.png"))
        logger.info(f"Tìm thấy {len(self.image_paths)} ảnh trong {image_dir}")
        
        # Tạo labels từ tên file (bỏ đuôi .png)
        self.labels = [path.stem for path in self.image_paths]
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        # Đọc ảnh
        image_path = self.image_paths[idx]
        image = Image.open(image_path).convert('RGB')
        
        # Lấy label từ tên file
        text = self.labels[idx]
        
        # Xử lý ảnh và text
        pixel_values = self.processor(image, return_tensors="pt").pixel_values
        labels = self.processor.tokenizer(
            text,
            padding="max_length",
            max_length=self.max_target_length,
            truncation=True,
            return_tensors="pt"
        ).input_ids
        
        return {
            "pixel_values": pixel_values.squeeze(),
            "labels": labels.squeeze()
        }

def main():
    # Đường dẫn
    model_path = "CaptchaData"
    train_data_path = "train_data"
    output_dir = "finetuned_model"
    
    # Kiểm tra đường dẫn
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Không tìm thấy model tại {model_path}")
    if not os.path.exists(train_data_path):
        raise FileNotFoundError(f"Không tìm thấy dữ liệu tại {train_data_path}")
    
    logger.info("Đang tải model và processor...")
    
    # Tải model và processor
    model = VisionEncoderDecoderModel.from_pretrained(model_path)
    processor = TrOCRProcessor.from_pretrained(model_path)
    
    # Thiết lập device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Sử dụng device: {device}")
    model.to(device)
    
    # Tạo dataset
    logger.info("Đang tạo dataset...")
    train_dataset = CaptchaDataset(train_data_path, processor)
    
    # Chia dataset thành train và validation (80-20)
    train_size = int(0.8 * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size]
    )
    
    logger.info(f"Train dataset: {len(train_dataset)} samples")
    logger.info(f"Validation dataset: {len(val_dataset)} samples")
    
    # Thiết lập training arguments
    training_args = TrainingArguments(
        output_dir=output_dir,
        per_device_train_batch_size=8,
        per_device_eval_batch_size=8,
        eval_strategy="steps",  # Sửa từ evaluation_strategy
        eval_steps=500,
        logging_steps=100,
        save_steps=500,
        save_total_limit=3,
        num_train_epochs=10,
        learning_rate=5e-5,
        warmup_steps=500,
        fp16=torch.cuda.is_available(),
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        report_to=None,  # Tắt wandb/tensorboard
    )
    
    # Tạo data collator tùy chỉnh cho VisionEncoderDecoder
    def collate_fn(batch):
        pixel_values = torch.stack([item["pixel_values"] for item in batch])
        labels = torch.stack([item["labels"] for item in batch])
        return {
            "pixel_values": pixel_values,
            "labels": labels
        }

    # Tạo trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        data_collator=collate_fn,
        tokenizer=processor.tokenizer,
    )
    
    # Bắt đầu training
    logger.info("Bắt đầu training...")
    trainer.train()
    
    # Lưu model cuối cùng
    logger.info("Đang lưu model...")
    trainer.save_model()
    processor.save_pretrained(output_dir)
    
    logger.info(f"Hoàn thành! Model đã được lưu tại {output_dir}")

if __name__ == "__main__":
    main()
