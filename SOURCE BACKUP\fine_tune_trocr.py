import os
from transformers import TrOCRProcessor, VisionEncoderDecoderModel, Seq2SeqTrainer, Seq2SeqTrainingArguments
from datasets import load_dataset, Dataset
from PIL import Image
import torch

# Đường dẫn file CSV và model output
DATA_DIR = os.path.dirname(os.path.abspath(__file__))
CSV_PATH = os.path.join(DATA_DIR, 'train_labels.csv')
OUTPUT_DIR = os.path.join(os.path.dirname(DATA_DIR), 'CaptchaData')

# Load processor và model gốc
model = VisionEncoderDecoderModel.from_pretrained('CaptchaData')
processor = TrOCRProcessor.from_pretrained('CaptchaData')

def load_examples(example):
    image = Image.open(example['image_path']).convert('RGB')
    return {'image': image, 'label': example['label']}

dataset = load_dataset('csv', data_files={'train': CSV_PATH})
dataset = dataset['train'].map(load_examples)

def preprocess(batch):
    pixel_values = processor(images=batch['image'], return_tensors='pt', padding=True).pixel_values
    labels = processor.tokenizer(batch['label'], padding='max_length', max_length=20, truncation=True).input_ids
    labels = [(l if l != processor.tokenizer.pad_token_id else -100) for l in labels]
    return {'pixel_values': pixel_values[0], 'labels': torch.tensor(labels)}

dataset = dataset.map(preprocess)

dataset.set_format(type='torch', columns=['pixel_values', 'labels'])

training_args = Seq2SeqTrainingArguments(
    output_dir=OUTPUT_DIR,
    per_device_train_batch_size=4,
    num_train_epochs=5,
    save_steps=500,
    logging_steps=100,
    learning_rate=5e-5,
    save_total_limit=2,
    fp16=torch.cuda.is_available(),
)

trainer = Seq2SeqTrainer(
    model=model,
    args=training_args,
    train_dataset=dataset,
    tokenizer=processor.feature_extractor,
)

trainer.train()

# Lưu lại model và processor
model.save_pretrained(OUTPUT_DIR)
processor.save_pretrained(OUTPUT_DIR)

print(f"Đã fine-tune xong, model lưu ở {OUTPUT_DIR}") 