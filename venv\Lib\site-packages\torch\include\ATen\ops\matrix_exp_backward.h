#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/matrix_exp_backward_ops.h>

namespace at {


// aten::matrix_exp_backward(Tensor self, Tensor grad) -> Tensor
inline at::Tensor matrix_exp_backward(const at::Tensor & self, const at::Tensor & grad) {
    return at::_ops::matrix_exp_backward::call(self, grad);
}

}
