import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
import threading
import trocr_solver
import os

class CaptchaSolverGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Test TrOCR Captcha Solver")
        self.root.geometry("700x500")
        # Frame để căn gi<PERSON>a <PERSON>nh
        self.image_frame = tk.Frame(root, width=600, height=250)
        self.image_frame.pack(pady=20)
        self.image_frame.pack_propagate(False)
        self.image_label = tk.Label(self.image_frame, text="Chưa chọn ảnh", width=60, height=15, anchor="center")
        self.image_label.pack(expand=True, fill="both")
        self.filename_var = tk.StringVar()
        self.filename_label = tk.Label(root, textvariable=self.filename_var, font=("Arial", 10, "italic"), fg="gray")
        self.filename_label.pack(pady=2)
        self.select_btn = tk.Button(root, text="Chọn ảnh", command=self.select_image)
        self.select_btn.pack(pady=5)
        # self.solve_btn = tk.Button(root, text="Giải Captcha", command=self.solve_captcha, state=tk.DISABLED)
        # self.solve_btn.pack(pady=5)
        self.result_var = tk.StringVar()
        self.result_label = tk.Label(root, textvariable=self.result_var, font=("Arial", 16), fg="blue")
        self.result_label.pack(pady=10)
        self.progress_var = tk.StringVar()
        self.progress_label = tk.Label(root, textvariable=self.progress_var, fg="green")
        self.progress_label.pack(pady=5)
        self.image_path = None
        self.imgtk = None
        self.model_loaded = False
        self.load_model_thread()

    def select_image(self):
        file_path = filedialog.askopenfilename(filetypes=[("Image Files", "*.png;*.jpg;*.jpeg;*.bmp")])
        if file_path:
            self.image_path = file_path
            img = Image.open(file_path)
            # Tính toán resize giữ tỉ lệ, vừa khung 600x250
            max_w, max_h = 600, 250
            w, h = img.size
            scale = min(max_w / w, max_h / h, 1.0)
            new_w, new_h = int(w * scale), int(h * scale)
            img = img.resize((new_w, new_h), Image.LANCZOS)
            self.imgtk = ImageTk.PhotoImage(img)
            self.image_label.config(image=self.imgtk, text="", anchor="center")
            self.filename_var.set(os.path.basename(file_path))
            self.result_var.set("")
            if self.model_loaded:
                self.result_var.set("Đang giải captcha...")
                self.root.update_idletasks()
                threading.Thread(target=self._solve_captcha_thread, daemon=True).start()
            else:
                self.result_var.set("Chờ model load xong...")

    def solve_captcha(self):
        # Không dùng nữa, giữ lại cho tương thích nếu cần
        if not self.image_path:
            messagebox.showwarning("Chưa chọn ảnh", "Vui lòng chọn ảnh captcha trước!")
            return
        self.result_var.set("Đang giải captcha...")
        threading.Thread(target=self._solve_captcha_thread, daemon=True).start()

    def _solve_captcha_thread(self):
        result = trocr_solver.solve_captcha(self.image_path)
        self.result_var.set(f"Kết quả: {result}")

    def load_model_thread(self):
        def progress_callback(percent):
            self.progress_var.set(f"Đang load model: {percent}%")
            self.root.update_idletasks()
        def load():
            try:
                trocr_solver.load_model(progress_callback)
                self.model_loaded = True
                self.progress_var.set("Đã load xong model!")
                if self.image_path:
                    self.result_var.set("Đang giải captcha...")
                    self.root.update_idletasks()
                    threading.Thread(target=self._solve_captcha_thread, daemon=True).start()
            except Exception as e:
                self.progress_var.set(f"Lỗi load model: {e}")
        threading.Thread(target=load, daemon=True).start()

def main():
    root = tk.Tk()
    app = CaptchaSolverGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main() 