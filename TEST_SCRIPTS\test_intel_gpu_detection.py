#!/usr/bin/env python3
"""
Script để test việc detect Intel GPU cụ thể
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hardware_detector import hardware_detector

def test_intel_gpu_detection():
    print("=== INTEL GPU DETECTION TEST ===\n")
    
    # Test hardware detector
    print("🔍 Testing Hardware Detector:")
    
    # L<PERSON>y thông tin chi tiết
    detailed_info = hardware_detector.get_detailed_info()
    
    print("📊 Detailed Hardware Info:")
    print(f"   CPU: {detailed_info['cpu']['name']}")
    print(f"   NVIDIA GPUs: {len(detailed_info['nvidia_gpus'])}")
    
    pytorch_info = detailed_info['pytorch']
    print(f"   PyTorch available: {pytorch_info['torch_available']}")
    print(f"   CUDA available: {pytorch_info['cuda_available']}")
    print(f"   XPU available: {pytorch_info.get('xpu_available', False)}")
    print(f"   Other backends: {pytorch_info.get('other_backends', [])}")
    
    # Test best device
    device_type, device_name, device_info = detailed_info['best_device']
    print(f"\n🏆 Best Device:")
    print(f"   Type: {device_type}")
    print(f"   Name: {device_name}")
    print(f"   Info: {device_info}")
    
    # Test display name
    display_name = hardware_detector.get_device_display_name()
    print(f"\n📱 Display Name: {display_name}")
    
    # Test PyTorch XPU specifically
    print(f"\n🔧 PyTorch XPU Test:")
    try:
        import torch
        print(f"   torch available: ✅")
        
        # Test XPU
        if hasattr(torch, 'xpu'):
            print(f"   torch.xpu module: ✅")
            
            if hasattr(torch.xpu, 'is_available'):
                try:
                    xpu_available = torch.xpu.is_available()
                    print(f"   torch.xpu.is_available(): {xpu_available}")
                    
                    if xpu_available:
                        if hasattr(torch.xpu, 'device_count'):
                            device_count = torch.xpu.device_count()
                            print(f"   torch.xpu.device_count(): {device_count}")
                        
                        if hasattr(torch.xpu, 'get_device_name'):
                            try:
                                device_name = torch.xpu.get_device_name(0)
                                print(f"   torch.xpu.get_device_name(0): {device_name}")
                            except Exception as e:
                                print(f"   torch.xpu.get_device_name(0): Error - {e}")
                        
                        # Test creating tensor on XPU
                        try:
                            test_tensor = torch.tensor([1, 2, 3], device='xpu')
                            print(f"   XPU tensor test: ✅ {test_tensor}")
                        except Exception as e:
                            print(f"   XPU tensor test: ❌ {e}")
                    
                except Exception as e:
                    print(f"   torch.xpu.is_available(): Error - {e}")
            else:
                print(f"   torch.xpu.is_available(): Not available")
        else:
            print(f"   torch.xpu module: ❌")
            
    except ImportError:
        print(f"   torch: ❌ Not installed")
    
    # Test manual Intel GPU detection
    print(f"\n🔍 Manual Intel GPU Detection:")
    try:
        import subprocess
        import platform
        
        if platform.system() == 'Windows':
            result = subprocess.run([
                'wmic', 'path', 'win32_VideoController', 'get', 'name,AdapterRAM'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                print("   Windows GPU List:")
                for line in lines[1:]:  # Skip header
                    line = line.strip()
                    if line and ('Intel' in line or 'UHD' in line or 'Iris' in line):
                        print(f"     - {line}")
        
        # Try Intel GPU tools
        try:
            result = subprocess.run(['intel_gpu_top', '--help'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("   intel_gpu_top: ✅ Available")
            else:
                print("   intel_gpu_top: ❌ Not available")
        except:
            print("   intel_gpu_top: ❌ Not found")
            
    except Exception as e:
        print(f"   Manual detection error: {e}")
    
    print("\n=== TEST COMPLETED ===")

def simulate_intel_gpu_environment():
    """Mô phỏng môi trường có Intel GPU"""
    print("\n=== SIMULATING INTEL GPU ENVIRONMENT ===")
    
    # Clear cache để test lại
    hardware_detector.clear_cache()
    
    # Giả lập có Intel XPU
    print("🔧 Simulating Intel XPU environment...")
    
    # Tạo mock pytorch info
    mock_pytorch_info = {
        'torch_available': True,
        'cuda_available': False,
        'cuda_device_count': 0,
        'cuda_devices': [],
        'current_device': 'xpu',
        'xpu_available': True,
        'xpu_device_count': 1,
        'other_backends': ['XPU (Intel UHD Graphics 630)']
    }
    
    # Inject vào cache
    hardware_detector._detection_cache['pytorch'] = mock_pytorch_info
    
    # Test với mock data
    device_type, device_name, device_info = hardware_detector.determine_best_device()
    print(f"   Mock Best Device: {device_type} - {device_name}")
    
    display_name = hardware_detector.get_device_display_name()
    print(f"   Mock Display Name: {display_name}")
    
    # Clear cache
    hardware_detector.clear_cache()
    print("   Cache cleared")

if __name__ == "__main__":
    test_intel_gpu_detection()
    simulate_intel_gpu_environment()
