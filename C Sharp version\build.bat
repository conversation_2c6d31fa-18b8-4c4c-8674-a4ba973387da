@echo off
echo ========================================
echo Building Kiem Tra MST C# Version 5.0.12
echo Target Framework: .NET 8.0
echo ========================================

echo.
echo 1. Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

echo.
echo 2. Building in Release mode...
dotnet build --configuration Release --no-restore
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo 3. Publishing standalone executable...
dotnet publish --configuration Release --self-contained true --runtime win-x64 --output "bin\Publish"
if %errorlevel% neq 0 (
    echo ERROR: Publish failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo Executable location: bin\Publish\KiemTraMST.exe
echo.
echo To run the application:
echo   1. Navigate to bin\Publish folder
echo   2. Run KiemTraMST.exe
echo.
pause
