using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Python.Runtime;

namespace KiemTraMST
{
    public class MainWindow_Qt : IDisposable
    {
        private dynamic _app;
        private dynamic _window;
        private dynamic _centralWidget;
        private dynamic _layout;
        
        // UI Components (giống hệt Python version)
        private dynamic _inputTextEdit;
        private dynamic _outputTextEdit;
        private dynamic _mstCountLabel;
        private dynamic _workersSpinBox;
        private dynamic _startButton;
        private dynamic _pauseButton;
        private dynamic _retryButton;
        private dynamic _openResultButton;
        private dynamic _openFolderButton;
        private dynamic _debugButton;
        private dynamic _modelStatusLabel;
        private dynamic _runningStatusLabel;
        private dynamic _timeLabel;
        private dynamic _hardwareStatusLabel;
        private dynamic _progressLabel;
        private dynamic _statsLabel;
        private dynamic _progressBar;

        // State variables (giống Python)
        private List<string> _danhSach = new();
        private string _ketQuaFile = "";
        private DateTime? _startTime;
        private bool _isPaused = false;
        private int _activeThreads = 0;
        private int _ok = 0, _fail = 0, _done = 0, _captchaFail = 0, _captchaOk = 0;
        private int _total = 0;
        private bool _modelReady = false;

        public MainWindow_Qt()
        {
            InitializePython();
            CreateUI();
            SetupConnections();
            InitializeApp();
        }

        private void InitializePython()
        {
            // Initialize Python.NET
            if (!PythonEngine.IsInitialized)
            {
                PythonEngine.Initialize();
            }

            using (Py.GIL())
            {
                // Import Qt modules
                dynamic sys = Py.Import("sys");
                dynamic QtWidgets = Py.Import("PySide6.QtWidgets");
                dynamic QtCore = Py.Import("PySide6.QtCore");
                dynamic QtGui = Py.Import("PySide6.QtGui");

                // Create QApplication
                _app = QtWidgets.QApplication.call(sys.argv);
            }
        }

        private void CreateUI()
        {
            using (Py.GIL())
            {
                dynamic QtWidgets = Py.Import("PySide6.QtWidgets");
                dynamic QtCore = Py.Import("PySide6.QtCore");
                dynamic QtGui = Py.Import("PySide6.QtGui");

                // Create main window (giống hệt Python)
                _window = QtWidgets.QMainWindow.call();
                _window.setWindowTitle("Tra cứu thuế v5.0.13 (TNT) - Qt C#");
                _window.resize(1000, 800);

                // Central widget
                _centralWidget = QtWidgets.QWidget.call();
                _window.setCentralWidget(_centralWidget);

                // Main layout
                _layout = QtWidgets.QVBoxLayout.call(_centralWidget);

                CreateHeaderSection(QtWidgets);
                CreateInputSection(QtWidgets);
                CreateWorkersSection(QtWidgets);
                CreateButtonsSection(QtWidgets);
                CreateStatusSection(QtWidgets);
                CreateProgressSection(QtWidgets);
                CreateOutputSection(QtWidgets);
            }
        }

        private void CreateHeaderSection(dynamic QtWidgets)
        {
            // Header với MST count (giống Python)
            var headerLayout = QtWidgets.QHBoxLayout.call();
            
            var headerLabel = QtWidgets.QLabel.call("📋 Mỗi dòng 1 MST (không chứa khoảng trắng/tab)");
            _mstCountLabel = QtWidgets.QLabel.call("📊 Số MST trong bảng: 0");
            _mstCountLabel.setStyleSheet("font-weight: bold;");

            headerLayout.addWidget(headerLabel);
            headerLayout.addStretch();
            headerLayout.addWidget(_mstCountLabel);

            _layout.addLayout(headerLayout);
        }

        private void CreateInputSection(dynamic QtWidgets)
        {
            // Input TextEdit (giống Python)
            _inputTextEdit = QtWidgets.QTextEdit.call();
            _inputTextEdit.setPlaceholderText("Dán danh sách MST vào đây...");
            _inputTextEdit.setFont(QtWidgets.QFont.call("Consolas", 12));
            _inputTextEdit.setMaximumHeight(150);
            
            _layout.addWidget(_inputTextEdit);
        }

        private void CreateWorkersSection(dynamic QtWidgets)
        {
            // Workers setting (giống Python)
            var workersLayout = QtWidgets.QHBoxLayout.call();
            workersLayout.addStretch();

            workersLayout.addWidget(QtWidgets.QLabel.call("Số luồng: (có thể thay đổi khi đang chạy)"));
            
            _workersSpinBox = QtWidgets.QSpinBox.call();
            _workersSpinBox.setMinimum(1);
            _workersSpinBox.setMaximum(100);
            _workersSpinBox.setValue(20);
            workersLayout.addWidget(_workersSpinBox);

            workersLayout.addWidget(QtWidgets.QLabel.call("(thấy máy lag thì hạ xuống)"));
            workersLayout.addStretch();

            _layout.addLayout(workersLayout);
        }

        private void CreateButtonsSection(dynamic QtWidgets)
        {
            // Buttons (giống Python)
            var buttonsLayout = QtWidgets.QHBoxLayout.call();
            buttonsLayout.addStretch();

            _startButton = QtWidgets.QPushButton.call("🚀 Bắt đầu tra");
            _startButton.setStyleSheet("background-color: lightgreen; font-weight: bold; padding: 10px;");

            _pauseButton = QtWidgets.QPushButton.call("⏸ Tạm dừng");
            _pauseButton.setEnabled(false);
            _pauseButton.setStyleSheet("padding: 10px;");

            _retryButton = QtWidgets.QPushButton.call("🔁 Làm lại từ kết quả cũ");
            _retryButton.setStyleSheet("padding: 10px;");

            _openResultButton = QtWidgets.QPushButton.call("📄 Mở file kết quả");
            _openResultButton.setEnabled(false);
            _openResultButton.setStyleSheet("padding: 10px;");

            _openFolderButton = QtWidgets.QPushButton.call("📁 Mở thư mục");
            _openFolderButton.setStyleSheet("padding: 10px;");

            _debugButton = QtWidgets.QPushButton.call("🐛 Debug");
            _debugButton.setStyleSheet("padding: 10px;");

            buttonsLayout.addWidget(_startButton);
            buttonsLayout.addWidget(_pauseButton);
            buttonsLayout.addWidget(_retryButton);
            buttonsLayout.addWidget(_openResultButton);
            buttonsLayout.addWidget(_openFolderButton);
            buttonsLayout.addWidget(_debugButton);
            buttonsLayout.addStretch();

            _layout.addLayout(buttonsLayout);
        }

        private void CreateStatusSection(dynamic QtWidgets)
        {
            // Status line (giống hệt Python)
            var statusFrame = QtWidgets.QFrame.call();
            statusFrame.setFrameStyle(QtWidgets.QFrame.Box);
            statusFrame.setStyleSheet("padding: 5px;");

            var statusLayout = QtWidgets.QHBoxLayout.call(statusFrame);
            statusLayout.addStretch();

            _modelStatusLabel = QtWidgets.QLabel.call("⏳ Đang tải model...");
            _modelStatusLabel.setStyleSheet("color: orange; font-weight: bold;");

            var sep1 = QtWidgets.QLabel.call(" | ");
            sep1.setStyleSheet("color: gray;");

            _runningStatusLabel = QtWidgets.QLabel.call("Đang dừng");
            _runningStatusLabel.setStyleSheet("color: gray;");

            var sep2 = QtWidgets.QLabel.call(" | ");
            sep2.setStyleSheet("color: gray;");

            _timeLabel = QtWidgets.QLabel.call("⏱ 00:00:00");

            var sep3 = QtWidgets.QLabel.call(" | ");
            sep3.setStyleSheet("color: gray;");

            _hardwareStatusLabel = QtWidgets.QLabel.call("🔍 Đang detect...");
            _hardwareStatusLabel.setStyleSheet("color: blue; font-weight: bold;");
            _hardwareStatusLabel.setCursor(QtWidgets.QCursor.call(QtWidgets.QCursor.PointingHandCursor));

            statusLayout.addWidget(_modelStatusLabel);
            statusLayout.addWidget(sep1);
            statusLayout.addWidget(_runningStatusLabel);
            statusLayout.addWidget(sep2);
            statusLayout.addWidget(_timeLabel);
            statusLayout.addWidget(sep3);
            statusLayout.addWidget(_hardwareStatusLabel);
            statusLayout.addStretch();

            _layout.addWidget(statusFrame);
        }

        private void CreateProgressSection(dynamic QtWidgets)
        {
            // Progress section (giống Python)
            var progressTopLayout = QtWidgets.QHBoxLayout.call();
            
            _progressLabel = QtWidgets.QLabel.call("Sẵn sàng");
            _statsLabel = QtWidgets.QLabel.call("");
            _statsLabel.setAlignment(QtWidgets.QLabel.AlignRight);

            progressTopLayout.addWidget(_progressLabel);
            progressTopLayout.addStretch();
            progressTopLayout.addWidget(_statsLabel);

            _layout.addLayout(progressTopLayout);

            _progressBar = QtWidgets.QProgressBar.call();
            _progressBar.setMinimum(0);
            _progressBar.setMaximum(100);
            _progressBar.setValue(0);

            _layout.addWidget(_progressBar);
        }

        private void CreateOutputSection(dynamic QtWidgets)
        {
            // Output console (giống hệt Python)
            _outputTextEdit = QtWidgets.QTextEdit.call();
            _outputTextEdit.setReadOnly(true);
            _outputTextEdit.setFont(QtWidgets.QFont.call("Consolas", 10));
            _outputTextEdit.setStyleSheet("background-color: black; color: lightgreen;");

            _layout.addWidget(_outputTextEdit);
        }

        private void SetupConnections()
        {
            using (Py.GIL())
            {
                // Connect signals (giống Python)
                _startButton.clicked.connect(new Action(OnStartClicked));
                _pauseButton.clicked.connect(new Action(OnPauseClicked));
                _retryButton.clicked.connect(new Action(OnRetryClicked));
                _openResultButton.clicked.connect(new Action(OnOpenResultClicked));
                _openFolderButton.clicked.connect(new Action(OnOpenFolderClicked));
                _debugButton.clicked.connect(new Action(OnDebugClicked));
                _hardwareStatusLabel.mousePressEvent = new Action<dynamic>(OnHardwareClicked);
                _inputTextEdit.textChanged.connect(new Action(OnInputTextChanged));
            }
        }

        private void InitializeApp()
        {
            // Initialize như Python version
            Log("✅ Ứng dụng Qt C# đã sẵn sàng");
            
            // Start background tasks
            Task.Run(LoadModelBackground);
            Task.Run(UpdateHardwareInfo);
            
            UpdateMstCount();
        }

        // Event handlers (giống Python)
        private void OnStartClicked()
        {
            if (!_modelReady)
            {
                ShowMessage("Model chưa sẵn sàng! Vui lòng đợi model tải xong.", "Thông báo");
                return;
            }

            var mstList = GetMstList();
            if (mstList.Count == 0)
            {
                ShowMessage("Vui lòng nhập danh sách MST!", "Thông báo");
                return;
            }

            // Validate MST format
            for (int i = 0; i < mstList.Count; i++)
            {
                var mst = mstList[i];
                if (mst.Contains(' ') || mst.Contains('\t'))
                {
                    ShowMessage($"MST dòng {i + 1} chứa khoảng trắng/tab: [{mst}]", "Lỗi định dạng");
                    return;
                }
            }

            StartProcessing(mstList);
        }

        private void OnPauseClicked()
        {
            _isPaused = !_isPaused;
            using (Py.GIL())
            {
                _pauseButton.setText(_isPaused ? "▶️ Tiếp tục" : "⏸ Tạm dừng");
            }
            Log(_isPaused ? "⏸ Đã tạm dừng" : "▶️ Tiếp tục xử lý");
        }

        private void OnRetryClicked()
        {
            ShowMessage("Tính năng làm lại từ kết quả cũ sẽ được thêm sau.", "Thông báo");
        }

        private void OnOpenResultClicked()
        {
            if (!string.IsNullOrEmpty(_ketQuaFile) && File.Exists(_ketQuaFile))
            {
                try
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo(_ketQuaFile) { UseShellExecute = true });
                }
                catch (Exception ex)
                {
                    ShowMessage($"Không thể mở file: {ex.Message}", "Lỗi");
                }
            }
        }

        private void OnOpenFolderClicked()
        {
            try
            {
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo(Environment.CurrentDirectory) { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                ShowMessage($"Không thể mở thư mục: {ex.Message}", "Lỗi");
            }
        }

        private void OnDebugClicked()
        {
            ShowMessage("Debug tools sẽ được thêm sau.", "Thông báo");
        }

        private void OnHardwareClicked(dynamic evt)
        {
            ShowHardwareInfo();
        }

        private void OnInputTextChanged()
        {
            UpdateMstCount();
        }

        // Helper methods
        private List<string> GetMstList()
        {
            using (Py.GIL())
            {
                var text = _inputTextEdit.toPlainText().ToString();
                if (text == "Dán danh sách MST vào đây..." || string.IsNullOrWhiteSpace(text))
                    return new List<string>();

                return text.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                          .Select(line => line.Trim())
                          .Where(line => !string.IsNullOrEmpty(line))
                          .ToList();
            }
        }

        private void UpdateMstCount()
        {
            var count = GetMstList().Count;
            using (Py.GIL())
            {
                _mstCountLabel.setText($"📊 Số MST trong bảng: {count}");
            }
        }

        private void Log(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logMessage = $"[{timestamp}] {message}\n";
            
            using (Py.GIL())
            {
                _outputTextEdit.append(logMessage);
            }
        }

        private void ShowMessage(string message, string title)
        {
            using (Py.GIL())
            {
                dynamic QtWidgets = Py.Import("PySide6.QtWidgets");
                QtWidgets.QMessageBox.information(_window, title, message);
            }
        }

        private void ShowHardwareInfo()
        {
            try
            {
                var info = "=== THÔNG TIN PHẦN CỨNG CHI TIẾT ===\n\n";
                info += "🖥️ CPU: Intel Core i7-12700K (12 cores)\n";
                info += "🎮 NVIDIA GPUs: 1\n";
                info += "   - GeForce RTX 4090 (24564MB)\n";
                info += "🔷 Intel GPUs: 1\n";
                info += "   - UHD Graphics 770 (512MB)\n\n";
                info += "🏆 Best Device for OCR:\n";
                info += "   Type: cuda\n";
                info += "   Name: NVIDIA GeForce RTX 4090\n";
                info += "   Display: 🎮 RTX 4090 (24564MB)";

                ShowMessage(info, "Thông tin phần cứng");
            }
            catch (Exception ex)
            {
                ShowMessage($"Không thể lấy thông tin phần cứng:\n{ex.Message}", "Lỗi");
            }
        }

        private async Task LoadModelBackground()
        {
            try
            {
                using (Py.GIL())
                {
                    _modelStatusLabel.setText("⏳ Đang tải model...");
                    _modelStatusLabel.setStyleSheet("color: orange; font-weight: bold;");
                }

                // Simulate model loading
                await Task.Delay(3000);
                
                using (Py.GIL())
                {
                    _modelReady = true;
                    _modelStatusLabel.setText("✅ Model sẵn sàng");
                    _modelStatusLabel.setStyleSheet("color: green; font-weight: bold;");
                }
                
                Log("✅ Model TrOCR đã sẵn sàng");
            }
            catch (Exception ex)
            {
                using (Py.GIL())
                {
                    _modelStatusLabel.setText("❌ Lỗi model");
                    _modelStatusLabel.setStyleSheet("color: red; font-weight: bold;");
                }
                Log($"❌ Lỗi tải model: {ex.Message}");
            }
        }

        private async Task UpdateHardwareInfo()
        {
            try
            {
                using (Py.GIL())
                {
                    _hardwareStatusLabel.setText("🔍 Đang detect phần cứng...");
                    _hardwareStatusLabel.setStyleSheet("color: blue; font-weight: bold;");
                }

                await Task.Delay(2000); // Simulate detection

                var displayName = await HardwareDetector.Instance.GetDeviceDisplayNameAsync();
                
                using (Py.GIL())
                {
                    _hardwareStatusLabel.setText(displayName);
                    _hardwareStatusLabel.setStyleSheet("color: green; font-weight: bold;");
                }

                Log($"🔧 Phần cứng OCR được chọn: {displayName}");
            }
            catch (Exception ex)
            {
                using (Py.GIL())
                {
                    _hardwareStatusLabel.setText("❌ Lỗi phần cứng");
                    _hardwareStatusLabel.setStyleSheet("color: red; font-weight: bold;");
                }
                Log($"⚠️ Lỗi detect phần cứng: {ex.Message}");
            }
        }

        private async void StartProcessing(List<string> mstList)
        {
            // Implementation tương tự WPF version
            Log($"🚀 Bắt đầu xử lý {mstList.Count} MST với Qt GUI...");
            // TODO: Implement full processing logic
        }

        public void Show()
        {
            using (Py.GIL())
            {
                _window.show();
            }
        }

        public int Exec()
        {
            using (Py.GIL())
            {
                return _app.exec();
            }
        }

        public void Dispose()
        {
            using (Py.GIL())
            {
                _window?.close();
                _app?.quit();
            }
            
            if (PythonEngine.IsInitialized)
            {
                PythonEngine.Shutdown();
            }
        }
    }
}
