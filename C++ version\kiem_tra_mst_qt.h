#ifndef KIEM_TRA_MST_QT_H
#define KIEM_TRA_MST_QT_H

#include <QtWidgets/QApplication>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFileDialog>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QFrame>

#include <QtCore/Qt>
#include <QtCore/QRunnable>
#include <QtCore/QThreadPool>
#include <QtCore/QObject>
#include <QtCore/QTimer>
#include <QtCore/QSize>
#include <QtCore/QSettings>
#include <QtCore/QStandardPaths>
#include <QtCore/QDir>
#include <QtCore/QDateTime>
#include <QtCore/QJsonDocument>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QRegularExpression>
#include <QtCore/QMutex>
#include <QtCore/QMutexLocker>

#include <QtGui/QFont>
#include <QtGui/QMovie>
#include <QtGui/QPixmap>
#include <QtGui/QTransform>
#include <QtGui/QIcon>

#include <QtCore/QEvent>

#include <QtNetwork/QNetworkAccessManager>
#include <QtNetwork/QNetworkRequest>
#include <QtNetwork/QNetworkReply>
#include <QtNetwork/QNetworkCookieJar>
#include <QUrlQuery>

#include <QProcess>
#include <QDesktopServices>
#include <QUrl>
#include <QFileInfo>
#include <QCoreApplication>
#include <QCloseEvent>
#include <QTextStream>
#include <QEventLoop>
#include <QBuffer>
#include <QImageReader>
#include <QPainter>

#include <functional>

// TrOCR functions
QByteArray preprocessImage(const QByteArray& imageData);
QString solveCaptcha(const QByteArray& imageData);
QString solveCaptchaFallback(const QByteArray& imageData);
void loadModel(std::function<void(int)> progressCallback = nullptr);
void loadModel(); // Overload without callback

// Debug flags structure
struct DebugFlags {
    bool save_response = false;
    bool wrong_captcha = false;
    bool always_fail = false;
};

// Forward declarations
class WorkerSignals;
class TraCuuWorker;
class EmojiAnimationLabel;
class FlippingGIFLabel;
class LoadingModelDialog;
class DebugDialog;
class RetryDialog;
class MainWindow;

class WorkerSignals : public QObject {
    Q_OBJECT

public:
    explicit WorkerSignals(QObject* parent = nullptr) : QObject(parent) {}

signals:
    void log(const QString& message);
    void updateResult(int index, const QString& text);
    void captchaResult(bool success);
    void taskCompleted(bool success);
    void finished();
    void error(const QString& errorMessage);
};

class TraCuuWorker : public QRunnable {
public:
    explicit TraCuuWorker(const QString& mst, int idx, const DebugFlags& debugFlags, QObject* parent = nullptr);
    ~TraCuuWorker() override;
    
    void run() override;
    
    WorkerSignals* getSignals() const { return m_signals; }

private:
    QString traCuu();
    
    QString m_mst;
    int m_idx;
    WorkerSignals* m_signals;
    QNetworkAccessManager* m_session;
    DebugFlags m_debugFlags;
};

class EmojiAnimationLabel : public QLabel {
    Q_OBJECT

public:
    explicit EmojiAnimationLabel(const QStringList& emojis, int delay = 200, int size = 32, QWidget* parent = nullptr);

private slots:
    void animate();

private:
    QStringList m_emojis;
    int m_currentIndex;
    QTimer* m_timer;
};

class FlippingGIFLabel : public QLabel {
    Q_OBJECT

public:
    explicit FlippingGIFLabel(const QString& gifPath, int flipInterval = 2000, const QSize& size = QSize(32, 32), QWidget* parent = nullptr);
    ~FlippingGIFLabel() override;

protected:
    bool event(QEvent* event) override;

private slots:
    void toggleFlip();
    void onFrameChanged(int frameNumber = -1);

private:
    QMovie* m_movie;
    QTimer* m_flipper;
    bool m_isFlipped;
};

class LoadingModelDialog : public QDialog {
    Q_OBJECT

public:
    explicit LoadingModelDialog(QWidget* parent = nullptr);

public slots:
    void updateProgress(int value);
    void setError(const QString& message);

private:
    EmojiAnimationLabel* m_emoji;
    QLabel* m_label;
    QProgressBar* m_progress;
};

class DebugDialog : public QDialog {
    Q_OBJECT

public:
    explicit DebugDialog(const DebugFlags& currentFlags, QWidget* parent = nullptr);

    DebugFlags getNewFlags() const { return m_newFlags; }

private slots:
    void saveAndClose();

private:
    DebugFlags m_newFlags;
    QCheckBox* m_saveResponseCb;
    QCheckBox* m_wrongCaptchaCb;
    QCheckBox* m_alwaysFailCb;
};

class RetryDialog : public QDialog {
    Q_OBJECT

public:
    explicit RetryDialog(QWidget* parent = nullptr);

    struct RetryOptions {
        QString path;
        bool retryPending;
        bool retryFailed;
    };

    RetryOptions getOptions() const;

private slots:
    void browseFile();

private:
    QLineEdit* m_pathEdit;
    QCheckBox* m_retryPendingCb;
    QCheckBox* m_retryFailedCb;
};

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget* parent = nullptr);
    ~MainWindow() override;

protected:
    void closeEvent(QCloseEvent* event) override;

private slots:
    void log(const QString& message);
    void updateMstCount();
    void updateStatus();
    void updateTimeDisplay();
    void onWorkersChange();
    void togglePause();
    void batDau();
    void lamLai();
    void openResultFileDirectly();
    void openResultFolder();
    void openDebugWindow();
    
    // Worker signal handlers
    void onWorkerUpdate(int idx, const QString& text);
    void onTaskCompleted(bool isOk);
    void onCaptchaResult(bool isOk);
    void onWorkerFinished();
    void handleWorkerError(const QString& errorMsg);
    
    // Model loading handlers
    void onModelProgress(int percent);
    void onModelLoaded();
    void onModelLoadError(const QString& errorMsg);

private:
    void setupState();
    void setupUi();
    void setupConnections();
    void loadModelBg();
    void checkSettingFile();
    void createDefaultSettingFile();
    void saveWorkersToSetting(int workersCount);
    void showKqLoading(bool show = true);
    void showKqDone();
    void processQueue();
    void finishProcessing();
    void writeResultFile();
    void retryProcessing(const QString& filePath, bool retryPending, bool retryFailed);
    void autoLamLaiFromLastResult();
    
    // UI components
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    QLabel* m_mstCountLabel;
    QTextEdit* m_textBox;
    QLineEdit* m_entryWorkers;
    QPushButton* m_btnStart;
    QPushButton* m_btnPause;
    QPushButton* m_btnRetry;
    QPushButton* m_btnOpenResult;
    QPushButton* m_btnOpenFolder;
    QPushButton* m_btnDebug;
    QPushButton* m_btnExit;
    QHBoxLayout* m_kqFrame;
    QLabel* m_labelKq;
    QWidget* m_kqWidget;
    QLabel* m_timeLabel;
    QLabel* m_statusLabel;
    QLabel* m_modelStatusLabel;
    QTextEdit* m_logBox;
    QTimer* m_timer;
    
    // State variables
    QStringList m_outputLinesFull;
    QList<int> m_updateIndices;
    QStringList m_danhSach;
    QString m_ketquaFile;
    qint64 m_startTime;
    bool m_isPaused;
    int m_activeThreads;
    int m_ok, m_fail, m_done, m_captchaFail, m_captchaOk;
    int m_total;
    DebugFlags m_debugFlags;
    QStringList m_outputLines;
    QList<QPair<int, QString>> m_taskQueue;
    bool m_modelReady;
    bool m_pendingStart;
    int m_lastModelPercent;
    
    // Constants
    static const QString MST_COUNT_PREFIX;
    static const QString VERSION;
};

#endif // KIEM_TRA_MST_QT_H
