#include <windows.h>

IDI_ICON1	ICON	"C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C++ version\\default.ico"

VS_VERSION_INFO VERSIONINFO
	FILEVERSION 5,0,0,0
	PRODUCTVERSION 5,0,0,0
	FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
	FILEFLAGS VS_FF_DEBUG
#else
	FILEFLAGS 0x0L
#endif
	FILEOS VOS_NT_WINDOWS32
	FILETYPE VFT_DLL
	FILESUBTYPE VFT2_UNKNOWN
	BEGIN
		BLOCK "StringFileInfo"
		BEGIN
			BLOCK "040904b0"
			BEGIN
				VALUE "CompanyName", "\0"
				VALUE "FileDescription", "Tra cuu MST - C++ Version\0"
				VALUE "FileVersion", "*******\0"
				VALUE "LegalCopyright", "TNT\0"
				VALUE "OriginalFilename", "kiem_tra_mst_qt.exe\0"
				VALUE "ProductName", "kiem_tra_mst_qt\0"
				VALUE "ProductVersion", "*******\0"
				VALUE "InternalName", "\0"
				VALUE "Comments", "\0"
				VALUE "LegalTrademarks", "\0"
			END
		END
		BLOCK "VarFileInfo"
		BEGIN
			VALUE "Translation", 0x0409, 1200
		END
	END
/* End of Version info */

