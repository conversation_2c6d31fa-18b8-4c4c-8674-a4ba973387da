using System;
using System.Threading.Tasks;
using System.Windows;

namespace KiemTraMST
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Initialize hardware detector on startup (async, non-blocking)
            _ = Task.Run(() => HardwareDetector.Instance);
        }
    }
}
