@echo off
echo Building Kiem Tra MST Qt C++ Version...

REM Method 1: Try qmake first (recommended)
echo Trying qmake build...
qmake kiem_tra_mst_qt.pro
if %ERRORLEVEL% EQU 0 (
    echo qmake successful, building with make...
    make release
    if %ERRORLEVEL% EQU 0 (
        echo Build completed with qmake!
        goto :copy_files
    )
)

REM Method 2: Fallback to CMake
echo qmake failed, trying CMake...
if not exist build mkdir build
cd build

cmake .. -G "Visual Studio 17 2022" -A x64
if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

cmake --build . --config Release
if %ERRORLEVEL% NEQ 0 (
    echo CMake build failed!
    pause
    exit /b 1
)

cd ..
echo Build completed with CMake!

:copy_files
REM Copy additional files if needed
if exist setting.ini copy setting.ini bin\
if exist *.py copy *.py bin\
if exist resource xcopy /E /I resource bin\resource\
if exist CaptchaData xcopy /E /I CaptchaData bin\CaptchaData\
if exist default.ico copy default.ico bin\

echo.
echo Build completed successfully!
echo Executable location:
if exist bin\kiem_tra_mst_qt.exe (
    echo   bin\kiem_tra_mst_qt.exe
) else if exist build\bin\Release\kiem_tra_mst_qt.exe (
    echo   build\bin\Release\kiem_tra_mst_qt.exe
) else (
    echo   Build may have failed - executable not found
)

pause
