@echo off
echo Opening Qt Creator with C++ project...
echo ====================================

set QT_CREATOR="D:\PROGRAMS_D\Qt\Tools\QtCreator\bin\qtcreator.exe"
set PROJECT_FILE="%~dp0kiem_tra_mst_qt.pro"

echo Qt Creator: %QT_CREATOR%
echo Project File: %PROJECT_FILE%
echo.

if not exist %QT_CREATOR% (
    echo ❌ Qt Creator not found at: %QT_CREATOR%
    echo Please check the path or install Qt Creator
    pause
    exit /b 1
)

if not exist %PROJECT_FILE% (
    echo ❌ Project file not found: %PROJECT_FILE%
    echo Please make sure you're in the C++ version directory
    pause
    exit /b 1
)

echo ✅ Opening Qt Creator with C++ project...
echo.
echo 📋 Next steps in Qt Creator:
echo 1. Configure Project with Qt 6.9.1 kit
echo 2. Build → Build All (Ctrl+Shift+B)
echo 3. Run (Ctrl+R)
echo.

start "" %QT_CREATOR% %PROJECT_FILE%

echo 🎉 Qt Creator opened successfully!
echo If build fails, check that you have a C++ compiler configured.
echo.
pause
