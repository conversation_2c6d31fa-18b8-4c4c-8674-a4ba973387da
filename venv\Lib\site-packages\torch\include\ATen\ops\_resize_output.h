#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/_resize_output_ops.h>

namespace at {


// aten::_resize_output_(Tensor(a!) self, SymInt[] size, Device device) -> Tensor(a!)
inline const at::Tensor & _resize_output_(const at::Tensor & self, at::IntArrayRef size, at::Device device) {
    return at::_ops::_resize_output_::call(self, c10::fromIntArrayRefSlow(size), device);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, int64_t>::value>>
  const at::Tensor & _resize_output_(const at::Tensor & self, at::IntArrayRef size, at::Device device) {
    return at::_ops::_resize_output_::call(self, c10::fromIntArrayRefSlow(size), device);
  }
}

// aten::_resize_output_(Tensor(a!) self, SymInt[] size, Device device) -> Tensor(a!)
inline const at::Tensor & _resize_output__symint(const at::Tensor & self, c10::SymIntArrayRef size, at::Device device) {
    return at::_ops::_resize_output_::call(self, size, device);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, c10::SymInt>::value>>
  const at::Tensor & _resize_output_(const at::Tensor & self, c10::SymIntArrayRef size, at::Device device) {
    return at::_ops::_resize_output_::call(self, size, device);
  }
}

// aten::_resize_output.out(Tensor self, SymInt[] size, Device device, *, Tensor(a!) out) -> Tensor(a!)
inline const at::Tensor & _resize_output_out(const at::Tensor & out, const at::Tensor & self, at::IntArrayRef size, at::Device device) {
    return at::_ops::_resize_output_out::call(self, c10::fromIntArrayRefSlow(size), device, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, int64_t>::value>>
  const at::Tensor & _resize_output_out(const at::Tensor & out, const at::Tensor & self, at::IntArrayRef size, at::Device device) {
    return at::_ops::_resize_output_out::call(self, c10::fromIntArrayRefSlow(size), device, out);
  }
}

// aten::_resize_output.out(Tensor self, SymInt[] size, Device device, *, Tensor(a!) out) -> Tensor(a!)
inline const at::Tensor & _resize_output_outf(const at::Tensor & self, at::IntArrayRef size, at::Device device, const at::Tensor & out) {
    return at::_ops::_resize_output_out::call(self, c10::fromIntArrayRefSlow(size), device, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, int64_t>::value>>
  const at::Tensor & _resize_output_outf(const at::Tensor & self, at::IntArrayRef size, at::Device device, const at::Tensor & out) {
    return at::_ops::_resize_output_out::call(self, c10::fromIntArrayRefSlow(size), device, out);
  }
}

// aten::_resize_output.out(Tensor self, SymInt[] size, Device device, *, Tensor(a!) out) -> Tensor(a!)
inline const at::Tensor & _resize_output_symint_out(const at::Tensor & out, const at::Tensor & self, c10::SymIntArrayRef size, at::Device device) {
    return at::_ops::_resize_output_out::call(self, size, device, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, c10::SymInt>::value>>
  const at::Tensor & _resize_output_out(const at::Tensor & out, const at::Tensor & self, c10::SymIntArrayRef size, at::Device device) {
    return at::_ops::_resize_output_out::call(self, size, device, out);
  }
}

// aten::_resize_output.out(Tensor self, SymInt[] size, Device device, *, Tensor(a!) out) -> Tensor(a!)
inline const at::Tensor & _resize_output_symint_outf(const at::Tensor & self, c10::SymIntArrayRef size, at::Device device, const at::Tensor & out) {
    return at::_ops::_resize_output_out::call(self, size, device, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, c10::SymInt>::value>>
  const at::Tensor & _resize_output_outf(const at::Tensor & self, c10::SymIntArrayRef size, at::Device device, const at::Tensor & out) {
    return at::_ops::_resize_output_out::call(self, size, device, out);
  }
}

// aten::_resize_output(Tensor self, SymInt[] size, Device device) -> Tensor
inline at::Tensor _resize_output(const at::Tensor & self, at::IntArrayRef size, at::Device device) {
    return at::_ops::_resize_output::call(self, c10::fromIntArrayRefSlow(size), device);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, int64_t>::value>>
  at::Tensor _resize_output(const at::Tensor & self, at::IntArrayRef size, at::Device device) {
    return at::_ops::_resize_output::call(self, c10::fromIntArrayRefSlow(size), device);
  }
}

// aten::_resize_output(Tensor self, SymInt[] size, Device device) -> Tensor
inline at::Tensor _resize_output_symint(const at::Tensor & self, c10::SymIntArrayRef size, at::Device device) {
    return at::_ops::_resize_output::call(self, size, device);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, c10::SymInt>::value>>
  at::Tensor _resize_output(const at::Tensor & self, c10::SymIntArrayRef size, at::Device device) {
    return at::_ops::_resize_output::call(self, size, device);
  }
}

}
