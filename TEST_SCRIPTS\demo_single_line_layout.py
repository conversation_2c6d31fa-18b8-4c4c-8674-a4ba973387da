#!/usr/bin/env python3
"""
Demo Single Line Layout - Tất cả thông tin trên 1 dòng
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hardware_detector import hardware_detector

def demo_single_line_layout():
    print("=== DEMO SINGLE LINE LAYOUT ===\n")
    
    print("🎨 LAYOUT MỚI - TẤT CẢ TRÊN 1 DÒNG:")
    print("   Trước: 2 dòng riêng biệt")
    print("   Sau:  1 dòng gộp chung")
    print()
    
    print("📏 CẤU TRÚC LAYOUT:")
    print("   [Kết quả] [Stretch] ⏱ Thời gian | ✅ Model | 🎮 Hardware")
    print()
    
    print("🔤 FONT SIZES:")
    print("   - Kết quả: Normal size (bold, màu)")
    print("   - Thời gian: 11px")
    print("   - Model Status: 10px")
    print("   - Hardware Status: 10px (bold)")
    print("   - Separators: 10px (gray)")
    print()
    
    print("📱 VÍ DỤ HIỂN THỊ:")
    
    # L<PERSON>y thông tin thực tế
    try:
        device_display_name = hardware_detector.get_device_display_name()
        
        examples = [
            {
                'scenario': 'Đang chạy bình thường',
                'result': '✅ Thành công: 15/20',
                'time': '⏱ Thời gian: 00:05:23',
                'model': '✅ Model sẵn sàng',
                'hardware': device_display_name
            },
            {
                'scenario': 'Đang tải model',
                'result': '⏳ Đang xử lý: 8/20',
                'time': '⏱ Thời gian: 00:02:15',
                'model': 'Tải model... 67%',
                'hardware': device_display_name
            },
            {
                'scenario': 'Lỗi model',
                'result': '❌ Lỗi: 3/20',
                'time': '⏱ Thời gian: 00:01:45',
                'model': '❌ Lỗi model',
                'hardware': device_display_name
            },
            {
                'scenario': 'Khởi động ứng dụng',
                'result': '',
                'time': '⏱ Thời gian: 00:00:00',
                'model': 'Tải model...',
                'hardware': '🔍 Đang detect...'
            }
        ]
        
        for i, example in enumerate(examples, 1):
            print(f"{i}. {example['scenario']}:")
            layout_line = f"   {example['result']}"
            if example['result']:
                layout_line += " [Stretch] "
            layout_line += f"{example['time']} | {example['model']} | {example['hardware']}"
            print(layout_line)
            print()
    
    except Exception as e:
        print(f"   Lỗi lấy thông tin phần cứng: {e}")
        print()

def demo_space_saving():
    print("=== DEMO TIẾT KIỆM KHÔNG GIAN ===\n")
    
    print("📊 SO SÁNH TRƯỚC VÀ SAU:")
    print()
    
    print("🔴 TRƯỚC (2 dòng):")
    print("   ┌─────────────────────────────────────────────────────────────┐")
    print("   │ [Kết quả]           ⏱ Thời gian: 00:05:23                  │")
    print("   │ ✅ Model sẵn sàng              🎮 GeForce GTX 1080 Ti      │")
    print("   └─────────────────────────────────────────────────────────────┘")
    print("   Chiều cao: ~60px")
    print()
    
    print("🟢 SAU (1 dòng):")
    print("   ┌─────────────────────────────────────────────────────────────┐")
    print("   │ [Kết quả]  ⏱ Thời gian: 00:05:23 | ✅ Model | 🎮 GTX 1080 Ti │")
    print("   └─────────────────────────────────────────────────────────────┘")
    print("   Chiều cao: ~30px")
    print()
    
    print("💾 TIẾT KIỆM:")
    print("   - Giảm 50% chiều cao khung trạng thái")
    print("   - Tăng không gian cho nội dung chính")
    print("   - Giao diện gọn gàng hơn")
    print("   - Dễ đọc thông tin cùng lúc")
    print()

def demo_responsive_behavior():
    print("=== DEMO RESPONSIVE BEHAVIOR ===\n")
    
    print("🔄 CÁC TRẠNG THÁI KHÁC NHAU:")
    
    states = [
        {
            'name': 'Khởi động',
            'model': 'Tải model...',
            'hardware': '🔍 Đang detect...',
            'color': 'orange/blue'
        },
        {
            'name': 'Đang tải',
            'model': 'Tải model... 45%',
            'hardware': '🔍 Đang detect...',
            'color': 'orange/blue'
        },
        {
            'name': 'Sẵn sàng',
            'model': '✅ Model sẵn sàng',
            'hardware': '🎮 GeForce GTX 1080 Ti',
            'color': 'green/green'
        },
        {
            'name': 'Lỗi model',
            'model': '❌ Lỗi model',
            'hardware': '🎮 GeForce GTX 1080 Ti',
            'color': 'red/green'
        },
        {
            'name': 'Lỗi phần cứng',
            'model': '✅ Model sẵn sàng',
            'hardware': '❌ Lỗi phần cứng',
            'color': 'green/red'
        }
    ]
    
    for state in states:
        print(f"   {state['name']}:")
        print(f"     Model: {state['model']} ({state['color'].split('/')[0]})")
        print(f"     Hardware: {state['hardware']} ({state['color'].split('/')[1]})")
        print()

def main():
    print("🚀 DEMO SINGLE LINE LAYOUT - VERSION 5.0.12")
    print("=" * 70)
    print()
    
    demo_single_line_layout()
    print("=" * 70)
    print()
    
    demo_space_saving()
    print("=" * 70)
    print()
    
    demo_responsive_behavior()
    print("=" * 70)
    print()
    
    print("✅ TỔNG KẾT:")
    print("   ✓ Gộp tất cả thông tin trạng thái vào 1 dòng")
    print("   ✓ Tiết kiệm 50% không gian chiều cao")
    print("   ✓ Font size tối ưu cho từng thông tin")
    print("   ✓ Separators rõ ràng giữa các phần")
    print("   ✓ Responsive với các trạng thái khác nhau")
    print("   ✓ Click hardware để xem chi tiết")
    print()
    print("🎯 Layout mới giúp giao diện gọn gàng và hiệu quả hơn!")

if __name__ == "__main__":
    main()
