@echo off
echo Copying Qt6 DLLs to executable directory
echo ========================================

REM Set paths relative to C++ version folder
set EXE_DIR=%~dp0build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\bin
set QT_BIN=D:\PROGRAMS_D\Qt\6.9.1\mingw_64\bin
set MINGW_BIN=D:\PROGRAMS_D\Qt\Tools\mingw1120_64\bin

echo Target directory: %EXE_DIR%
echo Qt6 directory: %QT_BIN%
echo MinGW directory: %MINGW_BIN%
echo.

REM Create target directory if it doesn't exist
if not exist "%EXE_DIR%" (
    echo Creating target directory...
    mkdir "%EXE_DIR%"
)

echo Copying Qt6 Core DLLs...
copy "%QT_BIN%\Qt6Core.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ Qt6Core.dll || echo ❌ Qt6Core.dll
copy "%QT_BIN%\Qt6Gui.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ Qt6Gui.dll || echo ❌ Qt6Gui.dll
copy "%QT_BIN%\Qt6Widgets.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ Qt6Widgets.dll || echo ❌ Qt6Widgets.dll
copy "%QT_BIN%\Qt6Network.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ Qt6Network.dll || echo ❌ Qt6Network.dll

echo.
echo Copying additional Qt6 DLLs...
copy "%QT_BIN%\Qt6Concurrent.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ Qt6Concurrent.dll || echo ⚠️ Qt6Concurrent.dll (optional)
copy "%QT_BIN%\Qt6OpenGL.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ Qt6OpenGL.dll || echo ⚠️ Qt6OpenGL.dll (optional)

echo.
echo Copying MinGW runtime DLLs...
copy "%MINGW_BIN%\libgcc_s_seh-1.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ libgcc_s_seh-1.dll || echo ❌ libgcc_s_seh-1.dll
copy "%MINGW_BIN%\libstdc++-6.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ libstdc++-6.dll || echo ❌ libstdc++-6.dll
copy "%MINGW_BIN%\libwinpthread-1.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ libwinpthread-1.dll || echo ❌ libwinpthread-1.dll

echo.
echo Copying additional MinGW DLLs...
copy "%MINGW_BIN%\libgomp-1.dll" "%EXE_DIR%\" >nul 2>&1 && echo ✅ libgomp-1.dll || echo ⚠️ libgomp-1.dll (optional)

echo.
echo Copying Qt6 platform plugins...
if not exist "%EXE_DIR%\platforms" mkdir "%EXE_DIR%\platforms"
copy "%QT_BIN%\..\plugins\platforms\qwindows.dll" "%EXE_DIR%\platforms\" >nul 2>&1 && echo ✅ qwindows.dll || echo ❌ qwindows.dll

echo.
echo Copying Qt6 styles...
if not exist "%EXE_DIR%\styles" mkdir "%EXE_DIR%\styles"
copy "%QT_BIN%\..\plugins\styles\qwindowsvistastyle.dll" "%EXE_DIR%\styles\" >nul 2>&1 && echo ✅ qwindowsvistastyle.dll || echo ⚠️ qwindowsvistastyle.dll (optional)

echo.
echo ✅ DLL copying completed!
echo.
echo Running application...
"%EXE_DIR%\kiem_tra_mst_qt.exe"

pause
