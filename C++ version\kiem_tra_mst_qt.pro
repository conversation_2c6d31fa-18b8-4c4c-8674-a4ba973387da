QT += core widgets network

CONFIG += c++17

TARGET = kiem_tra_mst_qt
TEMPLATE = app

# Source files
SOURCES += kiem_tra_mst_qt.cpp

# Headers
HEADERS += kiem_tra_mst_qt.h

# Resources (if using .qrc files)
# RESOURCES +=

# Icon (uncomment to add icon)
win32:RC_ICONS = default.ico

# Output directory
DESTDIR = bin

# Temporary directories
OBJECTS_DIR = build/obj
MOC_DIR = build/moc
RCC_DIR = build/rcc
UI_DIR = build/ui

# Version info
VERSION = 5.0.0

# Windows specific
win32 {
    CONFIG += windows
    QMAKE_TARGET_DESCRIPTION = "Tra cuu MST - C++ Version"
    QMAKE_TARGET_COPYRIGHT = "TNT"
}

# macOS specific
macx {
    QMAKE_INFO_PLIST = Info.plist
}

# Install rules
target.path = $$PWD/bin
INSTALLS += target

