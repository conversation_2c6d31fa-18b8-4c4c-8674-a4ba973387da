cmake_minimum_required(VERSION 3.16)

project(kiem_tra_mst_qt VERSION 5.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network)

# Define source files
set(SOURCES
    kiem_tra_mst_qt.cpp
)

set(HEADERS
    kiem_tra_mst_qt.h
)

# Create executable
add_executable(kiem_tra_mst_qt ${SOURCES} ${HEADERS})

# Enable Qt MOC for this target
set_target_properties(kiem_tra_mst_qt PROPERTIES
    AUTOMOC ON
    AUTORCC ON
    AUTOUIC ON
)

# Link Qt6 libraries
target_link_libraries(kiem_tra_mst_qt
    Qt6::Core
    Qt6::Widgets
    Qt6::Network
)

# Set output directory
set_target_properties(kiem_tra_mst_qt PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy resources to build directory (if needed)
file(COPY ${CMAKE_SOURCE_DIR}/resource DESTINATION ${CMAKE_BINARY_DIR}/bin)
file(COPY ${CMAKE_SOURCE_DIR}/CaptchaData DESTINATION ${CMAKE_BINARY_DIR}/bin)
file(COPY ${CMAKE_SOURCE_DIR}/default.ico DESTINATION ${CMAKE_BINARY_DIR}/bin)

# Windows specific settings
if(WIN32)
    set_target_properties(kiem_tra_mst_qt PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
endif()
