#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/sigmoid_ops.h>

namespace at {


// aten::sigmoid(Tensor self) -> Tensor
inline at::Tensor sigmoid(const at::Tensor & self) {
    return at::_ops::sigmoid::call(self);
}

// aten::sigmoid_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & sigmoid_(at::Tensor & self) {
    return at::_ops::sigmoid_::call(self);
}

// aten::sigmoid.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & sigmoid_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::sigmoid_out::call(self, out);
}
// aten::sigmoid.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & sigmoid_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::sigmoid_out::call(self, out);
}

}
