../../Scripts/datasets-cli.exe,sha256=G1agMpsJ3-3SdBH9xLrDlpsMgpb36lmD-Nf_AK6q6I4,106385
datasets-3.1.0.dist-info/AUTHORS,sha256=L0FBY23tCNHLmvsOKAbumHn8WZZIK98sH53JYxhAchU,327
datasets-3.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
datasets-3.1.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
datasets-3.1.0.dist-info/METADATA,sha256=-ydJvL_IH24w0BN2MtZDa9RWsm3uJZgezkAkTKM0WDk,20332
datasets-3.1.0.dist-info/RECORD,,
datasets-3.1.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets-3.1.0.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
datasets-3.1.0.dist-info/entry_points.txt,sha256=iM-h4A7OQCrZqr3L2mwiyMtPeFj8w4HAHzmI45y3tg0,69
datasets-3.1.0.dist-info/top_level.txt,sha256=9A857YvCQm_Dg3UjeKkWPz9sDBos0t3zN2pf5krTemQ,9
datasets/__init__.py,sha256=awD7b8qCC1-UoaFG6e_tb8-8TwkjmwGvDukNc7qSdlY,1606
datasets/__pycache__/__init__.cpython-38.pyc,,
datasets/__pycache__/arrow_dataset.cpython-38.pyc,,
datasets/__pycache__/arrow_reader.cpython-38.pyc,,
datasets/__pycache__/arrow_writer.cpython-38.pyc,,
datasets/__pycache__/builder.cpython-38.pyc,,
datasets/__pycache__/combine.cpython-38.pyc,,
datasets/__pycache__/config.cpython-38.pyc,,
datasets/__pycache__/data_files.cpython-38.pyc,,
datasets/__pycache__/dataset_dict.cpython-38.pyc,,
datasets/__pycache__/distributed.cpython-38.pyc,,
datasets/__pycache__/exceptions.cpython-38.pyc,,
datasets/__pycache__/fingerprint.cpython-38.pyc,,
datasets/__pycache__/hub.cpython-38.pyc,,
datasets/__pycache__/info.cpython-38.pyc,,
datasets/__pycache__/inspect.cpython-38.pyc,,
datasets/__pycache__/iterable_dataset.cpython-38.pyc,,
datasets/__pycache__/keyhash.cpython-38.pyc,,
datasets/__pycache__/load.cpython-38.pyc,,
datasets/__pycache__/naming.cpython-38.pyc,,
datasets/__pycache__/search.cpython-38.pyc,,
datasets/__pycache__/splits.cpython-38.pyc,,
datasets/__pycache__/streaming.cpython-38.pyc,,
datasets/__pycache__/table.cpython-38.pyc,,
datasets/arrow_dataset.py,sha256=8iV8w-BL0-cS3uwopSvBg5FglwdUVzVKgFl1vqrN5q4,291245
datasets/arrow_reader.py,sha256=9O_phThVngfWziLXIqa9HI1xP344mVeEVmbV0E-KQzI,25131
datasets/arrow_writer.py,sha256=OTajobA09q67YeavMOannBk1oEaVal4kuabV_yujW5E,29433
datasets/builder.py,sha256=b_1pnJDa2Z1DnGfbp5QdHI-hOyPchBx__hde-igPVmk,92351
datasets/combine.py,sha256=OvMg-5A_cBraHyEXbNTTrWjd9sbUiyA7PG6aBJpbg5Q,10924
datasets/commands/__init__.py,sha256=rujbQtxJbwHhF9WQqp2DD9tfVTghDMJdl0v6H551Pcs,312
datasets/commands/__pycache__/__init__.cpython-38.pyc,,
datasets/commands/__pycache__/convert.cpython-38.pyc,,
datasets/commands/__pycache__/convert_to_parquet.cpython-38.pyc,,
datasets/commands/__pycache__/datasets_cli.cpython-38.pyc,,
datasets/commands/__pycache__/delete_from_hub.cpython-38.pyc,,
datasets/commands/__pycache__/env.cpython-38.pyc,,
datasets/commands/__pycache__/test.cpython-38.pyc,,
datasets/commands/convert.py,sha256=nsEjpuHLonMjAPB-51mcS0DzHaWwX8V7LHm_MYbpwtY,7880
datasets/commands/convert_to_parquet.py,sha256=cCCug82MPSUiA_TUlJLFUhqGdaKNOL2NVpKQNtTvaCQ,1593
datasets/commands/datasets_cli.py,sha256=KHQa0rn3w4DQ_mM-1BaYvsrG6todZFyoBxM3nWjUBZA,1422
datasets/commands/delete_from_hub.py,sha256=o0wdolb1r1Jnl6F0KdqKn3u0l8VR2od6KzbRoqrSNPM,1396
datasets/commands/env.py,sha256=8qg-hpXSXXsHvtYFvJkn5rn9IncqPsjjx3nR8no4a2I,1239
datasets/commands/test.py,sha256=yi4zAexiP7fu1lmB5b0N6q11ABzkkwXKhH2HCk7U-Yg,9088
datasets/config.py,sha256=3yuwr2hyutbCzq_H1gmP08fq-9LmrJpWQVgDK45RPec,10143
datasets/data_files.py,sha256=UcIKAbve3bht_J2bCJ_j6Wg4huDOi9K0mvlrmviC7MA,32464
datasets/dataset_dict.py,sha256=-l70m9R3cxYaVlM-uxEj6cuKzeuCA9aeAlrM_iFYaK4,105468
datasets/distributed.py,sha256=pulXFluRCmo69KeDqblPz32avS6LCHTGycS77XgI2mY,1562
datasets/download/__init__.py,sha256=lbFOtITDaR7PHrhzJ8VfRnpaOT6NYozSxUcLv_GVfTg,281
datasets/download/__pycache__/__init__.cpython-38.pyc,,
datasets/download/__pycache__/download_config.cpython-38.pyc,,
datasets/download/__pycache__/download_manager.cpython-38.pyc,,
datasets/download/__pycache__/streaming_download_manager.cpython-38.pyc,,
datasets/download/download_config.py,sha256=9bZdajkKMZsiKqYvvE-qCfWT9d3lZj824-ZgQScKAbg,3801
datasets/download/download_manager.py,sha256=7_6N145ctuUbl15IIg2A02X8oZSWXChRRGAE_n2ipOU,12774
datasets/download/streaming_download_manager.py,sha256=fxIpGNU8IN4_R3RFvT0S0ht5bi4Fy0sNosBU-dhadPM,7523
datasets/exceptions.py,sha256=S9yi0fEFsSc3V694NLxNX3QQq1H70mFWPF0OgMepB2g,4197
datasets/features/__init__.py,sha256=8ET2tWiub5dawMT2ysyIsg-o4_UlPpiEJpAPJddCKoA,497
datasets/features/__pycache__/__init__.cpython-38.pyc,,
datasets/features/__pycache__/audio.cpython-38.pyc,,
datasets/features/__pycache__/features.cpython-38.pyc,,
datasets/features/__pycache__/image.cpython-38.pyc,,
datasets/features/__pycache__/translation.cpython-38.pyc,,
datasets/features/__pycache__/video.cpython-38.pyc,,
datasets/features/audio.py,sha256=8_xpCxr5jyCM9zemFWTZK6mNfXv6VeF_3stNdQx0JFA,12225
datasets/features/features.py,sha256=f_RuLEuyISbX8ZI2Gv3emaBH4QEBbhKN9OrDFdCqMsc,92535
datasets/features/image.py,sha256=JoBseOcKuoa4d04xu-sQylvGWVURhZfJPml4pSTHDnQ,15526
datasets/features/translation.py,sha256=6m-swyy1WdWW4EfHVv447jE-Gws6bzn7CPD8EBw31K8,4458
datasets/features/video.py,sha256=ce-qHXlFjuBZ7dL33aoxcw_fpTZWxal1Y-SKy11Ns80,11694
datasets/filesystems/__init__.py,sha256=C2PeP1kpXspMjksUl8BJqJF5Mwk1Bj5IZaUnjh-5imk,1523
datasets/filesystems/__pycache__/__init__.cpython-38.pyc,,
datasets/filesystems/__pycache__/compression.cpython-38.pyc,,
datasets/filesystems/compression.py,sha256=2NnuTGzqmH5wk_Vmp9nhuQCAAZ6bzBpCErvrHVOLR4c,4488
datasets/fingerprint.py,sha256=K3Dbu3etl0ProYrSUG8o3k1Q0uD5cCfu_c_0Upsud7A,20350
datasets/formatting/__init__.py,sha256=K_egpF_SWGHL9H0hlob4lO6g214Sjh0fMG-7_V8w7eE,5391
datasets/formatting/__pycache__/__init__.cpython-38.pyc,,
datasets/formatting/__pycache__/formatting.cpython-38.pyc,,
datasets/formatting/__pycache__/jax_formatter.cpython-38.pyc,,
datasets/formatting/__pycache__/np_formatter.cpython-38.pyc,,
datasets/formatting/__pycache__/polars_formatter.cpython-38.pyc,,
datasets/formatting/__pycache__/tf_formatter.cpython-38.pyc,,
datasets/formatting/__pycache__/torch_formatter.cpython-38.pyc,,
datasets/formatting/formatting.py,sha256=jpUl_UlCT_AAFwhOnjoQYOr_3n4MpIRXL6C6CbMQCgY,26098
datasets/formatting/jax_formatter.py,sha256=WTCOR1QELNIG8QwAvoyZaL6zmwfbJ5Ki-N_V-iVJKYc,7385
datasets/formatting/np_formatter.py,sha256=jhq9C7BiiONDEQYIPQU-akmSUz-tkru1Gv3XhryULo0,5047
datasets/formatting/polars_formatter.py,sha256=PoOZM4RLFvAJdRZyNG5w3aOps3W3Saq1F8Mfyapgv8I,4700
datasets/formatting/tf_formatter.py,sha256=Hx4wH6LQtxDNY4oV70vUWJJktQ4nY8qEObTZT33ySnA,5234
datasets/formatting/torch_formatter.py,sha256=XJEJtNtHONHaa2Ai6oww4JW3dRWHz1jIWfmRGMEVo8I,5007
datasets/hub.py,sha256=TFBvGkTXceEmaz0FDlP-mDmQLYLLCZ9T6GT0J8Nn3Gw,9380
datasets/info.py,sha256=sO4isuhaBObGl59UtU2-bWhU17oE1gHIASaSbEQzctc,19675
datasets/inspect.py,sha256=JrC9cP4Th6Fy4yYAuZFXM40OUV8puA629UQAIVt5534,16978
datasets/io/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/io/__pycache__/__init__.cpython-38.pyc,,
datasets/io/__pycache__/abc.cpython-38.pyc,,
datasets/io/__pycache__/csv.cpython-38.pyc,,
datasets/io/__pycache__/generator.cpython-38.pyc,,
datasets/io/__pycache__/json.cpython-38.pyc,,
datasets/io/__pycache__/parquet.cpython-38.pyc,,
datasets/io/__pycache__/spark.cpython-38.pyc,,
datasets/io/__pycache__/sql.cpython-38.pyc,,
datasets/io/__pycache__/text.cpython-38.pyc,,
datasets/io/abc.py,sha256=LwDMXYs6YkhZuz1JiMK4PDIqgNjv7I8xH3UMUELW2ys,1672
datasets/io/csv.py,sha256=v4zaWehHb9U3njbdhy7wQnb8qO_c_58XOUC9JgBBVwI,5265
datasets/io/generator.py,sha256=sP_5GNozcxXIgDsPVMW_riqCZdInZ0_iFzcY_X1F-Mo,1909
datasets/io/json.py,sha256=QE_ifGHVxPTwsgDUElc0DpD55_lO-tu37bE984etXP8,6408
datasets/io/parquet.py,sha256=IxotIfpNHXvJgFzsbT3-CjB1_FfvKpYhNNU1Akxe9bs,4354
datasets/io/spark.py,sha256=VUIODLHgIbiK0CI0UvthQ_gUO0MQDtHUozvw7Dfs8FI,1797
datasets/io/sql.py,sha256=4Zjw7peVEhhzoDtz2VTCFPqt2Tpy4zMB7T7ajb2GVTY,4234
datasets/io/text.py,sha256=bebEzXBSGC40_Gy94j9ZTJ7Hg0IfrV_4pnIUEhQZVig,1975
datasets/iterable_dataset.py,sha256=VWYObnydZ-DmlbNfQfmsmvcr23ixnEFz7z8liSZD7BA,145206
datasets/keyhash.py,sha256=gZLJ-0lIaj5mXP3fm0zFz8oY9L3Qu_OMkgil06oq0eg,3872
datasets/load.py,sha256=J0_f_yjmM9fA1530DbtdTil3cJIa81wmPHgAU2I78BM,104254
datasets/naming.py,sha256=aqQqYG4QR8YoxJJMAUyVv_oQyudm4WAApsEHvcozpNg,3001
datasets/packaged_modules/__init__.py,sha256=_yxecSzYMq0waZLbGR0-NZ2NvbeUeIIbOoVpGPSPWK0,4395
datasets/packaged_modules/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/arrow/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/arrow/__pycache__/arrow.cpython-38.pyc,,
datasets/packaged_modules/arrow/arrow.py,sha256=GIEF10DPgfj2JE3UEi7REgU7Y6f7KiNgPWp4EnAUk5Q,3472
datasets/packaged_modules/audiofolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/audiofolder/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/audiofolder/__pycache__/audiofolder.cpython-38.pyc,,
datasets/packaged_modules/audiofolder/audiofolder.py,sha256=R437ibRq7-G1Zzs5Ji8Y9YNRwe1gtPqivmQm0HzAR38,1493
datasets/packaged_modules/cache/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/cache/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/cache/__pycache__/cache.cpython-38.pyc,,
datasets/packaged_modules/cache/cache.py,sha256=UJT0U0costw4VAUk0fcAG63WQBv-GduX7Qw5VQyobN4,8209
datasets/packaged_modules/csv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/csv/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/csv/__pycache__/csv.cpython-38.pyc,,
datasets/packaged_modules/csv/csv.py,sha256=82m39udsJK92n87jx1_vJYZ4HGKIm6JOKgQmowfkj1w,8580
datasets/packaged_modules/folder_based_builder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/folder_based_builder/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/folder_based_builder/__pycache__/folder_based_builder.cpython-38.pyc,,
datasets/packaged_modules/folder_based_builder/folder_based_builder.py,sha256=S75So3rXNvRrD6VL98xg1DWs5cPEKPlAMWV90YKnFqA,22273
datasets/packaged_modules/generator/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/generator/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/generator/__pycache__/generator.cpython-38.pyc,,
datasets/packaged_modules/generator/generator.py,sha256=atst9Zm2PJQV1ap8yabiGBijcc3q4ECZkS0I0cLskr8,1033
datasets/packaged_modules/imagefolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/imagefolder/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/imagefolder/__pycache__/imagefolder.cpython-38.pyc,,
datasets/packaged_modules/imagefolder/imagefolder.py,sha256=Tv6s3uOU9M6-vZLwuTuWuxiWaI3vy3SbjUjXx8-QFx0,1900
datasets/packaged_modules/json/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/json/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/json/__pycache__/json.cpython-38.pyc,,
datasets/packaged_modules/json/json.py,sha256=ipf8GieLlsGt5x1rJKr4ViJWg9oTHNp85OKYyPSW2R0,8698
datasets/packaged_modules/pandas/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/pandas/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/pandas/__pycache__/pandas.cpython-38.pyc,,
datasets/packaged_modules/pandas/pandas.py,sha256=eR0B5iGOHZ1owzezYmlvx5U_rWblmlpCt_PdC5Ax59E,2547
datasets/packaged_modules/parquet/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/parquet/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/parquet/__pycache__/parquet.cpython-38.pyc,,
datasets/packaged_modules/parquet/parquet.py,sha256=m-M-YrS6uzdOxvbBpT8vw8lVs65FJuNiXgb5bN-uy08,4541
datasets/packaged_modules/spark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/spark/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/spark/__pycache__/spark.cpython-38.pyc,,
datasets/packaged_modules/spark/spark.py,sha256=8hTQwNbT8SOJyMTtmjtlO4X3v9O2BUsBn8bcsLwN-UI,14675
datasets/packaged_modules/sql/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/sql/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/sql/__pycache__/sql.cpython-38.pyc,,
datasets/packaged_modules/sql/sql.py,sha256=CihTzJh3Z95a0WbEoCT159aUkGh-KsNhv62v5LctLXk,4514
datasets/packaged_modules/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/text/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/text/__pycache__/text.cpython-38.pyc,,
datasets/packaged_modules/text/text.py,sha256=VOJVHkmy4Vm53nspW7QboCkPxd1S0M0uEzun5v8rzUE,5516
datasets/packaged_modules/videofolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/videofolder/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/videofolder/__pycache__/videofolder.cpython-38.pyc,,
datasets/packaged_modules/videofolder/videofolder.py,sha256=Rw89Bb9q8CbYhlKu7kRECUL7g1iehU9V0Ha6q2VmQ6I,832
datasets/packaged_modules/webdataset/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/webdataset/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/webdataset/__pycache__/_tenbin.cpython-38.pyc,,
datasets/packaged_modules/webdataset/__pycache__/webdataset.cpython-38.pyc,,
datasets/packaged_modules/webdataset/_tenbin.py,sha256=oovYsgR2R3eXSn1xSCLG3oTly1szKDP4UOiRp4ORdIk,8533
datasets/packaged_modules/webdataset/webdataset.py,sha256=L6COBXiQTmbubLuA4snxYQlZQJlq69Na56t3X3Gypww,10403
datasets/packaged_modules/xml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/xml/__pycache__/__init__.cpython-38.pyc,,
datasets/packaged_modules/xml/__pycache__/xml.cpython-38.pyc,,
datasets/packaged_modules/xml/xml.py,sha256=av0HcLQnKl5d1yM0jfBqVhw9EbzqmO_RsHDfa5pkvx4,2822
datasets/parallel/__init__.py,sha256=wiRFK4x67ez2vvmjwM2Sb9R1yFdf38laSarU9y0Bido,76
datasets/parallel/__pycache__/__init__.cpython-38.pyc,,
datasets/parallel/__pycache__/parallel.cpython-38.pyc,,
datasets/parallel/parallel.py,sha256=E-oOQ6zwKrkLFPwZ-3EOcr_aANJDhE-d6QTq7Mp7WvA,4738
datasets/search.py,sha256=oUh55M77KOxHU-V4ZFVosHCj3IaAOWJJ8bmkGD1aXxw,35606
datasets/splits.py,sha256=AY9zyECJ_C9W8ctGpqicrrIT1ZzKuea4BBs6hS1Nk_w,23445
datasets/streaming.py,sha256=lOn__P1Tp2Z8jEbjwrdRPs-abNhUqOTY18fXaXQyPtA,6534
datasets/table.py,sha256=NwD07aYXjOm65ljrm9XWTrLTtAfUj8xSfLRtRP_DtXw,95865
datasets/utils/__init__.py,sha256=PuZtB9YTbRyvdwubnsx-JGdHuMA7p0I0Rmh0E_uxYF0,999
datasets/utils/__pycache__/__init__.cpython-38.pyc,,
datasets/utils/__pycache__/_dataset_viewer.cpython-38.pyc,,
datasets/utils/__pycache__/_dill.cpython-38.pyc,,
datasets/utils/__pycache__/_filelock.cpython-38.pyc,,
datasets/utils/__pycache__/deprecation_utils.cpython-38.pyc,,
datasets/utils/__pycache__/doc_utils.cpython-38.pyc,,
datasets/utils/__pycache__/experimental.cpython-38.pyc,,
datasets/utils/__pycache__/extract.cpython-38.pyc,,
datasets/utils/__pycache__/file_utils.cpython-38.pyc,,
datasets/utils/__pycache__/filelock.cpython-38.pyc,,
datasets/utils/__pycache__/hub.cpython-38.pyc,,
datasets/utils/__pycache__/info_utils.cpython-38.pyc,,
datasets/utils/__pycache__/logging.cpython-38.pyc,,
datasets/utils/__pycache__/metadata.cpython-38.pyc,,
datasets/utils/__pycache__/patching.cpython-38.pyc,,
datasets/utils/__pycache__/py_utils.cpython-38.pyc,,
datasets/utils/__pycache__/sharding.cpython-38.pyc,,
datasets/utils/__pycache__/stratify.cpython-38.pyc,,
datasets/utils/__pycache__/tf_utils.cpython-38.pyc,,
datasets/utils/__pycache__/tqdm.cpython-38.pyc,,
datasets/utils/__pycache__/track.cpython-38.pyc,,
datasets/utils/__pycache__/typing.cpython-38.pyc,,
datasets/utils/__pycache__/version.cpython-38.pyc,,
datasets/utils/_dataset_viewer.py,sha256=5wG9YBdDROORE50T_yg8-NfFp1G3RjWgaX9ScxC4_Y4,4409
datasets/utils/_dill.py,sha256=0QphnYT5cKHJEn17Cs_i1XFYazIfJZUr5mm8ehee_bw,17136
datasets/utils/_filelock.py,sha256=yl4ZQupEUyPu7f8D2ZCXitIMlajDu322QcO7Fio8eQI,2370
datasets/utils/deprecation_utils.py,sha256=hTHwlzRs92NfNVudH71LMpW70sjbsP5amebrIgi3A-U,3452
datasets/utils/doc_utils.py,sha256=HoSm0TFaQaCYGfDgNhpBJ4Xc2WQZuOD6dTxLd9D87fs,407
datasets/utils/experimental.py,sha256=JgOjaEY3RWZ--3u0-ry82gLCDUpudfBfl-hWZ46SyS4,1097
datasets/utils/extract.py,sha256=QAqRbfbB_yUG2RZLyWeTZviSb6Qr8zEFH05YM6XlW9w,13039
datasets/utils/file_utils.py,sha256=02pG_Lnl2KsjX7B9a-KBo66T8Ogj1AGbvSQIsAPgBSI,54289
datasets/utils/filelock.py,sha256=H6C5dQGFCzVKyeDRRY8fZ4YGTEvvNd-MTjpL_sWYb5k,352
datasets/utils/hub.py,sha256=sD9VpJENA3M9_rWFGavUaVV_GsrOBLEKCZjcqtRdJ_s,438
datasets/utils/info_utils.py,sha256=gAzubjnQbE0YTzB3hf3Cipmx5wCBtOje3fPwjYdzVBE,4330
datasets/utils/logging.py,sha256=a9kgqN1Xo6HvsIPbrHY08n7cUukxQqd3vpwTubisL3E,5404
datasets/utils/metadata.py,sha256=lO-Z23e_qXC2we_1bKgpAfrMKjkBJXdml581bCOCfmQ,9386
datasets/utils/patching.py,sha256=iTeb7XG4faLJKNylq55EcZyCndUXU_XBDvOOkuDz_sc,4955
datasets/utils/py_utils.py,sha256=7h91TmcHN4_Utiqx--qOsPEUqgLu6iVnG2isq0u-x-Y,27617
datasets/utils/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/utils/resources/__pycache__/__init__.cpython-38.pyc,,
datasets/utils/resources/creators.json,sha256=XtIpMZefgBOdTevRrQTkFiufbgCbp_iyxseyphYQkn0,257
datasets/utils/resources/languages.json,sha256=Z0rQNPsfje8zMi8KdvvwxF4APwwqcskJFUvhNiLAgPM,199138
datasets/utils/resources/multilingualities.json,sha256=02Uc8RtRzfl13l98Y_alZm5HuMYwPzL78B0S5a1X-8c,205
datasets/utils/resources/readme_structure.yaml,sha256=hNf9msoBZw5jfakQrDb0Af8T325TXdcaHsAO2MUcZvY,3877
datasets/utils/resources/size_categories.json,sha256=_5nAP7z8R6t7_GfER81QudFO6Y1tqYu4AWrr4Aot8S8,171
datasets/utils/sharding.py,sha256=FDi895opKH7XkpfIu-ag9PqBQo2PGx0tSO3Dg-gDAAs,4288
datasets/utils/stratify.py,sha256=uMwuCDRbW342vy-lXDHs6IQusOr7c9nOG3PpnWyzJO4,4091
datasets/utils/tf_utils.py,sha256=vFciShIUekuFS8Q01jDixl29d9II4D5ebTPQfhh3OT8,24456
datasets/utils/tqdm.py,sha256=44F0g2fBpJwShh1l88PP7Z8kBihFWA_Yee4sjiQSxes,4303
datasets/utils/track.py,sha256=0OmbkJwVDlM0_ocs5h30vr5oOmoQ6FTKDuEuw6b8CvE,1856
datasets/utils/typing.py,sha256=LznosIqUzjXgwbRLAGCv4_7-yZo7muYY42Y3495oz5I,224
datasets/utils/version.py,sha256=Z82cHpjTbQVJyWgnwSU8DsW2G0y-sSbSoOVeQrAds9k,3281
