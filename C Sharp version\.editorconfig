root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.{cs,csx,vb,vbx}]
indent_size = 4

[*.{json,xml,yml,yaml}]
indent_size = 2

[*.cs]
# .NET 8 specific settings
dotnet_sort_system_directives_first = true
dotnet_separate_import_directive_groups = false

# C# 12 features
csharp_prefer_simple_using_statement = true
csharp_prefer_static_local_functions = true
csharp_style_prefer_primary_constructors = true

# Code style rules
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
