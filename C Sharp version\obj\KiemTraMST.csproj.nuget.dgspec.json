{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C Sharp version\\KiemTraMST.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C Sharp version\\KiemTraMST.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C Sharp version\\KiemTraMST.csproj", "projectName": "KiemTraMST", "projectPath": "C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C Sharp version\\KiemTraMST.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C Sharp version\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.ML.OnnxRuntime": {"target": "Package", "version": "[1.19.2, )"}, "Microsoft.ML.OnnxRuntime.Gpu": {"target": "Package", "version": "[1.19.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[112.0.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.0, )"}, "System.Management": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}}}