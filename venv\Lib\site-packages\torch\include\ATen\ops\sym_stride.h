#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/sym_stride_ops.h>

namespace at {


// aten::sym_stride.int(Tensor self, int dim) -> SymInt
inline c10::SymInt __dispatch_sym_stride(const at::Tensor & self, int64_t dim) {
    return at::_ops::sym_stride_int::call(self, dim);
}

}
