{"version": 3, "targets": {"net9.0-windows7.0": {"Microsoft.ML.OnnxRuntime/1.19.2": {"type": "package", "dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.19.2"}, "build": {"build/netstandard2.1/Microsoft.ML.OnnxRuntime.props": {}, "build/netstandard2.1/Microsoft.ML.OnnxRuntime.targets": {}}, "runtimeTargets": {"runtimes/android/native/onnxruntime.aar": {"assetType": "native", "rid": "android"}, "runtimes/ios/native/onnxruntime.xcframework.zip": {"assetType": "native", "rid": "ios"}, "runtimes/linux-arm64/native/libonnxruntime.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libonnxruntime_providers_shared.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/libonnxruntime.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libonnxruntime_providers_shared.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libonnxruntime.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libonnxruntime.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm64/native/onnxruntime.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime.lib": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/onnxruntime.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/onnxruntime.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime.lib": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.ML.OnnxRuntime.Gpu/1.19.2": {"type": "package", "dependencies": {"Microsoft.ML.OnnxRuntime.Gpu.Linux": "1.19.2", "Microsoft.ML.OnnxRuntime.Gpu.Windows": "1.19.2", "Microsoft.ML.OnnxRuntime.Managed": "1.19.2"}, "build": {"buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.props": {}, "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.targets": {}}}, "Microsoft.ML.OnnxRuntime.Gpu.Linux/1.19.2": {"type": "package", "dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.19.2"}, "build": {"buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.Linux.props": {}, "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.Linux.targets": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libonnxruntime.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libonnxruntime_providers_cuda.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libonnxruntime_providers_shared.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libonnxruntime_providers_tensorrt.so": {"assetType": "native", "rid": "linux-x64"}}}, "Microsoft.ML.OnnxRuntime.Gpu.Windows/1.19.2": {"type": "package", "dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.19.2"}, "build": {"buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.Windows.props": {}, "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.Windows.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/onnxruntime.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_cuda.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_cuda.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_tensorrt.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_tensorrt.lib": {"assetType": "native", "rid": "win-x64"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.19.2": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net6.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "build": {"build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets": {}}}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "RestSharp/112.0.0": {"type": "package", "compile": {"lib/net8.0/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/RestSharp.dll": {"related": ".xml"}}}, "System.CodeDom/9.0.0": {"type": "package", "compile": {"lib/net9.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Drawing.Common/9.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "9.0.0"}, "compile": {"lib/net9.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net9.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net9.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Management/9.0.0": {"type": "package", "dependencies": {"System.CodeDom": "9.0.0"}, "compile": {"lib/net9.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}}}, "libraries": {"Microsoft.ML.OnnxRuntime/1.19.2": {"sha512": "V1v8BebP1ZEFcZf2hGWuN7S5jAqmE2zBOq9RbGebZi2jd/aTZpg4PiTDtpQoGAIhZg4EPQUsHAyOJxwue44pBA==", "type": "package", "path": "microsoft.ml.onnxruntime/1.19.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ORT_icon_for_light_bg.png", "Privacy.md", "README.md", "ThirdPartyNotices.txt", "build/native/Microsoft.ML.OnnxRuntime.props", "build/native/Microsoft.ML.OnnxRuntime.targets", "build/native/include/cpu_provider_factory.h", "build/native/include/onnxruntime_c_api.h", "build/native/include/onnxruntime_cxx_api.h", "build/native/include/onnxruntime_cxx_inline.h", "build/native/include/onnxruntime_float16.h", "build/native/include/onnxruntime_lite_custom_op.h", "build/native/include/onnxruntime_run_options_config_keys.h", "build/native/include/onnxruntime_session_options_config_keys.h", "build/native/include/provider_options.h", "build/net8.0-android31.0/Microsoft.ML.OnnxRuntime.targets", "build/net8.0-ios15.4/Microsoft.ML.OnnxRuntime.targets", "build/net8.0-maccatalyst14.0/_._", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.props", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.targets", "build/netstandard2.1/Microsoft.ML.OnnxRuntime.props", "build/netstandard2.1/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net8.0-android31.0/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net8.0-ios15.4/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net8.0-maccatalyst14.0/_._", "microsoft.ml.onnxruntime.1.19.2.nupkg.sha512", "microsoft.ml.onnxruntime.nuspec", "runtimes/android/native/onnxruntime.aar", "runtimes/ios/native/onnxruntime.xcframework.zip", "runtimes/linux-arm64/native/libonnxruntime.so", "runtimes/linux-arm64/native/libonnxruntime_providers_shared.so", "runtimes/linux-x64/native/libonnxruntime.so", "runtimes/linux-x64/native/libonnxruntime_providers_shared.so", "runtimes/osx-arm64/native/libonnxruntime.dylib", "runtimes/osx-x64/native/libonnxruntime.dylib", "runtimes/win-arm64/native/onnxruntime.dll", "runtimes/win-arm64/native/onnxruntime.lib", "runtimes/win-arm64/native/onnxruntime_providers_shared.dll", "runtimes/win-arm64/native/onnxruntime_providers_shared.lib", "runtimes/win-x64/native/onnxruntime.dll", "runtimes/win-x64/native/onnxruntime.lib", "runtimes/win-x64/native/onnxruntime_providers_shared.dll", "runtimes/win-x64/native/onnxruntime_providers_shared.lib", "runtimes/win-x86/native/onnxruntime.dll", "runtimes/win-x86/native/onnxruntime.lib", "runtimes/win-x86/native/onnxruntime_providers_shared.dll", "runtimes/win-x86/native/onnxruntime_providers_shared.lib"]}, "Microsoft.ML.OnnxRuntime.Gpu/1.19.2": {"sha512": "lV5B4K/a7mBKi98sKWwxEzQxIfE/9GLMWPYDedqI5h21p1ZEbr4bQZ1RJFA/wuP5H5jTTaBz+gK65Pc+AOVc+A==", "type": "package", "path": "microsoft.ml.onnxruntime.gpu/1.19.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ORT_icon_for_light_bg.png", "Privacy.md", "README.md", "ThirdPartyNotices.txt", "buildTransitive/native/Microsoft.ML.OnnxRuntime.Gpu.props", "buildTransitive/native/Microsoft.ML.OnnxRuntime.Gpu.targets", "buildTransitive/netstandard2.0/Microsoft.ML.OnnxRuntime.Gpu.props", "buildTransitive/netstandard2.0/Microsoft.ML.OnnxRuntime.Gpu.targets", "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.props", "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.targets", "microsoft.ml.onnxruntime.gpu.1.19.2.nupkg.sha512", "microsoft.ml.onnxruntime.gpu.nuspec"]}, "Microsoft.ML.OnnxRuntime.Gpu.Linux/1.19.2": {"sha512": "qlPkDLOepAuB+v9DZ8PvwlinSUhYJRLMDWhqHJXdmH8Ay4Tr4T7E4MgcU1eJpooo3JG3J3xVwCjQrFpS5/f53Q==", "type": "package", "path": "microsoft.ml.onnxruntime.gpu.linux/1.19.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ORT_icon_for_light_bg.png", "Privacy.md", "README.md", "ThirdPartyNotices.txt", "buildTransitive/native/Microsoft.ML.OnnxRuntime.Gpu.Linux.props", "buildTransitive/native/Microsoft.ML.OnnxRuntime.Gpu.Linux.targets", "buildTransitive/native/include/cpu_provider_factory.h", "buildTransitive/native/include/onnxruntime_c_api.h", "buildTransitive/native/include/onnxruntime_cxx_api.h", "buildTransitive/native/include/onnxruntime_cxx_inline.h", "buildTransitive/native/include/onnxruntime_float16.h", "buildTransitive/native/include/onnxruntime_lite_custom_op.h", "buildTransitive/native/include/onnxruntime_run_options_config_keys.h", "buildTransitive/native/include/onnxruntime_session_options_config_keys.h", "buildTransitive/native/include/provider_options.h", "buildTransitive/netstandard2.0/Microsoft.ML.OnnxRuntime.Gpu.Linux.props", "buildTransitive/netstandard2.0/Microsoft.ML.OnnxRuntime.Gpu.Linux.targets", "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.Linux.props", "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.Linux.targets", "microsoft.ml.onnxruntime.gpu.linux.1.19.2.nupkg.sha512", "microsoft.ml.onnxruntime.gpu.linux.nuspec", "runtimes/linux-x64/native/libonnxruntime.so", "runtimes/linux-x64/native/libonnxruntime_providers_cuda.so", "runtimes/linux-x64/native/libonnxruntime_providers_shared.so", "runtimes/linux-x64/native/libonnxruntime_providers_tensorrt.so"]}, "Microsoft.ML.OnnxRuntime.Gpu.Windows/1.19.2": {"sha512": "5rkB5/K1t2/aHFSTLmPrQtvRTDA8Lo6ghtR6W+nK+uwCKLd2LJgbyKGlFmz+0JIy1oqNtUN9eLmXx5/x8TGHjw==", "type": "package", "path": "microsoft.ml.onnxruntime.gpu.windows/1.19.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ORT_icon_for_light_bg.png", "Privacy.md", "README.md", "ThirdPartyNotices.txt", "buildTransitive/native/Microsoft.ML.OnnxRuntime.Gpu.Windows.props", "buildTransitive/native/Microsoft.ML.OnnxRuntime.Gpu.Windows.targets", "buildTransitive/native/include/cpu_provider_factory.h", "buildTransitive/native/include/onnxruntime_c_api.h", "buildTransitive/native/include/onnxruntime_cxx_api.h", "buildTransitive/native/include/onnxruntime_cxx_inline.h", "buildTransitive/native/include/onnxruntime_float16.h", "buildTransitive/native/include/onnxruntime_lite_custom_op.h", "buildTransitive/native/include/onnxruntime_run_options_config_keys.h", "buildTransitive/native/include/onnxruntime_session_options_config_keys.h", "buildTransitive/native/include/provider_options.h", "buildTransitive/netstandard2.0/Microsoft.ML.OnnxRuntime.Gpu.Windows.props", "buildTransitive/netstandard2.0/Microsoft.ML.OnnxRuntime.Gpu.Windows.targets", "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.Windows.props", "buildTransitive/netstandard2.1/Microsoft.ML.OnnxRuntime.Gpu.Windows.targets", "microsoft.ml.onnxruntime.gpu.windows.1.19.2.nupkg.sha512", "microsoft.ml.onnxruntime.gpu.windows.nuspec", "runtimes/win-x64/native/onnxruntime.dll", "runtimes/win-x64/native/onnxruntime.lib", "runtimes/win-x64/native/onnxruntime_providers_cuda.dll", "runtimes/win-x64/native/onnxruntime_providers_cuda.lib", "runtimes/win-x64/native/onnxruntime_providers_shared.dll", "runtimes/win-x64/native/onnxruntime_providers_shared.lib", "runtimes/win-x64/native/onnxruntime_providers_tensorrt.dll", "runtimes/win-x64/native/onnxruntime_providers_tensorrt.lib"]}, "Microsoft.ML.OnnxRuntime.Managed/1.19.2": {"sha512": "4oNu7/L1ar3PmXNvAnary0/MbyqNkKDmisrG54QVbFoSuW1GNw1CcWDBrlYHbafo41p4/cnVQ3lF/IHuSm8quQ==", "type": "package", "path": "microsoft.ml.onnxruntime.managed/1.19.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "ORT_icon_for_light_bg.png", "Privacy.md", "ThirdPartyNotices.txt", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets", "lib/net6.0/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net8.0-android34.0/Microsoft.ML.OnnxRuntime.dll", "lib/net8.0-android34.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net8.0-android34.0/Microsoft.ML.OnnxRuntime.xml", "lib/net8.0-ios17.5/Microsoft.ML.OnnxRuntime.dll", "lib/net8.0-ios17.5/Microsoft.ML.OnnxRuntime.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.ML.OnnxRuntime.dll", "lib/net8.0-maccatalyst17.5/Microsoft.ML.OnnxRuntime.pdb", "lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll", "lib/netstandard2.0/Microsoft.ML.OnnxRuntime.pdb", "microsoft.ml.onnxruntime.managed.1.19.2.nupkg.sha512", "microsoft.ml.onnxruntime.managed.nuspec"]}, "Microsoft.Win32.SystemEvents/9.0.0": {"sha512": "z8FfGIaoeALdD+KF44A2uP8PZIQQtDGiXsOLuN8nohbKhkyKt7zGaZb+fKiCxTuBqG22Q7myIAioSWaIcOOrOw==", "type": "package", "path": "microsoft.win32.systemevents/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/net9.0/Microsoft.Win32.SystemEvents.dll", "lib/net9.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.9.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "RestSharp/112.0.0": {"sha512": "vezxJ6DLk0eDx+rmXwVHQvJ3elZCJfnLd8neRrkA+IvTs9falf2CEAolsjI1YKkkHTOvG13xWTnHK7gU0zgaaw==", "type": "package", "path": "restsharp/112.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net471/RestSharp.dll", "lib/net471/RestSharp.xml", "lib/net48/RestSharp.dll", "lib/net48/RestSharp.xml", "lib/net6.0/RestSharp.dll", "lib/net6.0/RestSharp.xml", "lib/net7.0/RestSharp.dll", "lib/net7.0/RestSharp.xml", "lib/net8.0/RestSharp.dll", "lib/net8.0/RestSharp.xml", "lib/netstandard2.0/RestSharp.dll", "lib/netstandard2.0/RestSharp.xml", "restsharp.112.0.0.nupkg.sha512", "restsharp.nuspec", "restsharp.png"]}, "System.CodeDom/9.0.0": {"sha512": "oTE5IfuMoET8yaZP/vdvy9xO47guAv/rOhe4DODuFBN3ySprcQOlXqO3j+e/H/YpKKR5sglrxRaZ2HYOhNJrqA==", "type": "package", "path": "system.codedom/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/net9.0/System.CodeDom.dll", "lib/net9.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.9.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/9.0.0": {"sha512": "uoozjI3+dlgKh2onFJcz8aNLh6TRCPlLSh8Dbuljc8CdvqXrxHOVysJlrHvlsOCqceqGBR1wrMPxlnzzhynktw==", "type": "package", "path": "system.drawing.common/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/net8.0/System.Private.Windows.Core.dll", "lib/net8.0/System.Private.Windows.Core.xml", "lib/net9.0/System.Drawing.Common.dll", "lib/net9.0/System.Drawing.Common.pdb", "lib/net9.0/System.Drawing.Common.xml", "lib/net9.0/System.Private.Windows.Core.dll", "lib/net9.0/System.Private.Windows.Core.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.9.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/9.0.0": {"sha512": "bVh4xAMI5grY5GZoklKcMBLirhC8Lqzp63Ft3zXJacwGAlLyFdF4k0qz4pnKIlO6HyL2Z4zqmHm9UkzEo6FFsA==", "type": "package", "path": "system.management/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/net9.0/System.Management.dll", "lib/net9.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "runtimes/win/lib/net9.0/System.Management.dll", "runtimes/win/lib/net9.0/System.Management.xml", "system.management.9.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["Microsoft.ML.OnnxRuntime >= 1.19.2", "Microsoft.ML.OnnxRuntime.Gpu >= 1.19.2", "Newtonsoft.Json >= 13.0.3", "RestSharp >= 112.0.0", "System.Drawing.Common >= 9.0.0", "System.Management >= 9.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C Sharp version\\KiemTraMST.csproj", "projectName": "KiemTraMST", "projectPath": "C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C Sharp version\\KiemTraMST.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\kiem tra mst v4\\C Sharp version\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.ML.OnnxRuntime": {"target": "Package", "version": "[1.19.2, )"}, "Microsoft.ML.OnnxRuntime.Gpu": {"target": "Package", "version": "[1.19.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[112.0.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.0, )"}, "System.Management": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}}