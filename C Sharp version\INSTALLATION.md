# Hướng dẫn cài đặt và chạy ứng dụng C#

## Bước 1: Cài đặt .NET SDK

### Tải và cài đặt .NET 9.0 SDK:
1. T<PERSON>y cập: https://dotnet.microsoft.com/download/dotnet/9.0
2. Tải **SDK x64** cho Windows
3. Chạy file installer và làm theo hướng dẫn
4. Khởi động lại Command Prompt/PowerShell

### Kiểm tra cài đặt:
```bash
dotnet --version
```
Kết quả mong đợi: `9.0.x` hoặc cao hơn

## Bước 2: Build và chạy ứng dụng

### Cách 1: Sử dụng script tự động
```bash
# Build ứng dụng
build.bat

# Chạy ứng dụng
run.bat
```

### Cách 2: Sử dụng command line
```bash
# Restore packages
dotnet restore

# Build
dotnet build --configuration Release

# Run
dotnet run --configuration Release
```

### Cách 3: Tạo executable standalone
```bash
# Publish standalone (không cần .NET runtime)
dotnet publish --configuration Release --self-contained true --runtime win-x64 --output "bin\Publish"

# Chạy file .exe
bin\Publish\KiemTraMST.exe
```

## Bước 3: Sử dụng ứng dụng

1. **Khởi động**: Chạy ứng dụng bằng một trong các cách trên
2. **Hardware Detection**: Ứng dụng sẽ tự động detect phần cứng
3. **Nhập MST**: Nhập danh sách MST hoặc load từ file
4. **Kiểm tra**: Click "Bắt đầu" để kiểm tra MST
5. **Xuất kết quả**: Click "Xuất Excel" để lưu kết quả

## Troubleshooting

### Lỗi: ".NET SDK not found"
- Cài đặt .NET 8.0 SDK từ Microsoft
- Khởi động lại terminal sau khi cài đặt

### Lỗi: "Package restore failed"
- Kiểm tra kết nối internet
- Chạy: `dotnet nuget locals all --clear`
- Thử lại: `dotnet restore`

### Lỗi: "Build failed"
- Kiểm tra .NET SDK version: `dotnet --version`
- Đảm bảo có .NET 9.0 hoặc cao hơn
- Xóa folder `bin` và `obj`, build lại

### Lỗi: "Hardware detection failed"
- Chạy ứng dụng với quyền Administrator
- Kiểm tra Windows Management Instrumentation service

## Tính năng chính

### ✅ Đã chuyển đổi từ Python
- [x] Hardware Detection (CPU, NVIDIA GPU, Intel GPU)
- [x] Single Line Status Layout
- [x] MST Checking với Multi-threading
- [x] Configuration Management
- [x] Export to CSV
- [x] Real-time Progress Tracking
- [x] Comprehensive Logging
- [x] Error Handling & Retry Logic

### 🎯 Tương thích hoàn toàn
- Layout giống hệt Python version
- Tất cả tính năng được preserve
- Performance tương đương hoặc tốt hơn
- Native Windows integration

## System Requirements

- **OS**: Windows 10/11 (x64)
- **Runtime**: .NET 9.0 hoặc cao hơn
- **RAM**: Tối thiểu 512MB
- **Disk**: 100MB free space
- **Network**: Internet connection để kiểm tra MST

## File Structure

```
C Sharp version/
├── KiemTraMST.csproj      # Project configuration
├── App.xaml               # Application definition
├── MainWindow.xaml        # Main UI layout
├── HardwareDetector.cs    # Hardware detection logic
├── MstChecker.cs         # MST checking logic
├── ConfigManager.cs      # Configuration management
├── setting.ini           # Configuration file
├── build.bat            # Build script
├── run.bat              # Run script
├── README.md            # Documentation
└── INSTALLATION.md      # This file
```

## Next Steps

Sau khi cài đặt thành công:
1. Test với file demo: `demo_mst_list.txt`
2. Cấu hình settings trong `setting.ini`
3. Sử dụng ứng dụng cho công việc thực tế

Chúc bạn sử dụng ứng dụng hiệu quả! 🚀
