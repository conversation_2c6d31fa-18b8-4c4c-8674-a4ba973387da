<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <AssemblyTitle>Ki<PERSON><PERSON> Tra MST Qt</AssemblyTitle>
    <AssemblyVersion>********</AssemblyVersion>
    <FileVersion>********</FileVersion>
    <AssemblyDescription>Ứng dụng kiểm tra MST với Qt GUI và OCR</AssemblyDescription>
    <Copyright>Copyright © 2024</Copyright>
    <PublishAot>false</PublishAot>
    <PublishTrimmed>false</PublishTrimmed>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <UseWindowsForms>false</UseWindowsForms>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <!-- Qt for C# -->
    <PackageReference Include="Python.Runtime" Version="3.0.3" />
    
    <!-- OCR and ML -->
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.19.2" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime.Gpu" Version="1.19.2" />
    
    <!-- Image processing -->
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
    
    <!-- HTTP and JSON -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RestSharp" Version="112.0.0" />
    
    <!-- System utilities -->
    <PackageReference Include="System.Management" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="setting.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Models\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
