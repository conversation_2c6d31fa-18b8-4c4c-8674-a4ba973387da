from PIL import Image
import os

# Đường dẫn thư mục chứa model đã tải sẵn
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
MODEL_DIR = os.path.join(BASE_DIR, "CaptchaData")

model = None
processor = None
device = None


def load_model(progress_callback=None):
    """
    Trì hoãn import mô hình để tránh cảnh báo CUDA hiện sớm.
    """
    global model, processor, device

    if progress_callback: progress_callback(5)

    # ⚠ Chỉ import khi thực sự cần để tránh warning hiện quá sớm
    import torch
    from transformers import VisionEncoderDecoderModel, TrOCRProcessor

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    if progress_callback: progress_callback(15)
    processor = TrOCRProcessor.from_pretrained(MODEL_DIR)

    if progress_callback: progress_callback(60)
    model = VisionEncoderDecoderModel.from_pretrained(MODEL_DIR)

    if progress_callback: progress_callback(90)
    model.to(device)

    if progress_callback: progress_callback(100)


def preprocess_image(image_path):
    image = Image.open(image_path).convert("RGBA")
    background = Image.new("RGBA", image.size, (255, 255, 255, 255))
    combined = Image.alpha_composite(background, image).convert("RGB")
    return combined


def solve_captcha(image_path):
    global model, processor, device
    if model is None or processor is None:
        return "❌ Model chưa được load"

    try:
        import torch  # import cục bộ để an toàn nếu gọi solve_captcha trước load_model
        img = preprocess_image(image_path)
        encoding = processor(images=img, return_tensors="pt").to(device)
        generated_ids = model.generate(encoding.pixel_values, max_length=20)
        text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
        return text.strip()
    except Exception as e:
        return f"❌ Lỗi OCR: {e}"
