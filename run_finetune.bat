@echo off
echo ========================================
echo     TrOCR Captcha Finetune Script
echo ========================================
echo.

echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found! Please install Python first.
    pause
    exit /b 1
)

echo.
echo Checking required folders...
if not exist "CaptchaData" (
    echo ERROR: CaptchaData folder not found!
    pause
    exit /b 1
)

if not exist "train_data" (
    echo ERROR: train_data folder not found!
    pause
    exit /b 1
)

echo.
echo Installing requirements...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install requirements!
    pause
    exit /b 1
)

echo.
echo Starting finetune process...
echo This may take a while depending on your hardware and data size.
echo.
python finetune_trocr.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo     Finetune completed successfully!
    echo ========================================
    echo.
    echo Model saved to: finetuned_model/
    echo.
    echo To test the model, run:
    echo python test_model.py
    echo.
) else (
    echo.
    echo ========================================
    echo     Finetune failed!
    echo ========================================
    echo Please check the error messages above.
    echo.
)

pause
