#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/polygamma_ops.h>

namespace at {


// aten::polygamma.out(int n, Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & polygamma_out(at::Tensor & out, int64_t n, const at::Tensor & self) {
    return at::_ops::polygamma_out::call(n, self, out);
}
// aten::polygamma.out(int n, Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & polygamma_outf(int64_t n, const at::Tensor & self, at::Tensor & out) {
    return at::_ops::polygamma_out::call(n, self, out);
}

// aten::polygamma(int n, Tensor self) -> Tensor
inline at::Tensor polygamma(int64_t n, const at::Tensor & self) {
    return at::_ops::polygamma::call(n, self);
}

}
