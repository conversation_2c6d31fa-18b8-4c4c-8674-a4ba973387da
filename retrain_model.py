import os
import shutil
import torch
from transformers import (
    VisionEncoderDecoderModel, 
    TrOCRProcessor,
    Seq2SeqTrainer, 
    Seq2SeqTrainingArguments,
    EarlyStoppingCallback
)
from PIL import Image
import logging
from dataclasses import dataclass
from typing import Dict, List, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class VisionSeq2SeqDataCollator:
    processor: TrOCRProcessor
    
    def __call__(self, features: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        # Tách pixel_values và labels
        pixel_values = torch.stack([f["pixel_values"] for f in features])
        labels = torch.stack([f["labels"] for f in features])
        
        # Thay -100 cho padding tokens
        labels[labels == self.processor.tokenizer.pad_token_id] = -100
        
        return {
            "pixel_values": pixel_values,
            "labels": labels
        }

class CaptchaDataset(torch.utils.data.Dataset):
    def __init__(self, data_dir, processor):
        self.data_dir = data_dir
        self.processor = processor
        self.image_paths = [f for f in os.listdir(data_dir) if f.endswith('.png')]
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = os.path.join(self.data_dir, self.image_paths[idx])
        image = Image.open(image_path).convert('RGB')
        label = os.path.splitext(self.image_paths[idx])[0]
        
        # Process image
        pixel_values = self.processor(image, return_tensors="pt").pixel_values.squeeze()
        
        # Process label
        labels = self.processor.tokenizer(
            label, 
            return_tensors="pt", 
            padding="max_length", 
            max_length=8,
            truncation=True
        ).input_ids.squeeze()
        
        return {
            "pixel_values": pixel_values,
            "labels": labels
        }

def main():
    # Xóa model cũ
    if os.path.exists("finetuned_model"):
        shutil.rmtree("finetuned_model")
        logger.info("Đã xóa model cũ")
    
    # Load model gốc
    logger.info("Đang tải model gốc...")
    model = VisionEncoderDecoderModel.from_pretrained("CaptchaData")
    processor = TrOCRProcessor.from_pretrained("CaptchaData")
    
    # Thiết lập device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    
    # Tạo dataset
    logger.info("Đang tạo dataset...")
    train_dataset = CaptchaDataset("train_data", processor)
    
    # Chia train/val
    train_size = int(0.9 * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size]
    )
    
    logger.info(f"Train: {len(train_dataset)}, Val: {len(val_dataset)}")
    
    # Data collator
    data_collator = VisionSeq2SeqDataCollator(processor=processor)
    
    # Training arguments
    training_args = Seq2SeqTrainingArguments(
        output_dir="finetuned_model",
        per_device_train_batch_size=8,
        per_device_eval_batch_size=8,
        eval_strategy="steps",
        eval_steps=500,
        logging_steps=100,
        save_steps=500,
        save_total_limit=3,
        num_train_epochs=2,
        learning_rate=5e-6,
        warmup_steps=200,
        fp16=torch.cuda.is_available(),
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        report_to=None,
        weight_decay=0.01,
    )
    
    # Trainer với custom data collator
    trainer = Seq2SeqTrainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        data_collator=data_collator,
        callbacks=[EarlyStoppingCallback(early_stopping_patience=3)]
    )
    
    # Train
    logger.info("Bắt đầu training...")
    trainer.train()
    
    # Save
    logger.info("Đang lưu model...")
    trainer.save_model()
    processor.save_pretrained("finetuned_model")
    
    logger.info("Hoàn thành!")

if __name__ == "__main__":
    main()
