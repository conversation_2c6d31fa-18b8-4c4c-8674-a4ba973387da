#############################################################################
# Makefile for building: kiem_tra_mst_qt
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  ..\..\kiem_tra_mst_qt.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_QML_DEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I"../../../C++ version" -I. -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore -Ibuild/moc -I/include -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\libQt6Widgets.a D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\libQt6Gui.a D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\libQt6Network.a D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\libQt6Core.a build\obj\kiem_tra_mst_qt_resource_res.o -lmingw32 D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = D:\PROGRAMS_D\Qt\6.9.1\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\PROGRAMS_D\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\PROGRAMS_D\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = build\obj\kiem_tra_mst_qt_resource_res.o
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = build\obj

####### Files

SOURCES       = ..\..\kiem_tra_mst_qt.cpp build\moc\moc_kiem_tra_mst_qt.cpp
OBJECTS       = build/obj/kiem_tra_mst_qt.o \
		build/obj/moc_kiem_tra_mst_qt.o

DIST          =  ..\..\kiem_tra_mst_qt.h ..\..\kiem_tra_mst_qt.cpp
QMAKE_TARGET  = kiem_tra_mst_qt
DESTDIR        = bin\ #avoid trailing-slash linebreak
TARGET         = kiem_tra_mst_qt.exe
DESTDIR_TARGET = bin\kiem_tra_mst_qt.exe

####### Build rules

first: all
all: Makefile.Release  bin/kiem_tra_mst_qt.exe

bin/kiem_tra_mst_qt.exe: D:/PROGRAMS_D/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a D:/PROGRAMS_D/Qt/6.9.1/mingw_64/lib/libQt6Gui.a D:/PROGRAMS_D/Qt/6.9.1/mingw_64/lib/libQt6Network.a D:/PROGRAMS_D/Qt/6.9.1/mingw_64/lib/libQt6Core.a D:/PROGRAMS_D/Qt/6.9.1/mingw_64/lib/libQt6EntryPoint.a $(OBJECTS) build/obj/kiem_tra_mst_qt_resource_res.o
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS) $(LIBS)

build/obj/kiem_tra_mst_qt_resource_res.o: kiem_tra_mst_qt_resource.rc
	windres -i kiem_tra_mst_qt_resource.rc -o build\obj\kiem_tra_mst_qt_resource_res.o --include-dir=. $(DEFINES)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release ..\..\kiem_tra_mst_qt.pro -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) kiem_tra_mst_qt.zip $(SOURCES) $(DIST) ..\..\kiem_tra_mst_qt.pro D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\spec_pre.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\device_config.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\common\sanitize.conf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\common\gcc-base.conf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\common\g++-base.conf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\common\windows-vulkan.conf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\common\g++-win32.conf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\common\windows-desktop.conf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\qconfig.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_freetype.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libpng.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_openxr_loader.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_linguist.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimedia.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_png_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3d.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3d_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3deffects.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3deffects_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticles.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dutils.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dutils_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dxr.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dxr_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimeline.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimeline_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_shadertools.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_shadertools_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_tools_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\qt_functions.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\qt_config.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\win32-g++\qmake.conf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\spec_post.prf .qmake.stash D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\toolchain.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\default_pre.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\win32\default_pre.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\resolve_config.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds_post.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\default_post.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\build_pass.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\qml_debug.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\precompile_header.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\warn_on.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\permissions.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\qt.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\resources_functions.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\resources.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\moc.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\win32\opengl.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\uic.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\qmake_use.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\file_copies.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\testcase_targets.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\exceptions.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\yacc.prf D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\lex.prf ..\..\kiem_tra_mst_qt.pro D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\Qt6Widgets.prl D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\Qt6Gui.prl D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\Qt6Network.prl D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\Qt6Core.prl D:\PROGRAMS_D\Qt\6.9.1\mingw_64\lib\Qt6EntryPoint.prl    D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp ..\..\kiem_tra_mst_qt.h  ..\..\kiem_tra_mst_qt.cpp     

clean: compiler_clean 
	-$(DEL_FILE) build\obj\kiem_tra_mst_qt.o build\obj\moc_kiem_tra_mst_qt.o
	-$(DEL_FILE) build\obj\kiem_tra_mst_qt_resource_res.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: build/moc/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) build\moc\moc_predefs.h
build/moc/moc_predefs.h: D:/PROGRAMS_D/Qt/6.9.1/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o build\moc\moc_predefs.h D:\PROGRAMS_D\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: build/moc/moc_kiem_tra_mst_qt.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) build\moc\moc_kiem_tra_mst_qt.cpp
build/moc/moc_kiem_tra_mst_qt.cpp: ../../kiem_tra_mst_qt.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QLineEdit \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QProgressBar \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QDialog \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QCheckBox \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QFrame \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/Qt \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QRunnable \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QThreadPool \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QSettings \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QStandardPaths \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QDir \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QRegularExpression \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QMutexLocker \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QFont \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QMovie \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qmovie.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qimagereader.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qimageiohandler.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qplugin.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfactoryinterface.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QPixmap \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QIcon \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QEvent \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkCookieJar \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkcookiejar.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QUrlQuery \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QProcess \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qprocess.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QDesktopServices \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QCoreApplication \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QCloseEvent \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QTextStream \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QEventLoop \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QBuffer \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbuffer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QImageReader \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QPainter \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		build/moc/moc_predefs.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\PROGRAMS_D\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include "C:/Users/<USER>/Desktop/kiem tra mst v4/C++ version/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/build/moc/moc_predefs.h" -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -I"C:/Users/<USER>/Desktop/kiem tra mst v4/C++ version" -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/PROGRAMS_D/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PROGRAMS_D/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PROGRAMS_D/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PROGRAMS_D/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PROGRAMS_D/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PROGRAMS_D/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\kiem_tra_mst_qt.h -o build\moc\moc_kiem_tra_mst_qt.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

build/obj/kiem_tra_mst_qt.o: ../../kiem_tra_mst_qt.cpp ../../kiem_tra_mst_qt.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QLineEdit \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QProgressBar \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QDialog \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QCheckBox \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtWidgets/QFrame \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/Qt \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QRunnable \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QThreadPool \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QSettings \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QStandardPaths \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QDir \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QRegularExpression \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QMutexLocker \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QFont \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QMovie \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qmovie.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qimagereader.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qimageiohandler.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qplugin.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qfactoryinterface.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QPixmap \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QIcon \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QEvent \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkCookieJar \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkcookiejar.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QUrlQuery \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QProcess \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qprocess.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QDesktopServices \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QCoreApplication \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QCloseEvent \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QTextStream \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QEventLoop \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/QBuffer \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtCore/qbuffer.h \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QImageReader \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/QPainter \
		D:/PROGRAMS_D/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\kiem_tra_mst_qt.o ..\..\kiem_tra_mst_qt.cpp

build/obj/moc_kiem_tra_mst_qt.o: build/moc/moc_kiem_tra_mst_qt.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_kiem_tra_mst_qt.o build\moc\moc_kiem_tra_mst_qt.cpp

####### Install

install_target: first FORCE
	@if not exist "C:$(INSTALL_ROOT:@msyshack@%=%)\Users\alibaba\Desktop\kiem tra mst v4\C++ version\bin" mkdir "C:$(INSTALL_ROOT:@msyshack@%=%)\Users\alibaba\Desktop\kiem tra mst v4\C++ version\bin" & if not exist "C:$(INSTALL_ROOT:@msyshack@%=%)\Users\alibaba\Desktop\kiem tra mst v4\C++ version\bin" exit 1
	-$(INSTALL_FILE) $(DESTDIR_TARGET) "C:$(INSTALL_ROOT:@msyshack@%=%)\Users\alibaba\Desktop\kiem tra mst v4\C++ version\bin\$(TARGET)"

uninstall_target: FORCE
	-$(DEL_FILE) "C:$(INSTALL_ROOT:@msyshack@%=%)\Users\alibaba\Desktop\kiem tra mst v4\C++ version\bin\$(TARGET)"
	-$(DEL_DIR) "C:$(INSTALL_ROOT:@msyshack@%=%)\Users\alibaba\Desktop\kiem tra mst v4\C++ version\bin" 


install: install_target  FORCE

uninstall: uninstall_target  FORCE

FORCE:

.SUFFIXES:

