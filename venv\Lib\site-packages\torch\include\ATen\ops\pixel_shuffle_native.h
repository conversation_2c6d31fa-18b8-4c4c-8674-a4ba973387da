#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor & pixel_shuffle_out(const at::Tensor & self, int64_t upscale_factor, at::Tensor & out);
TORCH_API at::Tensor pixel_shuffle_cpu(const at::Tensor & self, int64_t upscale_factor);
TORCH_API at::Tensor math_pixel_shuffle(const at::Tensor & self, int64_t upscale_factor);
} // namespace native
} // namespace at
