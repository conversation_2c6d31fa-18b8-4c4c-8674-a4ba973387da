@echo off
echo ========================================
echo Running Kiem Tra MST Qt C# Version 5.0.13
echo Target Framework: .NET 9.0
echo GUI Framework: Qt via Python.NET + PySide6
echo ========================================

echo.
echo 🔍 Pre-flight checks...

echo.
echo 1. Checking .NET runtime...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET runtime not found!
    echo Please install .NET 9.0 or later from:
    echo https://dotnet.microsoft.com/download/dotnet/9.0
    pause
    exit /b 1
)
echo ✅ .NET runtime available

echo.
echo 2. Checking Python + PySide6...
python -c "import PySide6.QtWidgets; print('✅ PySide6 ready')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ PySide6 not available!
    echo Installing PySide6...
    pip install PySide6
    if %errorlevel% neq 0 (
        echo ❌ Failed to install PySide6
        echo Please install manually: pip install PySide6
        pause
        exit /b 1
    )
    echo ✅ PySide6 installed
) else (
    echo ✅ PySide6 ready
)

echo.
echo 3. Checking build...
if not exist "bin\Release\net9.0-windows\KiemTraMST.dll" (
    echo ⚠️ Application not built yet
    echo Building Qt version...
    call build_qt.bat
    if %errorlevel% neq 0 (
        echo ❌ Build failed
        pause
        exit /b 1
    )
) else (
    echo ✅ Application built
)

echo.
echo ========================================
echo Starting Qt Application
echo ========================================

echo.
echo 🚀 Launching Qt GUI...
echo 📱 Main window will appear shortly...
echo 🎯 Features: Qt GUI identical to Python version
echo.

dotnet run --project KiemTraMST_Qt.csproj --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application failed to start
    echo.
    echo 🔧 Troubleshooting:
    echo 1. Check Python installation: python --version
    echo 2. Check PySide6: python -c "import PySide6"
    echo 3. Try building first: build_qt.bat
    echo 4. Check .NET version: dotnet --version
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Qt application closed normally
pause
