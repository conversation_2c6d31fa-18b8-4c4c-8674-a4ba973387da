C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\MainWindow.baml
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\App.baml
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\MainWindow.g.cs
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\App.g.cs
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST_MarkupCompile.cache
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.g.resources
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.AssemblyInfo.cs
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\setting.ini
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\KiemTraMST.exe
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\KiemTraMST.deps.json
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\KiemTraMST.runtimeconfig.json
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\KiemTraMST.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\KiemTraMST.pdb
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\Microsoft.ML.OnnxRuntime.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\RestSharp.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\System.Management.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\android\native\onnxruntime.aar
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\ios\native\onnxruntime.xcframework.zip
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\linux-arm64\native\libonnxruntime.so
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\linux-arm64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\linux-x64\native\libonnxruntime.so
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\linux-x64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\osx-arm64\native\libonnxruntime.dylib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\osx-x64\native\libonnxruntime.dylib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-arm64\native\onnxruntime.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-arm64\native\onnxruntime.lib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-arm64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-arm64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x64\native\onnxruntime.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x64\native\onnxruntime.lib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x86\native\onnxruntime.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x86\native\onnxruntime.lib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x86\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x86\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\linux-x64\native\libonnxruntime_providers_cuda.so
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\linux-x64\native\libonnxruntime_providers_tensorrt.so
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x64\native\onnxruntime_providers_cuda.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x64\native\onnxruntime_providers_cuda.lib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x64\native\onnxruntime_providers_tensorrt.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win-x64\native\onnxruntime_providers_tensorrt.lib
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\bin\Release\net9.0-windows\runtimes\win\lib\net9.0\System.Management.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.csproj.Up2Date
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\refint\KiemTraMST.dll
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.pdb
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\KiemTraMST.genruntimeconfig.cache
C:\Users\<USER>\Desktop\kiem tra mst v4\C Sharp version\obj\Release\net9.0-windows\ref\KiemTraMST.dll
