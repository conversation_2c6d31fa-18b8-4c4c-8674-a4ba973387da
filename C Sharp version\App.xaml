<Application x:Class="KiemTraMST.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style TargetType="Label">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style x:Key="StatusLabel" TargetType="Label">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style x:Key="ErrorLabel" TargetType="Label" BasedOn="{StaticResource StatusLabel}">
            <Setter Property="Foreground" Value="Red"/>
        </Style>
        
        <Style x:Key="SuccessLabel" TargetType="Label" BasedOn="{StaticResource StatusLabel}">
            <Setter Property="Foreground" Value="Green"/>
        </Style>
        
        <Style x:Key="WarningLabel" TargetType="Label" BasedOn="{StaticResource StatusLabel}">
            <Setter Property="Foreground" Value="Orange"/>
        </Style>
    </Application.Resources>
</Application>
