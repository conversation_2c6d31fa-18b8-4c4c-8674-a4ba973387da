#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/_fw_primal_copy_ops.h>

namespace at {


// aten::_fw_primal_copy(Tensor self, int level) -> Tensor
inline at::Tensor _fw_primal_copy(const at::Tensor & self, int64_t level) {
    return at::_ops::_fw_primal_copy::call(self, level);
}

// aten::_fw_primal_copy.out(Tensor self, int level, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _fw_primal_copy_out(at::Tensor & out, const at::Tensor & self, int64_t level) {
    return at::_ops::_fw_primal_copy_out::call(self, level, out);
}
// aten::_fw_primal_copy.out(Tensor self, int level, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _fw_primal_copy_outf(const at::Tensor & self, int64_t level, at::Tensor & out) {
    return at::_ops::_fw_primal_copy_out::call(self, level, out);
}

}
