#!/usr/bin/env python3
# mypy: allow-untyped-defs

# Copyright (c) Facebook, Inc. and its affiliates.
# All rights reserved.
#
# This source code is licensed under the BSD-style license found in the
# LICENSE file in the root directory of this source tree.
# Multiprocessing error-reporting module


from torch.distributed.elastic.multiprocessing.errors.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

__all__ = ['get_error_handler']

def get_error_handler():
    return ErrorHandler()
