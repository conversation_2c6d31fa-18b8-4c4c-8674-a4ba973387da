{"best_metric": 2.9062485694885254, "best_model_checkpoint": "finetuned_model\\checkpoint-1000", "epoch": 0.544069640914037, "eval_steps": 500, "global_step": 1000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0544069640914037, "grad_norm": 45.679100036621094, "learning_rate": 2.4500000000000003e-06, "loss": 4.1124, "step": 100}, {"epoch": 0.1088139281828074, "grad_norm": 21.349472045898438, "learning_rate": 4.95e-06, "loss": 3.2399, "step": 200}, {"epoch": 0.1632208922742111, "grad_norm": 19.273691177368164, "learning_rate": 4.8590333716916e-06, "loss": 3.1133, "step": 300}, {"epoch": 0.2176278563656148, "grad_norm": 13.488992691040039, "learning_rate": 4.715189873417722e-06, "loss": 3.0616, "step": 400}, {"epoch": 0.2720348204570185, "grad_norm": 11.980513572692871, "learning_rate": 4.571346375143844e-06, "loss": 3.0125, "step": 500}, {"epoch": 0.2720348204570185, "eval_loss": 2.9821252822875977, "eval_runtime": 54.7418, "eval_samples_per_second": 29.849, "eval_steps_per_second": 3.745, "step": 500}, {"epoch": 0.3264417845484222, "grad_norm": 12.950060844421387, "learning_rate": 4.427502876869966e-06, "loss": 3.0409, "step": 600}, {"epoch": 0.3808487486398259, "grad_norm": 13.172683715820312, "learning_rate": 4.2836593785960876e-06, "loss": 2.981, "step": 700}, {"epoch": 0.4352557127312296, "grad_norm": 10.958518028259277, "learning_rate": 4.13981588032221e-06, "loss": 2.9688, "step": 800}, {"epoch": 0.4896626768226333, "grad_norm": 11.293314933776855, "learning_rate": 3.995972382048331e-06, "loss": 2.9804, "step": 900}, {"epoch": 0.544069640914037, "grad_norm": 11.454388618469238, "learning_rate": 3.852128883774454e-06, "loss": 2.97, "step": 1000}, {"epoch": 0.544069640914037, "eval_loss": 2.9062485694885254, "eval_runtime": 54.8126, "eval_samples_per_second": 29.811, "eval_steps_per_second": 3.74, "step": 1000}], "logging_steps": 100, "max_steps": 3676, "num_input_tokens_seen": 0, "num_train_epochs": 2, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 3, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 5.986281527967744e+18, "train_batch_size": 8, "trial_name": null, "trial_params": null}