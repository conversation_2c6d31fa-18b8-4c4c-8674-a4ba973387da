#!/usr/bin/env python3
"""
Script để test việc detect Intel GPU integrated
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hardware_detector import hardware_detector

def test_intel_integrated_gpu():
    print("=== INTEL INTEGRATED GPU TEST ===\n")
    
    # Clear cache để test lại
    hardware_detector.clear_cache()
    
    # Test Intel GPU detection
    print("🔍 Testing Intel GPU Detection:")
    intel_gpus = hardware_detector.get_intel_gpu_info()
    
    if intel_gpus:
        print(f"   Found {len(intel_gpus)} Intel GPU(s):")
        for gpu in intel_gpus:
            print(f"     - {gpu['name']} (Memory: {gpu.get('memory', 'Unknown')})")
    else:
        print("   No Intel GPUs found")
    
    # Test detailed info
    print(f"\n📊 Detailed Hardware Info:")
    detailed_info = hardware_detector.get_detailed_info()
    
    print(f"   CPU: {detailed_info['cpu']['name']}")
    print(f"   NVIDIA GPUs: {len(detailed_info['nvidia_gpus'])}")
    print(f"   Intel GPUs: {len(detailed_info['intel_gpus'])}")
    
    # Test best device
    device_type, device_name, device_info = detailed_info['best_device']
    print(f"\n🏆 Best Device:")
    print(f"   Type: {device_type}")
    print(f"   Name: {device_name}")
    print(f"   Info keys: {list(device_info.keys())}")
    
    if 'intel_gpu' in device_info:
        print(f"   Intel GPU: {device_info['intel_gpu']}")
    
    # Test display name
    display_name = hardware_detector.get_device_display_name()
    print(f"\n📱 Display Name: {display_name}")

def simulate_intel_only_environment():
    """Mô phỏng môi trường chỉ có Intel GPU integrated"""
    print("\n=== SIMULATING INTEL-ONLY ENVIRONMENT ===")
    
    # Clear cache
    hardware_detector.clear_cache()
    
    # Mock data - giả lập không có NVIDIA, không có XPU support, chỉ có Intel integrated
    mock_pytorch_info = {
        'torch_available': True,
        'cuda_available': False,
        'cuda_device_count': 0,
        'cuda_devices': [],
        'current_device': 'cpu',
        'xpu_available': False,
        'xpu_device_count': 0,
        'other_backends': []
    }
    
    mock_nvidia_gpus = []
    
    mock_intel_gpus = [
        {
            'index': '0',
            'name': 'Intel(R) UHD Graphics 630',
            'memory': '1024',
            'vendor': 'Intel',
            'type': 'integrated'
        }
    ]
    
    # Inject vào cache
    hardware_detector._detection_cache['pytorch'] = mock_pytorch_info
    hardware_detector._detection_cache['nvidia'] = mock_nvidia_gpus
    hardware_detector._detection_cache['intel'] = mock_intel_gpus
    
    print("🔧 Simulating Intel-only environment...")
    
    # Test với mock data
    device_type, device_name, device_info = hardware_detector.determine_best_device()
    print(f"   Mock Best Device: {device_type} - {device_name}")
    
    display_name = hardware_detector.get_device_display_name()
    print(f"   Mock Display Name: {display_name}")
    
    # Test detailed info
    detailed_info = hardware_detector.get_detailed_info()
    print(f"   Mock Intel GPUs: {len(detailed_info['intel_gpus'])}")
    
    # Clear cache
    hardware_detector.clear_cache()
    print("   Cache cleared")

def simulate_intel_xpu_environment():
    """Mô phỏng môi trường có Intel XPU support"""
    print("\n=== SIMULATING INTEL XPU ENVIRONMENT ===")
    
    # Clear cache
    hardware_detector.clear_cache()
    
    # Mock data - giả lập có Intel XPU support
    mock_pytorch_info = {
        'torch_available': True,
        'cuda_available': False,
        'cuda_device_count': 0,
        'cuda_devices': [],
        'current_device': 'xpu',
        'xpu_available': True,
        'xpu_device_count': 1,
        'other_backends': ['XPU (Intel Arc A770)']
    }
    
    mock_nvidia_gpus = []
    
    mock_intel_gpus = [
        {
            'index': '0',
            'name': 'Intel Arc A770',
            'memory': '16384',
            'vendor': 'Intel',
            'type': 'discrete'
        }
    ]
    
    # Inject vào cache
    hardware_detector._detection_cache['pytorch'] = mock_pytorch_info
    hardware_detector._detection_cache['nvidia'] = mock_nvidia_gpus
    hardware_detector._detection_cache['intel'] = mock_intel_gpus
    
    print("🔧 Simulating Intel XPU environment...")
    
    # Test với mock data
    device_type, device_name, device_info = hardware_detector.determine_best_device()
    print(f"   Mock Best Device: {device_type} - {device_name}")
    
    display_name = hardware_detector.get_device_display_name()
    print(f"   Mock Display Name: {display_name}")
    
    # Clear cache
    hardware_detector.clear_cache()
    print("   Cache cleared")

if __name__ == "__main__":
    test_intel_integrated_gpu()
    simulate_intel_only_environment()
    simulate_intel_xpu_environment()
