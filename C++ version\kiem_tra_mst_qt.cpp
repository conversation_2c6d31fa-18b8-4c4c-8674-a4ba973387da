/*
 * Đ<PERSON> biên dịch file này, bạn cần cài đặt Qt6:
 * - Qt6 Core, Widgets, Network
 * - CMake hoặc qmake để build
 *
 * File này là phiên bản C++ được chuyển đổi 1:1 từ kiem_tra_mst_qt.py
 * sử dụng Qt6 để có giao diện hiện đại và hỗ trợ tốt cho emoji màu.
 */

#include "kiem_tra_mst_qt.h"

#include <random>
#include <chrono>
#include <thread>
#include <future>
#include <functional>

// ==============================================================================
// PHẦN CẤU HÌNH VÀ LOGIC BACKEND (CHUYỂN ĐỔI TỪ PYTHON)
// ==============================================================================

// Global configuration variables
int default_workers = 20;
int default_retry_failed = 20;
int default_timeout = 120;
int default_ocr_timeout = 120;

// Model loading variables (equivalent to Python globals)
QMutex model_lock;
bool model_loaded = false;

// Load configuration from setting.ini
void loadConfiguration() {
    QSettings settings("setting.ini", QSettings::IniFormat);
    // setIniCodec removed in Qt6, UTF-8 is default

    default_workers = settings.value("DEFAULT/max_workers", 20).toInt();
    default_retry_failed = settings.value("DEFAULT/retry_failed", 20).toInt();
    default_timeout = settings.value("DEFAULT/request_timeout", 120).toInt();
    default_ocr_timeout = settings.value("DEFAULT/ocr_timeout", 120).toInt();
}

// Create default setting.ini file
void createDefaultSettingFile() {
    QSettings settings("setting.ini", QSettings::IniFormat);
    // setIniCodec removed in Qt6, UTF-8 is default

    settings.setValue("DEFAULT/max_workers", 20);
    settings.setValue("DEFAULT/retry_failed", 20);
    settings.setValue("DEFAULT/request_timeout", 120);
    settings.setValue("DEFAULT/ocr_timeout", 120);

    settings.sync();
}

// ======= BẮT ĐẦU PHẦN GỘP TỪ trocr_solver.py (ENHANCED FOR C++) =======

QByteArray preprocessImage(const QByteArray& imageData) {
    // Load image from byte array
    QBuffer buffer;
    buffer.setData(imageData);
    buffer.open(QIODevice::ReadOnly);

    QImageReader reader(&buffer);
    QImage image = reader.read();

    if (image.isNull()) {
        return QByteArray();
    }

    // Convert RGBA to RGB with white background
    if (image.format() == QImage::Format_RGBA8888 || image.hasAlphaChannel()) {
        QImage background(image.size(), QImage::Format_RGB888);
        background.fill(Qt::white);

        QPainter painter(&background);
        painter.drawImage(0, 0, image);
        painter.end();

        image = background;
    } else {
        image = image.convertToFormat(QImage::Format_RGB888);
    }

    // Save processed image to byte array
    QByteArray processedData;
    QBuffer outputBuffer(&processedData);
    outputBuffer.open(QIODevice::WriteOnly);
    image.save(&outputBuffer, "PNG");

    return processedData;
}

QString solveCaptcha(const QByteArray& imageData) {
    // First preprocess the image
    QByteArray processedData = preprocessImage(imageData);
    if (processedData.isEmpty()) {
        return "❌ Lỗi xử lý ảnh";
    }

    // Try to use Python TrOCR via subprocess
    QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    QString tempImagePath = QDir(tempDir).filePath("temp_captcha.png");
    QString tempScriptPath = QDir(tempDir).filePath("trocr_solver_temp.py");

    // Save processed image to temp file
    QFile imageFile(tempImagePath);
    if (!imageFile.open(QIODevice::WriteOnly)) {
        return solveCaptchaFallback(imageData);
    }
    imageFile.write(processedData);
    imageFile.close();

    // Create temporary Python script
    QString pythonScript = R"(
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PIL import Image
    import torch
    from transformers import VisionEncoderDecoderModel, TrOCRProcessor

    # Try to load model from CaptchaData directory
    base_dir = os.path.dirname(os.path.abspath(__file__))
    model_dir = os.path.join(base_dir, "CaptchaData")

    if not os.path.exists(model_dir):
        # Try parent directory
        parent_dir = os.path.dirname(base_dir)
        model_dir = os.path.join(parent_dir, "CaptchaData")

    if not os.path.exists(model_dir):
        print("❌ Model directory not found")
        sys.exit(1)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    processor = TrOCRProcessor.from_pretrained(model_dir)
    model = VisionEncoderDecoderModel.from_pretrained(model_dir)
    model.to(device)

    # Load and process image
    image_path = sys.argv[1]
    image = Image.open(image_path).convert("RGB")

    encoding = processor(images=image, return_tensors="pt").to(device)
    generated_ids = model.generate(encoding.pixel_values, max_length=20)
    text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]

    print(text.strip())

except Exception as e:
    print(f"❌ Lỗi OCR: {e}")
    sys.exit(1)
)";

    QFile scriptFile(tempScriptPath);
    if (!scriptFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return solveCaptchaFallback(imageData);
    }
    QTextStream out(&scriptFile);
    out << pythonScript;
    scriptFile.close();

    // Execute Python script
    QProcess process;
    process.setWorkingDirectory(QCoreApplication::applicationDirPath());

    QStringList arguments;
    arguments << tempScriptPath << tempImagePath;

    process.start("python", arguments);
    if (!process.waitForFinished(30000)) { // 30 second timeout
        process.kill();
        // Cleanup
        QFile::remove(tempImagePath);
        QFile::remove(tempScriptPath);
        return solveCaptchaFallback(imageData);
    }

    QString result = process.readAllStandardOutput().trimmed();
    QString error = process.readAllStandardError().trimmed();

    // Cleanup
    QFile::remove(tempImagePath);
    QFile::remove(tempScriptPath);

    if (process.exitCode() != 0 || result.isEmpty() || result.contains("❌")) {
        return solveCaptchaFallback(imageData);
    }

    return result;
}

QString solveCaptchaFallback(const QByteArray& imageData) {
    Q_UNUSED(imageData)

    // Fallback to random generation when TrOCR fails
    static std::random_device rd;
    static std::mt19937 gen(rd());

    QString chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    static std::uniform_int_distribution<> dis(0, chars.length() - 1);

    QString result;
    for (int i = 0; i < 5; ++i) {
        result += chars[dis(gen)];
    }

    // Simulate processing time
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    return result;
}

void loadModel(std::function<void(int)> progressCallback) {
    QMutexLocker locker(&model_lock);
    if (model_loaded) {
        if (progressCallback) progressCallback(100);
        return;
    }
    
    if (progressCallback) progressCallback(5);
    
    // Simulate model loading time
    for (int i = 5; i <= 100; i += 5) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        if (progressCallback) progressCallback(i);
    }
    
    model_loaded = true;
}

void loadModel() {
    loadModel(nullptr);
}
// ======= KẾT THÚC PHẦN GỘP TỪ trocr_solver.py =======

// ==============================================================================
// IMPLEMENTATION
// ==============================================================================

// ==============================================================================
// IMPLEMENTATION
// ==============================================================================

const QString MainWindow::MST_COUNT_PREFIX = "📊 Số MST trong bảng: ";
const QString MainWindow::VERSION = "5.0.0";

// TraCuuWorker Implementation
TraCuuWorker::TraCuuWorker(const QString& mst, int idx, const DebugFlags& debugFlags, QObject* parent)
    : m_mst(mst), m_idx(idx), m_session(nullptr), m_debugFlags(debugFlags) {
    Q_UNUSED(parent) // Mark parameter as intentionally unused
    m_signals = new WorkerSignals();
    setAutoDelete(true);
}

TraCuuWorker::~TraCuuWorker() {
    delete m_session;
    delete m_signals;
}

void TraCuuWorker::run() {
    m_session = new QNetworkAccessManager();

    emit m_signals->log(QString("[%1] 🚀 Bắt đầu xử lý").arg(m_mst));
    emit m_signals->updateResult(m_idx, QString("Đang kiểm tra...\t%1").arg(m_mst));

    try {
        int attempt = 0;
        while (attempt < default_retry_failed) {
            try {
                emit m_signals->log(QString("[%1] 🧩 Đang lấy captcha").arg(m_mst));
                QString diaChi = this->traCuu();
                emit m_signals->log(QString("[%1] ✅ Đã lấy được địa chỉ").arg(m_mst));

                if (diaChi.toLower().startsWith("không lấy được")) {
                    throw std::runtime_error("Không lấy được địa chỉ");
                }

                if (diaChi.trimmed().isEmpty()) {
                    diaChi = "(Rỗng)";
                }

                QString resultText = QString("%1\t%2").arg(diaChi, m_mst);
                emit m_signals->updateResult(m_idx, resultText);
                emit m_signals->taskCompleted(true);
                break;
            } catch (const std::exception& e) {
                attempt++;
                QString errorMsg = QString::fromStdString(e.what());
                emit m_signals->log(QString("[%1] ❌ Lỗi lần %2: %3").arg(m_mst).arg(attempt).arg(errorMsg));

                if (attempt >= default_retry_failed) {
                    QString failText = QString("không lấy được địa chỉ\t%1").arg(m_mst);
                    emit m_signals->updateResult(m_idx, failText);
                    emit m_signals->taskCompleted(false);
                }
            }
        }
    } catch (const std::exception& e) {
        emit m_signals->error(QString("Lỗi nghiêm trọng worker [%1]: %2").arg(m_mst, QString::fromStdString(e.what())));
    }

    emit m_signals->finished();
}

QString TraCuuWorker::traCuu() {
    if (m_debugFlags.always_fail) {
        return "không lấy được địa chỉ (debug)";
    }

    // Set up headers
    QNetworkRequest request;
    request.setRawHeader("User-Agent", "Mozilla/5.0");
    request.setRawHeader("Referer", "https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp");

    // Initial request to get session
    QUrl initialUrl("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp");
    request.setUrl(initialUrl);

    QNetworkReply* initialReply = m_session->get(request);
    QEventLoop loop;
    QObject::connect(initialReply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    QTimer::singleShot(default_timeout * 1000, &loop, &QEventLoop::quit);
    loop.exec();

    if (initialReply->error() != QNetworkReply::NoError) {
        initialReply->deleteLater();
        throw std::runtime_error(QString("Lỗi kết nối: %1").arg(initialReply->errorString()).toStdString());
    }
    initialReply->deleteLater();

    // Get captcha
    QUrl captchaUrl("https://tracuunnt.gdt.gov.vn/tcnnt/captcha.png");
    request.setUrl(captchaUrl);

    QNetworkReply* captchaReply = m_session->get(request);
    QEventLoop captchaLoop;
    QObject::connect(captchaReply, &QNetworkReply::finished, &captchaLoop, &QEventLoop::quit);
    QTimer::singleShot(default_timeout * 1000, &captchaLoop, &QEventLoop::quit);
    captchaLoop.exec();

    if (captchaReply->error() != QNetworkReply::NoError) {
        captchaReply->deleteLater();
        throw std::runtime_error(QString("Lỗi tải captcha: %1").arg(captchaReply->errorString()).toStdString());
    }

    QByteArray imageBuffer = captchaReply->readAll();
    captchaReply->deleteLater();

    // Solve captcha
    QString code;
    if (m_debugFlags.wrong_captcha) {
        // Generate random wrong captcha
        static std::random_device rd;
        static std::mt19937 gen(rd());
        QString chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
        static std::uniform_int_distribution<> dis(0, chars.length() - 1);
        for (int i = 0; i < 5; ++i) {
            code += chars[dis(gen)];
        }
    } else {
        try {
            // Use future for timeout handling with real TrOCR
            auto future = std::async(std::launch::async, [&imageBuffer]() {
                return solveCaptcha(imageBuffer);
            });

            if (future.wait_for(std::chrono::seconds(default_ocr_timeout)) == std::future_status::timeout) {
                throw std::runtime_error(QString("OCR captcha timeout sau %1 giây").arg(default_ocr_timeout).toStdString());
            }

            code = future.get();
            if (code.isEmpty() || code.contains("❌")) {
                throw std::runtime_error("Lỗi OCR captcha");
            }
        } catch (const std::exception& e) {
            throw std::runtime_error(QString("Lỗi giải captcha: %1").arg(QString::fromStdString(e.what())).toStdString());
        }
    }

    // Prepare POST data
    QUrlQuery postData;
    postData.addQueryItem("mst", m_mst);
    postData.addQueryItem("fullname", "");
    postData.addQueryItem("address", "");
    postData.addQueryItem("captcha", code);

    emit m_signals->log(QString("[%1] 📤 Gửi request tra cứu").arg(m_mst));

    // Send POST request
    QUrl postUrl("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp");
    request.setUrl(postUrl);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");

    QNetworkReply* postReply = m_session->post(request, postData.toString(QUrl::FullyEncoded).toUtf8());
    QEventLoop postLoop;
    QObject::connect(postReply, &QNetworkReply::finished, &postLoop, &QEventLoop::quit);
    QTimer::singleShot(default_timeout * 1000, &postLoop, &QEventLoop::quit);
    postLoop.exec();

    if (postReply->error() != QNetworkReply::NoError) {
        postReply->deleteLater();
        throw std::runtime_error(QString("Lỗi gửi request: %1").arg(postReply->errorString()).toStdString());
    }

    QString html = QString::fromUtf8(postReply->readAll());
    postReply->deleteLater();

    // Save response for debug
    if (m_debugFlags.save_response) {
        QDir debugDir("debug");
        if (!debugDir.exists()) {
            debugDir.mkpath(".");
        }

        QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
        QString filePath = QString("debug/debug_raw_%1_%2.txt").arg(m_mst, timestamp);

        QFile file(filePath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            // setCodec removed in Qt6, UTF-8 is default
            out << html;
            file.close();
            emit m_signals->log(QString("[DEBUG] Đã lưu file trả về: %1").arg(filePath));
        }
    }

    // Check for captcha error
    QString htmlLower = html.toLower();
    if (htmlLower.contains("vui lòng nhập đúng mã xác nhận") ||
        htmlLower.contains("vui l&#242;ng nh&#7853;p &#273;&#250;ng m&#227; x&#225;c nh&#7853;n")) {
        emit m_signals->captchaResult(false);
        throw std::runtime_error("Captcha sai");
    }

    emit m_signals->captchaResult(true);

    // Check for common error messages
    if (htmlLower.contains("không tìm thấy người nộp thuế")) {
        return "Không tìm thấy người nộp thuế nào phù hợp.";
    }
    if (htmlLower.contains("mã số thuế không hợp lệ")) {
        return "Mã số thuế không hợp lệ.";
    }

    // Parse JSON response
    try {
        QRegularExpression regex(R"(var\s+nntJson\s*=\s*(\{.*?\});)", QRegularExpression::DotMatchesEverythingOption);
        QRegularExpressionMatch match = regex.match(html);

        if (match.hasMatch()) {
            QString jsonStr = match.captured(1);
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(jsonStr.toUtf8(), &error);

            if (error.error != QJsonParseError::NoError) {
                return QString("không lấy được địa chỉ (lỗi parse JSON: %1)").arg(error.errorString());
            }

            QJsonObject data = doc.object();
            QJsonArray dataArray = data["DATA"].toArray();

            QJsonObject info;
            bool found = false;
            for (const auto& item : dataArray) {
                QJsonObject obj = item.toObject();
                if (obj["MST"].toString() == m_mst) {
                    info = obj;
                    found = true;
                    break;
                }
            }

            if (!found) {
                return "không lấy được địa chỉ (không tìm thấy MST phù hợp)";
            }

            QJsonArray dktDiaChi = info["DKT_DIA_CHI"].toArray();
            QJsonObject diaChiInfo;

            for (const auto& item : dktDiaChi) {
                QJsonObject obj = item.toObject();
                if (obj["LOAI"].toString() == "0300") {
                    diaChiInfo = obj;
                    break;
                }
            }

            QString diaChi = diaChiInfo["DIA_CHI"].toString();
            QString xa = diaChiInfo["PHUONG_XA"].toString();
            QString tinh = diaChiInfo["TINH_TP"].toString();

            if (diaChi.isEmpty()) {
                diaChi = info["DIA_CHI_TRU_SO_EXT"].toString().trimmed();
            }

            QStringList addressParts;
            if (!diaChi.isEmpty()) addressParts << diaChi;
            if (!xa.isEmpty()) addressParts << xa;
            if (!tinh.isEmpty()) addressParts << tinh;

            return addressParts.join(", ");
        }
    } catch (const std::exception& e) {
        return QString("không lấy được địa chỉ (lỗi parse: %1)").arg(QString::fromStdString(e.what()));
    }

    return "không lấy được địa chỉ";
}

// EmojiAnimationLabel Implementation
EmojiAnimationLabel::EmojiAnimationLabel(const QStringList& emojis, int delay, int size, QWidget* parent)
    : QLabel(parent), m_emojis(emojis), m_currentIndex(0) {

    QFont font("Segoe UI Emoji", size / 2);
    setFont(font);

    m_timer = new QTimer(this);
    connect(m_timer, &QTimer::timeout, this, &EmojiAnimationLabel::animate);
    m_timer->start(delay);

    animate(); // Show first emoji immediately
}

void EmojiAnimationLabel::animate() {
    if (m_emojis.isEmpty()) {
        return; // Nothing to animate
    }

    if (m_currentIndex < 0 || m_currentIndex >= m_emojis.size()) {
        // Reset index if out of bounds
        m_currentIndex = 0;
    }

    if (m_currentIndex < m_emojis.size()) {
        setText(m_emojis[m_currentIndex]);
        m_currentIndex = (m_currentIndex + 1) % m_emojis.size();
    }
}

// FlippingGIFLabel Implementation
FlippingGIFLabel::FlippingGIFLabel(const QString& gifPath, int flipInterval, const QSize& size, QWidget* parent)
    : QLabel(parent), m_isFlipped(false) {

    m_movie = new QMovie(gifPath, QByteArray(), this);
    m_movie->setScaledSize(size);

    // Connect frame changed signal
    connect(m_movie, &QMovie::frameChanged, this, &FlippingGIFLabel::onFrameChanged);

    // Timer for flipping
    m_flipper = new QTimer(this);
    connect(m_flipper, &QTimer::timeout, this, &FlippingGIFLabel::toggleFlip);
    m_flipper->start(flipInterval);

    m_movie->start();
}

FlippingGIFLabel::~FlippingGIFLabel() {
    if (m_movie) {
        m_movie->stop();
    }
    if (m_flipper) {
        m_flipper->stop();
    }
}

bool FlippingGIFLabel::event(QEvent* event) {
    if (event->type() == QEvent::Destroy) {
        // Cleanup when widget is being destroyed
        if (m_movie) {
            m_movie->stop();
        }
        if (m_flipper) {
            m_flipper->stop();
        }
    }
    return QLabel::event(event);
}

void FlippingGIFLabel::toggleFlip() {
    m_isFlipped = !m_isFlipped;
    onFrameChanged(); // Update current frame with new flip state
}

void FlippingGIFLabel::onFrameChanged(int frameNumber) {
    Q_UNUSED(frameNumber)

    QPixmap pixmap = m_movie->currentPixmap();
    if (m_isFlipped) {
        // Flip horizontally
        pixmap = pixmap.transformed(QTransform().scale(-1, 1));
    }

    setPixmap(pixmap);
}

// LoadingModelDialog Implementation
LoadingModelDialog::LoadingModelDialog(QWidget* parent)
    : QDialog(parent) {

    setWindowTitle("Đang tải model TrOCR...");
    setFixedSize(320, 140);
    setModal(true);
    setWindowFlag(Qt::WindowCloseButtonHint, false);

    QVBoxLayout* layout = new QVBoxLayout(this);

    QStringList emojis = {"⏳", "⌛"};
    m_emoji = new EmojiAnimationLabel(emojis, 300, 32, this);
    m_emoji->setAlignment(Qt::AlignCenter);
    layout->addWidget(m_emoji);

    m_label = new QLabel("Đang tải model TrOCR, vui lòng chờ...", this);
    m_label->setAlignment(Qt::AlignCenter);
    layout->addWidget(m_label);

    m_progress = new QProgressBar(this);
    m_progress->setRange(0, 100);
    layout->addWidget(m_progress);
}

void LoadingModelDialog::updateProgress(int value) {
    m_progress->setValue(value);
    if (value == 100) {
        accept();
    }
}

void LoadingModelDialog::setError(const QString& message) {
    m_label->setText(QString("Lỗi: %1").arg(message));
    setWindowFlag(Qt::WindowCloseButtonHint, true); // Allow closing on error
    update();
}

// MainWindow Implementation
MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent), m_modelReady(false), m_pendingStart(false), m_lastModelPercent(0) {

    // Load configuration
    loadConfiguration();

    // Set window title and icon
    setWindowTitle(QString("Tra cứu thuế v%1 (TNT)").arg(VERSION));
    setWindowIcon(QIcon("default.ico"));

    // Initialize state
    setupState();

    // Initialize UI
    setupUi();

    // Setup connections
    setupConnections();

    // Load model in background
    loadModelBg();

    // Check setting file
    checkSettingFile();
    updateMstCount();
}

MainWindow::~MainWindow() {
    // Cleanup is handled by Qt's parent-child relationship
}

void MainWindow::setupState() {
    m_startTime = 0;
    m_isPaused = false;
    m_activeThreads = 0;
    m_ok = m_fail = m_done = m_captchaFail = m_captchaOk = 0;
    m_total = 0;
    m_kqWidget = nullptr;
}

void MainWindow::setupUi() {
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    m_mainLayout = new QVBoxLayout(m_centralWidget);

    // 1. Hướng dẫn và Số lượng MST
    QHBoxLayout* topLayout = new QHBoxLayout;
    topLayout->addWidget(new QLabel("📋 Mỗi dòng 1 MST (không chứa khoảng trắng/tab)"));
    topLayout->addStretch();
    m_mstCountLabel = new QLabel(MST_COUNT_PREFIX + "0");
    topLayout->addWidget(m_mstCountLabel);
    m_mainLayout->addLayout(topLayout);

    // 2. Textbox nhập liệu
    m_textBox = new QTextEdit;
    m_textBox->setLineWrapMode(QTextEdit::NoWrap);
    m_textBox->setPlaceholderText("Dán danh sách MST vào đây...");
    m_textBox->setMinimumHeight(150);
    m_mainLayout->addWidget(m_textBox);

    // 3. Cài đặt luồng (canh giữa)
    QHBoxLayout* workersLayout = new QHBoxLayout;
    workersLayout->addStretch();
    workersLayout->addWidget(new QLabel("Số luồng: (có thể thay đổi khi đang chạy)"));
    m_entryWorkers = new QLineEdit(QString::number(default_workers));
    m_entryWorkers->setFixedWidth(50);
    workersLayout->addWidget(m_entryWorkers);
    workersLayout->addWidget(new QLabel("(thấy máy lag thì hạ xuống)"));
    workersLayout->addStretch();
    m_mainLayout->addLayout(workersLayout);

    // 4. Các nút chức năng
    QHBoxLayout* btnLayout = new QHBoxLayout;
    m_btnStart = new QPushButton("🚀 Bắt đầu tra");
    m_btnPause = new QPushButton("⏸ Tạm dừng");
    m_btnPause->setEnabled(false);
    m_btnRetry = new QPushButton("🔁 Làm lại từ kết quả cũ");
    m_btnOpenResult = new QPushButton("📄 Mở file kết quả");
    m_btnOpenResult->setEnabled(false);
    m_btnOpenFolder = new QPushButton("📁 Mở thư mục");
    m_btnDebug = new QPushButton("🐞 Debug");
    m_btnExit = new QPushButton("❌ Thoát");

    btnLayout->addWidget(m_btnStart);
    btnLayout->addWidget(m_btnPause);
    btnLayout->addWidget(m_btnRetry);
    btnLayout->addWidget(m_btnOpenResult);
    btnLayout->addWidget(m_btnOpenFolder);
    btnLayout->addWidget(m_btnDebug);
    btnLayout->addWidget(m_btnExit);
    m_mainLayout->addLayout(btnLayout);

    // 5. Khung trạng thái
    QFrame* statusFrame = new QFrame;
    statusFrame->setFrameShape(QFrame::StyledPanel);
    QHBoxLayout* statusLayout = new QHBoxLayout(statusFrame);

    m_kqFrame = new QHBoxLayout;
    m_labelKq = new QLabel("");
    m_kqFrame->addWidget(m_labelKq);

    m_timeLabel = new QLabel("⏱ Thời gian: 00:00:00");

    statusLayout->addStretch();
    statusLayout->addLayout(m_kqFrame);
    statusLayout->addWidget(m_timeLabel);
    statusLayout->addStretch();

    m_mainLayout->addWidget(statusFrame);

    // 6. Label trạng thái chi tiết
    m_statusLabel = new QLabel("");
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_mainLayout->addWidget(m_statusLabel);

    // 7. Label trạng thái model
    m_modelStatusLabel = new QLabel("Đang tải model...");
    m_modelStatusLabel->setAlignment(Qt::AlignCenter);
    m_modelStatusLabel->setStyleSheet("color: orange;");
    m_mainLayout->addWidget(m_modelStatusLabel);

    // 8. Log box
    m_logBox = new QTextEdit;
    m_logBox->setReadOnly(true);
    m_mainLayout->addWidget(m_logBox);

    // Timer cho đồng hồ
    m_timer = new QTimer(this);

    updateStatus();
}

void MainWindow::setupConnections() {
    connect(m_textBox, &QTextEdit::textChanged, this, &MainWindow::updateMstCount);
    connect(m_entryWorkers, &QLineEdit::editingFinished, this, &MainWindow::onWorkersChange);

    connect(m_btnStart, &QPushButton::clicked, this, &MainWindow::batDau);
    connect(m_btnPause, &QPushButton::clicked, this, &MainWindow::togglePause);
    connect(m_btnRetry, &QPushButton::clicked, this, &MainWindow::lamLai);
    connect(m_btnOpenResult, &QPushButton::clicked, this, &MainWindow::openResultFileDirectly);
    connect(m_btnOpenFolder, &QPushButton::clicked, this, &MainWindow::openResultFolder);
    connect(m_btnDebug, &QPushButton::clicked, this, &MainWindow::openDebugWindow);
    connect(m_btnExit, &QPushButton::clicked, this, &QWidget::close);

    connect(m_timer, &QTimer::timeout, this, &MainWindow::updateTimeDisplay);
}

void MainWindow::closeEvent(QCloseEvent* event) {
    QMessageBox::StandardButton reply = QMessageBox::question(this, "Xác nhận thoát",
        "Bạn có chắc muốn thoát chương trình không?",
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        event->accept();
    } else {
        event->ignore();
    }
}

// MainWindow Slot Implementations
void MainWindow::log(const QString& message) {
    m_logBox->append(message);
}

void MainWindow::updateMstCount() {
    QString content = m_textBox->toPlainText().trimmed();
    if (!content.isEmpty()) {
        QStringList lines = content.split('\n', Qt::SkipEmptyParts);
        QStringList validLines;
        for (const QString& line : lines) {
            if (!line.trimmed().isEmpty()) {
                validLines.append(line.trimmed());
            }
        }
        int count = validLines.size();
        m_mstCountLabel->setText(MST_COUNT_PREFIX + QString::number(count));
    } else {
        m_mstCountLabel->setText(MST_COUNT_PREFIX + "0");
    }
}

void MainWindow::updateStatus() {
    int captchaTotal = m_captchaOk + m_captchaFail;
    double percent = (captchaTotal > 0) ? (m_captchaOk * 100.0 / captchaTotal) : 0.0;
    QString captchaInfo = QString("🧩 Captcha đúng: %1/%2 (%3%)").arg(m_captchaOk).arg(captchaTotal).arg(percent, 0, 'f', 1);

    int maxWorkers = QThreadPool::globalInstance()->maxThreadCount();
    QString statusText = QString("✔ Thành công: %1   ❌ Thất bại: %2   %3   "
                                "✍️ Đã xử lý: %4 / %5   "
                                "👨‍👩‍👦‍👦 Luồng: %6/%7")
                        .arg(m_ok).arg(m_fail).arg(captchaInfo)
                        .arg(m_done).arg(m_total)
                        .arg(m_activeThreads).arg(maxWorkers);
    m_statusLabel->setText(statusText);
}

void MainWindow::updateTimeDisplay() {
    if (m_startTime > 0) {
        qint64 elapsed = QDateTime::currentMSecsSinceEpoch() / 1000 - m_startTime;
        int hours = elapsed / 3600;
        int minutes = (elapsed % 3600) / 60;
        int seconds = elapsed % 60;
        m_timeLabel->setText(QString("⏱ %1:%2:%3")
                            .arg(hours, 2, 10, QChar('0'))
                            .arg(minutes, 2, 10, QChar('0'))
                            .arg(seconds, 2, 10, QChar('0')));
    }
}

void MainWindow::loadModelBg() {
    m_modelStatusLabel->setText("Đang tải model...");
    m_modelStatusLabel->setStyleSheet("color: orange;");

    // Create a worker to load model in background
    class ModelLoader : public QRunnable {
    public:
        ModelLoader(MainWindow* window) : m_window(window) {
            setAutoDelete(true);
        }

        void run() override {
            try {
                loadModel([this](int progress) {
                    QMetaObject::invokeMethod(m_window, "onModelProgress",
                                            Qt::QueuedConnection, Q_ARG(int, progress));
                });
                QMetaObject::invokeMethod(m_window, "onModelLoaded", Qt::QueuedConnection);
            } catch (const std::exception& e) {
                QMetaObject::invokeMethod(m_window, "onModelLoadError",
                                        Qt::QueuedConnection, Q_ARG(QString, QString::fromStdString(e.what())));
            }
        }

    private:
        MainWindow* m_window;
    };

    QThreadPool::globalInstance()->start(new ModelLoader(this));
}

void MainWindow::onModelProgress(int percent) {
    m_lastModelPercent = percent;
    if (m_pendingStart) {
        m_modelStatusLabel->setText(QString("Đang tải model... Sẽ tự động bắt đầu khi model sẵn sàng. %1%").arg(percent));
    } else {
        m_modelStatusLabel->setText(QString("Đang tải model... %1%").arg(percent));
    }
}

void MainWindow::onModelLoaded() {
    m_modelStatusLabel->setText("Model đã sẵn sàng");
    m_modelStatusLabel->setStyleSheet("color: green;");
    m_modelReady = true;

    // If there's a pending start, automatically begin
    if (m_pendingStart) {
        m_pendingStart = false;
        batDau();
    }
}

void MainWindow::onModelLoadError(const QString& errorMsg) {
    m_modelStatusLabel->setText(QString("Lỗi tải model: %1").arg(errorMsg));
    m_modelStatusLabel->setStyleSheet("color: red;");
    QMessageBox::critical(this, "Lỗi nghiêm trọng",
                         QString("Không thể tải model TrOCR:\n%1\nChương trình sẽ thoát.").arg(errorMsg));
    close();
}

void MainWindow::checkSettingFile() {
    QFileInfo settingFile("setting.ini");
    if (settingFile.exists()) {
        try {
            QSettings testSettings("setting.ini", QSettings::IniFormat);
            // setIniCodec removed in Qt6, UTF-8 is default

            bool hasWorkers = testSettings.contains("DEFAULT/max_workers");
            bool hasRetry = testSettings.contains("DEFAULT/retry_failed");
            bool hasTimeout = testSettings.contains("DEFAULT/request_timeout");
            bool hasOcrTimeout = testSettings.contains("DEFAULT/ocr_timeout");

            if (hasWorkers && hasRetry && hasTimeout && hasOcrTimeout) {
                log("✅ File setting.ini đã được tìm thấy và hợp lệ");
                log(QString("   - Số luồng mặc định: %1").arg(default_workers));
                log(QString("   - Số lần thử lại: %1").arg(default_retry_failed));
                log(QString("   - Timeout request: %1s").arg(default_timeout));
                log(QString("   - Timeout OCR captcha: %1s").arg(default_ocr_timeout));
            } else {
                log("⚠️ File setting.ini tồn tại nhưng thiếu một số cấu hình");
                log("   Sử dụng giá trị mặc định");
            }
        } catch (const std::exception& e) {
            log(QString("⚠️ File setting.ini có lỗi: %1").arg(QString::fromStdString(e.what())));
            log("   Sử dụng giá trị mặc định");
        }
    } else {
        log("❌ Không tìm thấy file setting.ini");
        log("   Tạo file setting.ini với cấu hình mặc định...");
        createDefaultSettingFile();
    }
}

void MainWindow::createDefaultSettingFile() {
    try {
        ::createDefaultSettingFile(); // Call global function
        log("✅ Đã tạo file setting.ini với cấu hình mặc định");
    } catch (const std::exception& e) {
        log(QString("❌ Không thể tạo file setting.ini: %1").arg(QString::fromStdString(e.what())));
    }
}

void MainWindow::saveWorkersToSetting(int workersCount) {
    try {
        QSettings settings("setting.ini", QSettings::IniFormat);
        // setIniCodec removed in Qt6, UTF-8 is default
        settings.setValue("DEFAULT/max_workers", workersCount);
        settings.sync();

        // Update global variable
        default_workers = workersCount;

        log(QString("💾 Đã lưu số luồng %1 vào file setting.ini").arg(workersCount));
    } catch (const std::exception& e) {
        log(QString("⚠️ Lỗi khi lưu setting: %1").arg(QString::fromStdString(e.what())));
    }
}

void MainWindow::onWorkersChange() {
    QString value = m_entryWorkers->text().trimmed();
    bool ok;
    int newWorkers = value.toInt(&ok);

    if (ok && newWorkers > 0) {
        int currentMax = QThreadPool::globalInstance()->maxThreadCount();
        if (newWorkers != currentMax) {
            QMessageBox::StandardButton reply = QMessageBox::question(this, "Xác nhận",
                QString("Bạn có muốn cập nhật số luồng từ %1 thành %2 không?").arg(currentMax).arg(newWorkers));

            if (reply == QMessageBox::Yes) {
                QThreadPool::globalInstance()->setMaxThreadCount(newWorkers);
                log(QString("🔧 Đã cập nhật số luồng: %1").arg(newWorkers));
                saveWorkersToSetting(newWorkers);
                updateStatus();
            } else {
                m_entryWorkers->setText(QString::number(currentMax));
            }
        }
    } else {
        m_entryWorkers->setText(QString::number(QThreadPool::globalInstance()->maxThreadCount()));
    }
}

void MainWindow::togglePause() {
    m_isPaused = !m_isPaused;
    if (m_isPaused) {
        m_btnPause->setText("▶️ Chạy tiếp");
        log("⏸ Đã tạm dừng. Các luồng đang chạy sẽ hoàn thành nốt.");
        QThreadPool::globalInstance()->setMaxThreadCount(0); // Pause new tasks
    } else {
        m_btnPause->setText("⏸ Tạm dừng");
        int maxThreads = m_entryWorkers->text().toInt();
        log(QString("▶️ Đã chạy tiếp với %1 luồng.").arg(maxThreads));
        QThreadPool::globalInstance()->setMaxThreadCount(maxThreads);
        processQueue();
    }
    updateStatus();
}

void MainWindow::showKqLoading(bool show) {
    if (show) {
        if (m_kqWidget) return;

        QString gifPath = "resource/working.gif";
        QFileInfo gifFile(gifPath);
        if (gifFile.exists()) {
            m_kqWidget = new FlippingGIFLabel(gifPath, 2000, QSize(28, 28), this);
        } else {
            // Fallback to emoji animation
            QStringList emojis = {"🧎🏻‍♂️", "🧍🏻‍♂️", "🚶🏻‍♂️", "🏃🏻‍♂️"};
            m_kqWidget = new EmojiAnimationLabel(emojis, 300, 28, this);
        }
        m_kqFrame->insertWidget(0, m_kqWidget);
        m_labelKq->setText(" Đang chạy...");
    } else {
        if (m_kqWidget) {
            m_kqWidget->deleteLater();
            m_kqWidget = nullptr;
        }
        m_labelKq->setText("");
    }
}

void MainWindow::showKqDone() {
    showKqLoading(false);
    m_kqWidget = new QLabel("🎉", this);
    QFont font("Segoe UI Emoji", 16);
    m_kqWidget->setFont(font);
    m_kqFrame->insertWidget(0, m_kqWidget);
    m_labelKq->setText(" Đã xong hết rồi!");
}

void MainWindow::handleWorkerError(const QString& errorMsg) {
    log(errorMsg);
    // Could add additional logic here, like showing message box
}

void MainWindow::batDau() {
    if (!m_modelReady) {
        // If model not ready, wait for model to finish then auto-start
        m_pendingStart = true;
        m_modelStatusLabel->setText(QString("Đang tải model... Sẽ tự động bắt đầu khi model sẵn sàng. %1%").arg(m_lastModelPercent));
        m_btnStart->setEnabled(false);
        return;
    }

    m_btnStart->setEnabled(false);
    m_pendingStart = false;

    // Reset result label state
    m_labelKq->setText("");
    if (m_kqWidget) {
        m_kqWidget->deleteLater();
        m_kqWidget = nullptr;
    }

    QString rawText = m_textBox->toPlainText();
    QStringList danhSachRaw;
    QStringList lines = rawText.split('\n', Qt::SkipEmptyParts);
    for (const QString& line : lines) {
        QString trimmed = line.trimmed();
        if (!trimmed.isEmpty()) {
            danhSachRaw.append(trimmed);
        }
    }

    if (danhSachRaw.isEmpty()) {
        m_labelKq->setText("❌ Không có MST nào.");
        m_btnStart->setEnabled(true);
        return;
    }

    // Check for spaces/tabs in MST
    for (int idx = 0; idx < danhSachRaw.size(); ++idx) {
        const QString& mst = danhSachRaw[idx];
        if (mst.contains(' ') || mst.contains('\t')) {
            m_labelKq->setText(QString("❌ MST dòng %1 chứa khoảng trắng/tab: [%2]").arg(idx + 1).arg(mst));
            m_btnStart->setEnabled(true);
            return;
        }
    }

    m_danhSach = danhSachRaw;

    // Reset state
    m_updateIndices.clear();
    m_outputLinesFull.clear();
    m_ok = m_fail = m_done = m_captchaFail = m_captchaOk = 0;
    m_activeThreads = 0;
    m_total = m_danhSach.size();
    m_outputLines.clear();
    m_outputLines.resize(m_total);
    m_isPaused = false;

    // Setup result file
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    m_ketquaFile = QString("ketqua_%1.txt").arg(timestamp);

    log(QString("Bắt đầu tra cứu %1 MST...").arg(m_total));
    updateStatus();

    // Start timer
    m_startTime = QDateTime::currentMSecsSinceEpoch() / 1000;
    m_timer->start(1000);

    // Update UI
    m_btnStart->setEnabled(false);
    m_btnRetry->setEnabled(false);
    m_btnPause->setEnabled(true);
    m_btnOpenResult->setEnabled(true);
    showKqLoading(true);

    // Create queue and start processing
    m_taskQueue.clear();
    for (int i = 0; i < m_danhSach.size(); ++i) {
        m_taskQueue.append(qMakePair(i, m_danhSach[i]));
    }

    QThreadPool::globalInstance()->setMaxThreadCount(m_entryWorkers->text().toInt());
    processQueue();
}

void MainWindow::processQueue() {
    while (!m_taskQueue.isEmpty()) {
        if (QThreadPool::globalInstance()->activeThreadCount() >= QThreadPool::globalInstance()->maxThreadCount()) {
            break; // Wait for available threads
        }

        QPair<int, QString> task = m_taskQueue.takeFirst();
        int idx = task.first;
        QString mst = task.second;

        TraCuuWorker* worker = new TraCuuWorker(mst, idx, m_debugFlags);
        QObject::connect(worker->getSignals(), &WorkerSignals::log, this, &MainWindow::log);
        QObject::connect(worker->getSignals(), &WorkerSignals::updateResult, this, &MainWindow::onWorkerUpdate);
        QObject::connect(worker->getSignals(), &WorkerSignals::captchaResult, this, &MainWindow::onCaptchaResult);
        QObject::connect(worker->getSignals(), &WorkerSignals::taskCompleted, this, &MainWindow::onTaskCompleted);
        QObject::connect(worker->getSignals(), &WorkerSignals::finished, this, &MainWindow::onWorkerFinished);
        QObject::connect(worker->getSignals(), &WorkerSignals::error, this, &MainWindow::handleWorkerError);

        m_activeThreads++;
        log(QString("🔧 Số luồng đang hoạt động tăng: %1 → %2").arg(m_activeThreads - 1).arg(m_activeThreads));
        updateStatus();
        QThreadPool::globalInstance()->start(worker);
    }
}

void MainWindow::onWorkerUpdate(int idx, const QString& text) {
    // Update result list with bounds checking
    if (!m_updateIndices.isEmpty()) {
        if (idx >= 0 && idx < m_updateIndices.size()) {
            int realIdx = m_updateIndices[idx];
            if (realIdx >= 0 && realIdx < m_outputLinesFull.size()) {
                m_outputLinesFull[realIdx] = text;
            } else {
                // Debug: Log bounds error
                log(QString("DEBUG: realIdx out of bounds: %1, size: %2").arg(realIdx).arg(m_outputLinesFull.size()));
            }
        } else {
            // Debug: Log bounds error
            log(QString("DEBUG: idx out of bounds: %1, updateIndices size: %2").arg(idx).arg(m_updateIndices.size()));
        }
    } else {
        if (idx >= 0 && idx < m_outputLines.size()) {
            m_outputLines[idx] = text;
        } else {
            // Debug: Log bounds error
            log(QString("DEBUG: idx out of bounds: %1, outputLines size: %2").arg(idx).arg(m_outputLines.size()));
        }
    }

    writeResultFile();
    log(text);
}

void MainWindow::onTaskCompleted(bool isOk) {
    // Only handle ok/fail counting here
    if (isOk) {
        m_ok++;
    } else {
        m_fail++;
    }
    // Don't call updateStatus here as onWorkerFinished will do it
}

void MainWindow::onCaptchaResult(bool isOk) {
    if (isOk) {
        m_captchaOk++;
    } else {
        m_captchaFail++;
    }
    updateStatus();
}

void MainWindow::onWorkerFinished() {
    m_activeThreads--;
    log(QString("🔧 Số luồng đang hoạt động giảm: %1 → %2").arg(m_activeThreads + 1).arg(m_activeThreads));
    m_done++;
    updateStatus();

    if (!m_isPaused) {
        processQueue(); // Get next task
    }

    if (m_done == m_total) {
        finishProcessing();
    }
}

void MainWindow::finishProcessing() {
    m_timer->stop();
    log("🎯 Hoàn thành xử lý tất cả MST");

    showKqDone();
    m_btnStart->setEnabled(true);
    m_btnRetry->setEnabled(true);
    m_btnPause->setEnabled(false);
    m_isPaused = false;

    // Only ask to retry if there are failures
    if (m_fail > 0) {
        QMessageBox::StandardButton reply = QMessageBox::question(this, "Hoàn thành",
            "🎉 Đã xong hết rồi!\nCó kết quả không lấy được địa chỉ.\n"
            "Bạn có muốn làm lại ngay từ file kết quả này không?",
            QMessageBox::Yes | QMessageBox::No, QMessageBox::Yes);

        if (reply == QMessageBox::Yes) {
            autoLamLaiFromLastResult();
        }
    } else {
        QMessageBox::StandardButton reply = QMessageBox::question(this, "Hoàn thành",
            "🎉 Đã xong hết rồi!\nBạn có muốn mở file kết quả không?",
            QMessageBox::Yes | QMessageBox::No, QMessageBox::Yes);

        if (reply == QMessageBox::Yes) {
            openResultFileDirectly();
        }
    }
}

void MainWindow::writeResultFile() {
    try {
        QStringList linesToWrite = m_outputLinesFull.isEmpty() ? m_outputLines : m_outputLinesFull;

        QFile file(m_ketquaFile);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            // setCodec removed in Qt6, UTF-8 is default
            out << linesToWrite.join("\n");
            file.close();
        }
    } catch (const std::exception& e) {
        log(QString("⚠ Lỗi ghi file kết quả: %1").arg(QString::fromStdString(e.what())));
    }
}

void MainWindow::openResultFileDirectly() {
    if (m_ketquaFile.isEmpty() || !QFile::exists(m_ketquaFile)) {
        QMessageBox::warning(this, "Thông báo", "Chưa có file kết quả hoặc file đã bị xóa.");
        return;
    }

    QString absolutePath = QFileInfo(m_ketquaFile).absoluteFilePath();
    if (QDesktopServices::openUrl(QUrl::fromLocalFile(absolutePath))) {
        log(QString("📄 Đã mở file kết quả: %1").arg(m_ketquaFile));
    } else {
        QMessageBox::warning(this, "Lỗi", "Không thể mở file kết quả.");
        log(QString("❌ Lỗi mở file kết quả: %1").arg(m_ketquaFile));
    }
}

void MainWindow::openResultFolder() {
    try {
        QString dirToOpen;

        // Check if running as executable or from source
        QString appPath = QCoreApplication::applicationFilePath();
        QFileInfo appInfo(appPath);

        if (appInfo.suffix().toLower() == "exe") {
            // Running as executable
            dirToOpen = QCoreApplication::applicationDirPath();
            log(QString("📁 Đã mở thư mục ứng dụng: %1").arg(dirToOpen));
        } else {
            // Running from source (development)
            dirToOpen = QDir::currentPath();
            log(QString("📁 Đã mở thư mục script: %1").arg(dirToOpen));
        }

        QDesktopServices::openUrl(QUrl::fromLocalFile(dirToOpen));
    } catch (const std::exception& e) {
        QMessageBox::critical(this, "Lỗi", QString("Không thể mở thư mục:\n%1").arg(QString::fromStdString(e.what())));
        log(QString("❌ Lỗi mở thư mục: %1").arg(QString::fromStdString(e.what())));
    }
}

void MainWindow::lamLai() {
    RetryDialog retryDialog(this);
    if (retryDialog.exec() != QDialog::Accepted) {
        return;
    }

    RetryDialog::RetryOptions options = retryDialog.getOptions();
    QString path = options.path;
    if (path.isEmpty() || !QFile::exists(path)) {
        QMessageBox::warning(this, "Lỗi", QString("File không tồn tại hoặc đường dẫn trống:\n%1").arg(path));
        return;
    }

    retryProcessing(path, options.retryPending, options.retryFailed);
}

void MainWindow::openDebugWindow() {
    DebugDialog debugDialog(m_debugFlags, this);
    if (debugDialog.exec() == QDialog::Accepted) {
        m_debugFlags = debugDialog.getNewFlags();
        QMessageBox::information(this, "Debug", "Chế độ debug đã được lưu cho lần tra cứu tiếp theo!");
    }
}

void MainWindow::retryProcessing(const QString& filePath, bool retryPending, bool retryFailed) {
    try {
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QMessageBox::critical(this, "Lỗi đọc file", QString("Không thể đọc file kết quả:\n%1").arg(file.errorString()));
            log(QString("⚠ Lỗi đọc file kết quả: %1").arg(file.errorString()));
            return;
        }

        QTextStream in(&file);
        // setCodec removed in Qt6, UTF-8 is default
        QStringList lines;
        while (!in.atEnd()) {
            lines.append(in.readLine());
        }
        file.close();

        m_outputLinesFull = lines;

        QStringList tasksToRetry;
        m_updateIndices.clear();

        for (int idx = 0; idx < m_outputLinesFull.size(); ++idx) {
            const QString& line = m_outputLinesFull[idx];
            bool shouldRetry = false;
            QString lineLower = line.toLower();

            if (retryPending && lineLower.contains("đang kiểm tra")) {
                shouldRetry = true;
            } else if (retryFailed && lineLower.contains("không lấy được địa chỉ")) {
                shouldRetry = true;
            }

            if (shouldRetry) {
                QStringList parts = line.trimmed().split('\t');
                if (parts.size() >= 2) {
                    QString mst = parts[1];
                    tasksToRetry.append(mst);
                    m_updateIndices.append(idx);
                }
            }
        }

        if (tasksToRetry.isEmpty()) {
            QMessageBox::information(this, "Thông báo", "Không có MST nào phù hợp trong file được chọn để tra lại.");
            return;
        }

        // Reset state & UI for the new run
        m_danhSach = tasksToRetry;
        m_ok = m_fail = m_done = m_captchaFail = m_captchaOk = 0;
        m_activeThreads = 0;
        m_total = m_danhSach.size();
        m_isPaused = false;

        // Create a new result file for the retry
        QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
        QFileInfo fileInfo(filePath);
        QString baseName = fileInfo.baseName();
        QString dirName = fileInfo.absolutePath();
        m_ketquaFile = QString("%1/%2_lamlai_%3.txt").arg(dirName, baseName, timestamp);

        // Write the full original content to the new file first
        writeResultFile();
        log(QString("🔁 Bắt đầu tra lại từ file: %1 (%2 MST)").arg(filePath).arg(m_total));

        m_startTime = QDateTime::currentMSecsSinceEpoch() / 1000;
        m_timer->start(1000);

        m_btnStart->setEnabled(false);
        m_btnRetry->setEnabled(false);
        m_btnPause->setEnabled(true);
        m_btnOpenResult->setEnabled(true);
        showKqLoading(true);

        // Reset output_lines for this retry run
        m_outputLines.clear();
        m_outputLines.resize(m_danhSach.size());
        m_taskQueue.clear();
        for (int i = 0; i < m_danhSach.size(); ++i) {
            m_taskQueue.append(qMakePair(i, m_danhSach[i]));
        }

        QThreadPool::globalInstance()->setMaxThreadCount(m_entryWorkers->text().toInt());
        processQueue();

    } catch (const std::exception& e) {
        QMessageBox::critical(this, "Lỗi", QString("Lỗi xử lý file: %1").arg(QString::fromStdString(e.what())));
    }
}

void MainWindow::autoLamLaiFromLastResult() {
    // Automatically retry failed and pending items from the last run
    retryProcessing(m_ketquaFile, true, true);
}

// DebugDialog Implementation
DebugDialog::DebugDialog(const DebugFlags& currentFlags, QWidget* parent)
    : QDialog(parent), m_newFlags(currentFlags) {

    setWindowTitle("Debug Tools");
    setFixedSize(400, 250);
    setModal(true);

    QVBoxLayout* layout = new QVBoxLayout(this);

    QLabel* infoLabel = new QLabel("Tính năng này dành cho việc kiểm tra lỗi, trong ngữ cảnh bình thường vui lòng không sử dụng");
    infoLabel->setStyleSheet("color: red;");
    infoLabel->setWordWrap(true);
    layout->addWidget(infoLabel);

    m_saveResponseCb = new QCheckBox("Lưu file trả về (raw HTML/json)");
    m_saveResponseCb->setChecked(m_newFlags.save_response);
    layout->addWidget(m_saveResponseCb);

    m_wrongCaptchaCb = new QCheckBox("Cố ý sai captcha");
    m_wrongCaptchaCb->setChecked(m_newFlags.wrong_captcha);
    layout->addWidget(m_wrongCaptchaCb);

    m_alwaysFailCb = new QCheckBox("Kết quả luôn là không tìm được địa chỉ");
    m_alwaysFailCb->setChecked(m_newFlags.always_fail);
    layout->addWidget(m_alwaysFailCb);

    QHBoxLayout* buttonLayout = new QHBoxLayout;
    QPushButton* saveButton = new QPushButton("Lưu");
    QPushButton* closeButton = new QPushButton("Đóng");

    buttonLayout->addStretch();
    buttonLayout->addWidget(saveButton);
    buttonLayout->addWidget(closeButton);
    buttonLayout->addStretch();

    layout->addLayout(buttonLayout);

    connect(saveButton, &QPushButton::clicked, this, &DebugDialog::saveAndClose);
    connect(closeButton, &QPushButton::clicked, this, &QDialog::reject);
}

void DebugDialog::saveAndClose() {
    m_newFlags.save_response = m_saveResponseCb->isChecked();
    m_newFlags.wrong_captcha = m_wrongCaptchaCb->isChecked();
    m_newFlags.always_fail = m_alwaysFailCb->isChecked();
    accept();
}

// RetryDialog Implementation
RetryDialog::RetryDialog(QWidget* parent)
    : QDialog(parent) {

    setWindowTitle("🔁 Làm lại từ kết quả cũ");
    setMinimumWidth(450);
    setModal(true);

    QVBoxLayout* layout = new QVBoxLayout(this);
    layout->addWidget(new QLabel("Chọn file kết quả cũ (.txt):"));

    QHBoxLayout* pathLayout = new QHBoxLayout;
    m_pathEdit = new QLineEdit;
    m_pathEdit->setPlaceholderText("Đường dẫn tới file kết quả...");
    pathLayout->addWidget(m_pathEdit);

    QPushButton* browseButton = new QPushButton("Chọn file");
    connect(browseButton, &QPushButton::clicked, this, &RetryDialog::browseFile);
    pathLayout->addWidget(browseButton);
    layout->addLayout(pathLayout);

    m_retryPendingCb = new QCheckBox("Tra cứu lại các kết quả 'Đang kiểm tra'");
    m_retryPendingCb->setChecked(true);
    layout->addWidget(m_retryPendingCb);

    m_retryFailedCb = new QCheckBox("Tra cứu lại các kết quả 'không lấy được địa chỉ'");
    m_retryFailedCb->setChecked(true);
    layout->addWidget(m_retryFailedCb);

    layout->addWidget(new QLabel("➡️ Chức năng này cho phép tra lại những dòng chưa có kết quả."));

    QHBoxLayout* buttonLayout = new QHBoxLayout;
    QPushButton* startButton = new QPushButton("🚀 Bắt đầu");
    QPushButton* cancelButton = new QPushButton("Hủy");
    buttonLayout->addStretch();
    buttonLayout->addWidget(startButton);
    buttonLayout->addWidget(cancelButton);
    layout->addLayout(buttonLayout);

    connect(startButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
}

void RetryDialog::browseFile() {
    QString filePath = QFileDialog::getOpenFileName(this, "Chọn file kết quả", "", "Text files (*.txt)");
    if (!filePath.isEmpty()) {
        m_pathEdit->setText(filePath);
    }
}

RetryDialog::RetryOptions RetryDialog::getOptions() const {
    RetryOptions options;
    options.path = m_pathEdit->text();
    options.retryPending = m_retryPendingCb->isChecked();
    options.retryFailed = m_retryFailedCb->isChecked();
    return options;
}

// Main function
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    // Increase font size for the entire application
    QFont font = app.font();
    font.setPointSize(font.pointSize() + 2);
    app.setFont(font);

    // Configure global thread pool
    QThreadPool::globalInstance()->setMaxThreadCount(default_workers);

    MainWindow window;
    window.resize(800, 600);
    window.show();

    return app.exec();
}

// MOC file will be generated and included automatically by Qt build system
