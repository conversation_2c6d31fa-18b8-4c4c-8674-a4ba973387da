#!/usr/bin/env python3
"""
Script để test model TrOCR đã được finetune
"""

import os
import torch
from transformers import VisionEncoderDecoderModel, TrOCRProcessor
from PIL import Image
import glob
import random
from pathlib import Path
import logging

# Thiết lập logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def predict_image(model, processor, image_path, device):
    """Dự đoán text từ ảnh"""
    # Đọc ảnh
    image = Image.open(image_path).convert('RGB')
    
    # Xử lý ảnh
    pixel_values = processor(image, return_tensors="pt").pixel_values.to(device)
    
    # Dự đoán
    with torch.no_grad():
        generated_ids = model.generate(pixel_values)
    
    # Decode kết quả
    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
    
    return generated_text

def test_model(model_path, test_data_path, num_samples=10):
    """Test model với một số ảnh ngẫu nhiên"""
    
    # Kiểm tra đường dẫn
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Không tìm thấy model tại {model_path}")
    if not os.path.exists(test_data_path):
        raise FileNotFoundError(f"Không tìm thấy dữ liệu test tại {test_data_path}")
    
    logger.info("Đang tải model...")
    
    # Tải model và processor
    model = VisionEncoderDecoderModel.from_pretrained(model_path)
    processor = TrOCRProcessor.from_pretrained(model_path)
    
    # Thiết lập device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Sử dụng device: {device}")
    model.to(device)
    model.eval()
    
    # Lấy danh sách ảnh
    image_paths = list(Path(test_data_path).glob("*.png"))
    
    if len(image_paths) == 0:
        logger.error("Không tìm thấy ảnh nào trong thư mục test")
        return
    
    # Chọn ngẫu nhiên một số ảnh để test
    test_images = random.sample(image_paths, min(num_samples, len(image_paths)))
    
    logger.info(f"Đang test với {len(test_images)} ảnh...")
    
    correct = 0
    total = len(test_images)
    
    for image_path in test_images:
        # Lấy ground truth từ tên file
        ground_truth = image_path.stem
        
        # Dự đoán
        try:
            predicted_text = predict_image(model, processor, image_path, device)
            
            # So sánh kết quả
            is_correct = predicted_text.strip().lower() == ground_truth.lower()
            if is_correct:
                correct += 1
            
            print(f"File: {image_path.name}")
            print(f"Ground Truth: {ground_truth}")
            print(f"Predicted: {predicted_text}")
            print(f"Correct: {'✓' if is_correct else '✗'}")
            print("-" * 50)
            
        except Exception as e:
            logger.error(f"Lỗi khi xử lý {image_path.name}: {e}")
    
    accuracy = correct / total * 100
    print(f"\nKết quả tổng quan:")
    print(f"Số ảnh test: {total}")
    print(f"Số dự đoán đúng: {correct}")
    print(f"Độ chính xác: {accuracy:.2f}%")

def predict_single_image(model_path, image_path):
    """Dự đoán cho một ảnh duy nhất"""
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Không tìm thấy model tại {model_path}")
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Không tìm thấy ảnh tại {image_path}")
    
    logger.info("Đang tải model...")
    
    # Tải model và processor
    model = VisionEncoderDecoderModel.from_pretrained(model_path)
    processor = TrOCRProcessor.from_pretrained(model_path)
    
    # Thiết lập device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    model.eval()
    
    # Dự đoán
    predicted_text = predict_image(model, processor, image_path, device)
    
    print(f"Ảnh: {image_path}")
    print(f"Dự đoán: {predicted_text}")
    
    return predicted_text

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Test TrOCR model")
    parser.add_argument("--model_path", default="finetuned_model", 
                       help="Đường dẫn đến model đã finetune")
    parser.add_argument("--test_data", default="train_data", 
                       help="Đường dẫn đến dữ liệu test")
    parser.add_argument("--num_samples", type=int, default=10,
                       help="Số ảnh để test")
    parser.add_argument("--single_image", type=str, default=None,
                       help="Đường dẫn đến ảnh duy nhất để test")
    
    args = parser.parse_args()
    
    if args.single_image:
        predict_single_image(args.model_path, args.single_image)
    else:
        test_model(args.model_path, args.test_data, args.num_samples)

if __name__ == "__main__":
    main()
