<Window x:Class="KiemTraMST.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Tra cứu thuế v5.0.13 (TNT)" Height="800" Width="1000"
        WindowStartupLocation="CenterScreen">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="150"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with MST count -->
        <DockPanel Grid.Row="0" Margin="0,0,0,10">
            <Label Content="📋 Mỗi dòng 1 MST (không chứa khoảng trắng/tab)" DockPanel.Dock="Left"/>
            <Label Name="MstCountLabel" Content="📊 Số MST trong bảng: 0" DockPanel.Dock="Right" FontWeight="Bold"/>
        </DockPanel>

        <!-- Input TextBox -->
        <TextBox Grid.Row="1" Name="InputTextBox"
                 AcceptsReturn="True"
                 VerticalScrollBarVisibility="Auto"
                 HorizontalScrollBarVisibility="Auto"
                 FontFamily="Consolas"
                 FontSize="12"
                 TextWrapping="NoWrap"
                 Margin="0,0,0,10"
                 TextChanged="InputTextBox_TextChanged"/>

        <!-- Workers setting -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
            <Label Content="Số luồng: (có thể thay đổi khi đang chạy)"/>
            <TextBox Name="WorkersTextBox" Text="20" Width="50" TextAlignment="Center" Margin="5,0"/>
            <Label Content="(thấy máy lag thì hạ xuống)"/>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
            <Button Name="StartButton" Content="🚀 Bắt đầu tra" Padding="10,5" Margin="5" FontWeight="Bold" Background="LightGreen"/>
            <Button Name="PauseButton" Content="⏸ Tạm dừng" Padding="10,5" Margin="5" IsEnabled="False"/>
            <Button Name="RetryButton" Content="🔁 Làm lại từ kết quả cũ" Padding="10,5" Margin="5"/>
            <Button Name="OpenResultButton" Content="📄 Mở file kết quả" Padding="10,5" Margin="5" IsEnabled="False"/>
            <Button Name="OpenFolderButton" Content="📁 Mở thư mục" Padding="10,5" Margin="5"/>
            <Button Name="DebugButton" Content="🐛 Debug" Padding="10,5" Margin="5"/>
        </StackPanel>

        <!-- Status line (single line like Python version) -->
        <Border Grid.Row="4" BorderBrush="Gray" BorderThickness="1" Padding="5" Margin="0,0,0,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Label Name="ModelStatusLabel" Content="⏳ Đang tải model..." Foreground="Orange" FontWeight="Bold"/>
                <Label Content=" | " Foreground="Gray"/>
                <Label Name="RunningStatusLabel" Content="Đang dừng" Foreground="Gray"/>
                <Label Content=" | " Foreground="Gray"/>
                <Label Name="TimeLabel" Content="⏱ 00:00:00"/>
                <Label Content=" | " Foreground="Gray"/>
                <Label Name="HardwareStatusLabel" Content="🔍 Đang detect..."
                       Foreground="Blue" FontWeight="Bold" Cursor="Hand"
                       MouseLeftButtonDown="HardwareStatusLabel_Click"/>
            </StackPanel>
        </Border>

        <!-- Progress -->
        <StackPanel Grid.Row="5" Margin="0,0,0,10">
            <DockPanel>
                <Label Name="ProgressLabel" Content="Sẵn sàng" HorizontalAlignment="Left"/>
                <Label Name="StatsLabel" Content="" HorizontalAlignment="Right"/>
            </DockPanel>
            <ProgressBar Name="ProgressBar" Height="20" Minimum="0" Maximum="100"/>
        </StackPanel>

        <!-- Output TextBox (like Python version) -->
        <TextBox Grid.Row="6" Name="OutputTextBox"
                 IsReadOnly="True"
                 VerticalScrollBarVisibility="Auto"
                 HorizontalScrollBarVisibility="Auto"
                 FontFamily="Consolas"
                 FontSize="10"
                 Background="Black"
                 Foreground="LightGreen"
                 TextWrapping="NoWrap"/>
    </Grid>
</Window>
