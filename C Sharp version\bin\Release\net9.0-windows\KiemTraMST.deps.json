{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"KiemTraMST/1.0.0": {"dependencies": {"Microsoft.ML.OnnxRuntime": "1.19.2", "Microsoft.ML.OnnxRuntime.Gpu": "1.19.2", "Newtonsoft.Json": "13.0.3", "RestSharp": "112.0.0", "System.Drawing.Common": "9.0.0", "System.Management": "9.0.0"}, "runtime": {"KiemTraMST.dll": {}}}, "Microsoft.ML.OnnxRuntime/1.19.2": {"dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.19.2"}, "runtimeTargets": {"runtimes/android/native/onnxruntime.aar": {"rid": "android", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios/native/onnxruntime.xcframework.zip": {"rid": "ios", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libonnxruntime.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libonnxruntime_providers_shared.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libonnxruntime.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libonnxruntime_providers_shared.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libonnxruntime.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libonnxruntime.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/onnxruntime.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.19.24.830"}, "runtimes/win-arm64/native/onnxruntime.lib": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.19.24.830"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.lib": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.19.24.830"}, "runtimes/win-x64/native/onnxruntime.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.19.24.830"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/onnxruntime.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.19.24.830"}, "runtimes/win-x86/native/onnxruntime.lib": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/onnxruntime_providers_shared.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.19.24.830"}, "runtimes/win-x86/native/onnxruntime_providers_shared.lib": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntime.Gpu/1.19.2": {"dependencies": {"Microsoft.ML.OnnxRuntime.Gpu.Linux": "1.19.2", "Microsoft.ML.OnnxRuntime.Gpu.Windows": "1.19.2", "Microsoft.ML.OnnxRuntime.Managed": "1.19.2"}}, "Microsoft.ML.OnnxRuntime.Gpu.Linux/1.19.2": {"dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.19.2"}, "runtimeTargets": {"runtimes/linux-x64/native/libonnxruntime_providers_cuda.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libonnxruntime_providers_tensorrt.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntime.Gpu.Windows/1.19.2": {"dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.19.2"}, "runtimeTargets": {"runtimes/win-x64/native/onnxruntime_providers_cuda.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime_providers_cuda.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime_providers_tensorrt.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime_providers_tensorrt.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.19.2": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/net6.0/Microsoft.ML.OnnxRuntime.dll": {"assemblyVersion": "1.19.2.0", "fileVersion": "1.19.2.0"}}}, "Microsoft.Win32.SystemEvents/9.0.0": {}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "RestSharp/112.0.0": {"runtime": {"lib/net8.0/RestSharp.dll": {"assemblyVersion": "112.0.0.0", "fileVersion": "112.0.0.0"}}}, "System.CodeDom/9.0.0": {}, "System.Drawing.Common/9.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.0"}}, "System.Management/9.0.0": {"dependencies": {"System.CodeDom": "9.0.0"}, "runtime": {"lib/net9.0/System.Management.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "System.Memory/4.5.5": {}}}, "libraries": {"KiemTraMST/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.ML.OnnxRuntime/1.19.2": {"type": "package", "serviceable": true, "sha512": "sha512-V1v8BebP1ZEFcZf2hGWuN7S5jAqmE2zBOq9RbGebZi2jd/aTZpg4PiTDtpQoGAIhZg4EPQUsHAyOJxwue44pBA==", "path": "microsoft.ml.onnxruntime/1.19.2", "hashPath": "microsoft.ml.onnxruntime.1.19.2.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime.Gpu/1.19.2": {"type": "package", "serviceable": true, "sha512": "sha512-lV5B4K/a7mBKi98sKWwxEzQxIfE/9GLMWPYDedqI5h21p1ZEbr4bQZ1RJFA/wuP5H5jTTaBz+gK65Pc+AOVc+A==", "path": "microsoft.ml.onnxruntime.gpu/1.19.2", "hashPath": "microsoft.ml.onnxruntime.gpu.1.19.2.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime.Gpu.Linux/1.19.2": {"type": "package", "serviceable": true, "sha512": "sha512-qlPkDLOepAuB+v9DZ8PvwlinSUhYJRLMDWhqHJXdmH8Ay4Tr4T7E4MgcU1eJpooo3JG3J3xVwCjQrFpS5/f53Q==", "path": "microsoft.ml.onnxruntime.gpu.linux/1.19.2", "hashPath": "microsoft.ml.onnxruntime.gpu.linux.1.19.2.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime.Gpu.Windows/1.19.2": {"type": "package", "serviceable": true, "sha512": "sha512-5rkB5/K1t2/aHFSTLmPrQtvRTDA8Lo6ghtR6W+nK+uwCKLd2LJgbyKGlFmz+0JIy1oqNtUN9eLmXx5/x8TGHjw==", "path": "microsoft.ml.onnxruntime.gpu.windows/1.19.2", "hashPath": "microsoft.ml.onnxruntime.gpu.windows.1.19.2.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime.Managed/1.19.2": {"type": "package", "serviceable": true, "sha512": "sha512-4oNu7/L1ar3PmXNvAnary0/MbyqNkKDmisrG54QVbFoSuW1GNw1CcWDBrlYHbafo41p4/cnVQ3lF/IHuSm8quQ==", "path": "microsoft.ml.onnxruntime.managed/1.19.2", "hashPath": "microsoft.ml.onnxruntime.managed.1.19.2.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-z8FfGIaoeALdD+KF44A2uP8PZIQQtDGiXsOLuN8nohbKhkyKt7zGaZb+fKiCxTuBqG22Q7myIAioSWaIcOOrOw==", "path": "microsoft.win32.systemevents/9.0.0", "hashPath": "microsoft.win32.systemevents.9.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "RestSharp/112.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-vezxJ6DLk0eDx+rmXwVHQvJ3elZCJfnLd8neRrkA+IvTs9falf2CEAolsjI1YKkkHTOvG13xWTnHK7gU0zgaaw==", "path": "restsharp/112.0.0", "hashPath": "restsharp.112.0.0.nupkg.sha512"}, "System.CodeDom/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oTE5IfuMoET8yaZP/vdvy9xO47guAv/rOhe4DODuFBN3ySprcQOlXqO3j+e/H/YpKKR5sglrxRaZ2HYOhNJrqA==", "path": "system.codedom/9.0.0", "hashPath": "system.codedom.9.0.0.nupkg.sha512"}, "System.Drawing.Common/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uoozjI3+dlgKh2onFJcz8aNLh6TRCPlLSh8Dbuljc8CdvqXrxHOVysJlrHvlsOCqceqGBR1wrMPxlnzzhynktw==", "path": "system.drawing.common/9.0.0", "hashPath": "system.drawing.common.9.0.0.nupkg.sha512"}, "System.Management/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bVh4xAMI5grY5GZoklKcMBLirhC8Lqzp63Ft3zXJacwGAlLyFdF4k0qz4pnKIlO6HyL2Z4zqmHm9UkzEo6FFsA==", "path": "system.management/9.0.0", "hashPath": "system.management.9.0.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}}}