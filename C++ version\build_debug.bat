@echo off
echo Building Kiem Tra MST Qt C++ Version in DEBUG mode...

REM Clean previous build
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release
if exist bin rmdir /s /q bin
if exist build rmdir /s /q build

REM Generate Makefile with qmake
echo Running qmake...
qmake kiem_tra_mst_qt.pro
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: qmake failed! Make sure Qt6 is installed and qmake is in PATH.
    pause
    exit /b 1
)

REM Build the project in DEBUG mode
echo Building project in DEBUG mode...
make debug
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

REM Copy resource files
echo Copying resource files...
if exist resource (
    if not exist bin\resource mkdir bin\resource
    xcopy /E /Y resource bin\resource\
)

if exist CaptchaData (
    if not exist bin\CaptchaData mkdir bin\CaptchaData
    xcopy /E /Y CaptchaData bin\CaptchaData\
)

if exist default.ico copy /Y default.ico bin\
if exist setting.ini copy /Y setting.ini bin\

echo.
echo ========================================
echo DEBUG Build completed successfully!
echo Executable: bin\kiem_tra_mst_qt.exe
echo ========================================
echo.
echo This debug build includes:
echo - Bounds checking with debug logging
echo - Better error messages
echo - Crash debugging information
echo.

REM Test if executable exists
if exist bin\kiem_tra_mst_qt.exe (
    echo Ready to run and debug!
) else (
    echo WARNING: Executable not found!
)

pause
