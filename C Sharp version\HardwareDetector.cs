using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Management;
using System.Threading;
using System.Threading.Tasks;

namespace KiemTraMST
{
    public class HardwareInfo
    {
        public required string Name { get; set; } = "";
        public string Memory { get; set; } = "";
        public string Vendor { get; set; } = "";
        public string Type { get; set; } = "";
        public string Driver { get; set; } = "";
        public string ComputeCapability { get; set; } = "";
        public int Index { get; set; }
    }

    public class DeviceInfo
    {
        public required string DeviceType { get; set; } = "";
        public required string DeviceName { get; set; } = "";
        public Dictionary<string, object> Properties { get; set; } = [];
    }

    public class HardwareDetector
    {
        private static readonly Lazy<HardwareDetector> _instance = new(() => new HardwareDetector());
        public static HardwareDetector Instance => _instance.Value;

        private readonly Dictionary<string, object> _detectionCache = [];
        private readonly object _lock = new();
        private DeviceInfo? _bestDeviceCache;

        private HardwareDetector() { }

        public async Task<Dictionary<string, string>> GetCpuInfoAsync()
        {
            if (_detectionCache.ContainsKey("cpu"))
                return (Dictionary<string, string>)_detectionCache["cpu"];

            var cpuInfo = new Dictionary<string, string>
            {
                ["name"] = Environment.ProcessorCount + " Core CPU",
                ["architecture"] = Environment.Is64BitOperatingSystem ? "x64" : "x86",
                ["cores"] = Environment.ProcessorCount.ToString()
            };

            try
            {
                await Task.Run(() =>
                {
                    using var searcher = new ManagementObjectSearcher("SELECT Name FROM Win32_Processor");
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        cpuInfo["name"] = obj["Name"]?.ToString() ?? cpuInfo["name"];
                        break;
                    }
                });
            }
            catch { }

            _detectionCache["cpu"] = cpuInfo;
            return cpuInfo;
        }

        public async Task<List<HardwareInfo>> GetNvidiaGpuInfoAsync()
        {
            if (_detectionCache.TryGetValue("nvidia", out var cached))
                return (List<HardwareInfo>)cached;

            var gpus = new List<HardwareInfo>();

            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "nvidia-smi",
                    Arguments = "--query-gpu=name,memory.total,driver_version,compute_cap --format=csv,noheader,nounits",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    await process.WaitForExitAsync();

                    if (process.ExitCode == 0)
                    {
                        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                        for (int i = 0; i < lines.Length; i++)
                        {
                            var parts = lines[i].Split(',', StringSplitOptions.TrimEntries);
                            if (parts.Length >= 4)
                            {
                                gpus.Add(new HardwareInfo
                                {
                                    Index = i,
                                    Name = parts[0],
                                    Memory = $"{parts[1]} MB",
                                    Driver = parts[2],
                                    ComputeCapability = parts[3],
                                    Vendor = "NVIDIA"
                                });
                            }
                        }
                    }
                }
            }
            catch { }

            _detectionCache["nvidia"] = gpus;
            return gpus;
        }

        public async Task<List<HardwareInfo>> GetIntelGpuInfoAsync()
        {
            if (_detectionCache.TryGetValue("intel", out var cached))
                return (List<HardwareInfo>)cached;

            var gpus = new List<HardwareInfo>();

            try
            {
                await Task.Run(() =>
                {
                    using var searcher = new ManagementObjectSearcher("SELECT Name, AdapterRAM FROM Win32_VideoController");
                    searcher.Options.Timeout = TimeSpan.FromSeconds(5); // Add timeout
                    int index = 0;
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var name = obj["Name"]?.ToString() ?? "";
                        if (name.Contains("Intel", StringComparison.OrdinalIgnoreCase) ||
                            name.Contains("UHD", StringComparison.OrdinalIgnoreCase) ||
                            name.Contains("Iris", StringComparison.OrdinalIgnoreCase) ||
                            name.Contains("HD Graphics", StringComparison.OrdinalIgnoreCase))
                        {
                            var memory = obj["AdapterRAM"]?.ToString() ?? "Unknown";
                            if (long.TryParse(memory, out var memoryBytes))
                            {
                                memory = (memoryBytes / (1024 * 1024)).ToString();
                            }

                            gpus.Add(new HardwareInfo
                            {
                                Index = index++,
                                Name = name,
                                Memory = memory,
                                Vendor = "Intel",
                                Type = "integrated"
                            });
                        }
                    }
                });
            }
            catch { }

            _detectionCache["intel"] = gpus;
            await Task.CompletedTask;
            return gpus;
        }

        public DeviceInfo DetermineBestDevice()
        {
            if (_bestDeviceCache != null)
                return _bestDeviceCache;

            lock (_lock)
            {
                if (_bestDeviceCache != null)
                    return _bestDeviceCache;

                // Check NVIDIA GPU first (highest priority)
                var nvidiaTask = GetNvidiaGpuInfoAsync();
                var nvidiaGpus = nvidiaTask.Result;

                if (nvidiaGpus.Any())
                {
                    var bestNvidia = nvidiaGpus
                        .Where(gpu => int.TryParse(gpu.Memory.Split(' ')[0], out _))
                        .OrderByDescending(gpu => int.Parse(gpu.Memory.Split(' ')[0]))
                        .FirstOrDefault();

                    if (bestNvidia != null)
                    {
                        _bestDeviceCache = new DeviceInfo
                        {
                            DeviceType = "cuda",
                            DeviceName = $"NVIDIA {bestNvidia.Name}",
                            Properties = new Dictionary<string, object>
                            {
                                ["memory"] = bestNvidia.Memory,
                                ["compute_capability"] = bestNvidia.ComputeCapability,
                                ["vendor"] = "NVIDIA"
                            }
                        };
                        return _bestDeviceCache;
                    }
                }

                // Fallback to CPU with Intel GPU info
                var cpuTask = GetCpuInfoAsync();
                var intelTask = GetIntelGpuInfoAsync();
                var cpuInfo = cpuTask.Result;
                var intelGpus = intelTask.Result;

                var properties = new Dictionary<string, object>(cpuInfo.ToDictionary(k => k.Key, v => (object)v.Value));

                if (intelGpus.Any())
                {
                    var intelGpu = intelGpus.First();
                    properties["intel_gpu"] = intelGpu.Name;
                    properties["gpu_type"] = "integrated";
                    
                    _bestDeviceCache = new DeviceInfo
                    {
                        DeviceType = "cpu",
                        DeviceName = $"CPU: {cpuInfo["name"]} + {intelGpu.Name}",
                        Properties = properties
                    };
                }
                else
                {
                    _bestDeviceCache = new DeviceInfo
                    {
                        DeviceType = "cpu",
                        DeviceName = $"CPU: {cpuInfo["name"]}",
                        Properties = properties
                    };
                }

                return _bestDeviceCache;
            }
        }

        public async Task<string> GetDeviceDisplayNameAsync()
        {
            var deviceInfo = DetermineBestDevice();
            await Task.CompletedTask;

            return deviceInfo.DeviceType switch
            {
                "cuda" => FormatNvidiaDisplay(deviceInfo),
                "cpu" => FormatCpuDisplay(deviceInfo),
                _ => $"🔧 {deviceInfo.DeviceName}"
            };
        }

        private string FormatNvidiaDisplay(DeviceInfo deviceInfo)
        {
            var name = deviceInfo.DeviceName.Replace("NVIDIA ", "");
            if (deviceInfo.Properties.TryGetValue("memory", out var memory))
            {
                var memoryStr = memory.ToString()?.Replace(" MB", "MB") ?? "";
                return $"🎮 {name} ({memoryStr})";
            }
            return $"🎮 {name}";
        }

        private string FormatCpuDisplay(DeviceInfo deviceInfo)
        {
            var cores = deviceInfo.Properties.TryGetValue("cores", out var coresObj) ? coresObj.ToString() : "Unknown";

            if (deviceInfo.Properties.TryGetValue("intel_gpu", out var intelGpuObj))
            {
                var intelGpuName = intelGpuObj.ToString() ?? "";
                if (intelGpuName.Contains("Intel"))
                {
                    intelGpuName = intelGpuName.Replace("Intel(R) ", "").Replace("Intel ", "");
                }
                return $"💻 CPU ({cores} cores) + 🔷 {intelGpuName}";
            }

            var cpuName = deviceInfo.Properties.TryGetValue("name", out var nameObj) ? nameObj.ToString() : "";
            if (cpuName?.Contains("Intel") == true)
            {
                return $"💻 Intel CPU ({cores} cores)";
            }
            else if (cpuName?.Contains("AMD") == true)
            {
                return $"💻 AMD CPU ({cores} cores)";
            }
            else
            {
                return $"💻 CPU ({cores} cores)";
            }
        }

        public void ClearCache()
        {
            lock (_lock)
            {
                _detectionCache.Clear();
                _bestDeviceCache = null;
            }
        }
    }
}
