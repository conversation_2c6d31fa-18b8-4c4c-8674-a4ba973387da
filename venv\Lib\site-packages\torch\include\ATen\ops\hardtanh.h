#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/hardtanh_ops.h>

namespace at {


// aten::hardtanh.out(Tensor self, Scalar min_val=-1, Scalar max_val=1, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & hardtanh_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & min_val=-1, const at::Scalar & max_val=1) {
    return at::_ops::hardtanh_out::call(self, min_val, max_val, out);
}
// aten::hardtanh.out(Tensor self, <PERSON>alar min_val=-1, Scalar max_val=1, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & hardtanh_outf(const at::Tensor & self, const at::Scalar & min_val, const at::Scalar & max_val, at::Tensor & out) {
    return at::_ops::hardtanh_out::call(self, min_val, max_val, out);
}

// aten::hardtanh(Tensor self, Scalar min_val=-1, Scalar max_val=1) -> Tensor
inline at::Tensor hardtanh(const at::Tensor & self, const at::Scalar & min_val=-1, const at::Scalar & max_val=1) {
    return at::_ops::hardtanh::call(self, min_val, max_val);
}

// aten::hardtanh_(Tensor(a!) self, Scalar min_val=-1, Scalar max_val=1) -> Tensor(a!)
inline at::Tensor & hardtanh_(at::Tensor & self, const at::Scalar & min_val=-1, const at::Scalar & max_val=1) {
    return at::_ops::hardtanh_::call(self, min_val, max_val);
}

}
