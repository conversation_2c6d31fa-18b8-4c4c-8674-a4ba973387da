# Để chạy file này, bạn cần cài đặt PySide6, requests và PySocks (cho proxy SOCKS):
# pip install PySide6 requests PySocks
#
# File này dùng để thu thập và lưu lại các captcha được giải đúng từ trang tracuunnt,
# phục vụ cho việc huấn luyện model OCR.

import sys, os, datetime, threading, re, json, configparser, traceback, concurrent.futures, time, requests, logging
from typing import Optional, List
import random
from PIL import Image

# ===================== HẰNG SỐ CHUỖI (MAGIC STRINGS) =====================
IP_API_URL = "http://ip-api.com/json"
CAPTCHA_PAGE_URL = "https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp"
CAPTCHA_IMAGE_URL = "https://tracuunnt.gdt.gov.vn/tcnnt/captcha.png"
CAPTCHA_WRONG_TEXTS = [
    "vui lòng nhập đúng mã xác nhận",
    "vui l&#242;ng nh&#7853;p &#273;&#250;ng m&#227; x&#225;c nh&#7853;n"
]
# --- Thêm import cho PySide6 ---
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QTextEdit, QLineEdit, QPushButton,
    QMessageBox, QFrame, QFileDialog, QComboBox, QStyle
)
from PySide6.QtCore import (
    Qt, QRunnable, QThreadPool, QObject, Signal, QTimer, QSize, Slot
)
from PySide6.QtGui import QFont, QMovie, QPixmap, QTransform, QIcon

# ==============================================================================
# PHẦN GHI LOG VÀ BẮT LỖI
# ==============================================================================

# Thiết lập ghi log ra file để debug các sự cố crash
# Ghi vào một file cố định, và xóa nội dung cũ mỗi khi mở lại (mode='w')
log_file = "captcha_harvester.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8', mode='w'),
        # logging.StreamHandler() # Bỏ comment nếu muốn xem log trên console
    ]
)

def global_exception_hook(exctype, value, tb):
    """
    Bắt tất cả các lỗi chưa được xử lý, ghi log và hiển thị hộp thoại.
    Điều này giúp chẩn đoán các sự cố crash ngẫu nhiên.
    """
    error_msg = "".join(traceback.format_exception(exctype, value, tb))
    logging.critical(f"LỖI CHƯA XỬ LÝ (UNHANDLED EXCEPTION):\n{error_msg}")
    
    # Chỉ hiển thị QMessageBox nếu QApplication đã được khởi tạo.
    # Nếu lỗi xảy ra trước khi app chạy, chúng ta không thể hiển thị cửa sổ đồ họa.
    if QApplication.instance():
        error_box = QMessageBox()
        error_box.setIcon(QMessageBox.Critical)
        error_box.setText("Ứng dụng gặp lỗi nghiêm trọng!")
        error_box.setInformativeText(
            f"Đã xảy ra một lỗi không mong muốn. Vui lòng gửi tệp log sau đây cho nhà phát triển để được hỗ trợ:\n\n"
            f"{os.path.abspath(log_file)}\n\n"
            "Ứng dụng có thể cần phải đóng."
        )
        error_box.setWindowTitle("Lỗi nghiêm trọng")
        error_box.setDetailedText(error_msg)
        error_box.setStandardButtons(QMessageBox.Ok)
        error_box.exec()
    else:
        # Nếu GUI chưa sẵn sàng, in ra stderr. File log vẫn được ghi.
        print(f"Lỗi nghiêm trọng xảy ra trước khi GUI khởi động. Chi tiết trong file: {os.path.abspath(log_file)}", file=sys.stderr)
        print(error_msg, file=sys.stderr)

    # Gọi lại hook mặc định để Python có thể thoát bình thường
    sys.__excepthook__(exctype, value, tb)

# Đặt hook bắt lỗi toàn cục ngay từ đầu
sys.excepthook = global_exception_hook

# ==============================================================================
# PHẦN CẤU HÌNH VÀ LOGIC BACKEND
# ==============================================================================

cfg = configparser.ConfigParser()
CONFIG_FILE = "captcha_harvester_qt.ini"
cfg.read(CONFIG_FILE, encoding="utf-8")
default_workers = cfg.getint("DEFAULT", "max_workers", fallback=10)
default_timeout = cfg.getint("DEFAULT", "request_timeout", fallback=120)
default_ocr_timeout = cfg.getint("DEFAULT", "ocr_timeout", fallback=120)
default_mst_to_generate = cfg.getint("DEFAULT", "mst_to_generate", fallback=100)
default_save_folder = cfg.get("DEFAULT", "save_folder", fallback="train_data")
default_max_retries = cfg.getint("DEFAULT", "max_retries_per_mst", fallback=20)
default_proxy_address = cfg.get("DEFAULT", "proxy_address", fallback="")
default_proxy_type = cfg.get("DEFAULT", "proxy_type", fallback="No Proxy")


def _create_proxy_dict(addr: str, ptype: str) -> Optional[dict]:
    """Tạo dictionary proxy từ địa chỉ và loại."""
    if not addr or ptype == "No Proxy":
        return None

    proxy_url = ""
    # Thêm http:// nếu người dùng quên
    if "://" not in addr:
        addr = "http://" + addr
        
    if ptype == "HTTP":
        proxy_url = addr
    elif ptype == "SOCKS4":
        # Cần PySocks
        proxy_url = addr.replace("http://", "socks4h://")
    elif ptype == "SOCKS5":
        # Cần PySocks
        proxy_url = addr.replace("http://", "socks5h://")
    
    if proxy_url:
        return {"http": proxy_url, "https": proxy_url}
    return None


# ======= BẮT ĐẦU PHẦN GỘP TỪ trocr_solver.py =======
# Đường dẫn thư mục chứa model đã tải sẵn
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
MODEL_DIR = os.path.join(BASE_DIR, "CaptchaData")

_model = None
_processor = None
_device = None
_model_lock = threading.Lock()
_model_loaded = False

def load_model(progress_callback=None):
    """
    Trì hoãn import mô hình để tránh cảnh báo CUDA hiện sớm.
    Có thể gọi nhiều lần, chỉ load 1 lần (thread-safe).
    """
    global _model, _processor, _device, _model_loaded
    if _model_loaded:
        if progress_callback: progress_callback(100)
        return
    with _model_lock:
        if _model_loaded:
            if progress_callback: progress_callback(100)
            return
        if progress_callback: progress_callback(5)
        import torch
        from transformers import VisionEncoderDecoderModel, TrOCRProcessor
        _device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        if progress_callback: progress_callback(15)
        _processor = TrOCRProcessor.from_pretrained(MODEL_DIR)
        if progress_callback: progress_callback(60)
        _model = VisionEncoderDecoderModel.from_pretrained(MODEL_DIR)
        if progress_callback: progress_callback(90)
        _model.to(_device)
        _model_loaded = True
        if progress_callback: progress_callback(100)

def preprocess_image(image_path_or_buffer):
    image = Image.open(image_path_or_buffer).convert("RGBA")
    background = Image.new("RGBA", image.size, (255, 255, 255, 255))
    combined = Image.alpha_composite(background, image).convert("RGB")
    return combined

def solve_captcha(image_path_or_buffer):
    global _model, _processor, _device, _model_loaded
    if not _model_loaded or _model is None or _processor is None:
        load_model()
    try:
        import torch
        img = preprocess_image(image_path_or_buffer)
        encoding = _processor(images=img, return_tensors="pt").to(_device)
        generated_ids = _model.generate(encoding.pixel_values, max_length=20)
        text = _processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
        return text.strip()
    except Exception as e:
        return f"❌ Lỗi OCR: {e}"
# ======= KẾT THÚC PHẦN GỘP TỪ trocr_solver.py =======


# ==============================================================================
# PHẦN GIAO DIỆN (UI) VÀ LOGIC TƯƠNG TÁC VỚI QT
# ==============================================================================

class WorkerSignals(QObject):
    """
    Định nghĩa các tín hiệu cho worker thu thập captcha.
    """
    log = Signal(str)
    captcha_saved = Signal(str) # Gửi tên file đã lưu
    captcha_failed = Signal()   # Báo captcha sai, khi worker đã hết lần thử cho 1 MST
    attempt_made = Signal(bool) # Báo kết quả của 1 lần thử, True=đúng, False=sai
    finished = Signal()         # Báo worker đã xong
    error = Signal(str)         # Báo lỗi nghiêm trọng
    captcha_duplicated = Signal(str) # Báo captcha đã tồn tại

class CaptchaHarvestWorker(QRunnable):
    """
    Worker class để lấy captcha và lưu lại nếu giải đúng.
    """
    def __init__(self, mst: str, save_folder: str, max_retries: int, proxy_config: Optional[dict], ip_info: str, parent: QObject = None):
        super().__init__()
        self.mst = mst
        self.signals = WorkerSignals(parent)
        self.session = None
        self.save_folder = save_folder
        self.max_retries = max_retries
        self.proxy_config = proxy_config
        self.ip_info = ip_info

    @Slot()
    def run(self):
        self.session = requests.Session()
        if self.proxy_config:
            self.session.proxies = self.proxy_config
            proxy_display = self.proxy_config.get('http') or self.proxy_config.get('https')
            log_msg = f"[{self.mst}] 🕵️ Sử dụng proxy: {proxy_display}"
            if self.ip_info:
                log_msg += f" => {self.ip_info}"
            self.signals.log.emit(log_msg)
        
        self.signals.log.emit(f"[{self.mst}] 🚀 Bắt đầu lấy captcha")
        try:
            for attempt in range(self.max_retries):
                # Đếm tổng số lần thử (bao gồm cả retry)
                if hasattr(self.signals, 'main_window') and self.signals.main_window:
                    self.signals.main_window.total_attempts += 1
                    self.signals.main_window.update_status()
                try:
                    # harvest_captcha_attempt sẽ return True nếu thành công và không cần thử lại
                    if self.harvest_captcha_attempt():
                        return 
                except Exception as e:
                    self.signals.log.emit(f"[{self.mst}] ⚠️ Lỗi ở lần thử {attempt + 1}/{self.max_retries}: {e}")
                    if attempt + 1 == self.max_retries:
                        raise Exception(f"Thất bại sau {self.max_retries} lần thử.")
                    time.sleep(1) # Chờ 1 giây trước khi thử lại
            
            self.signals.log.emit(f"[{self.mst}] ❌ Không thể lấy captcha sau {self.max_retries} lần thử.")
            self.signals.captcha_failed.emit()

        except Exception as e:
            self.signals.error.emit(f"Lỗi worker [{self.mst}]: {e}\n{traceback.format_exc()}")
        finally:
            self.session.close()
            self.signals.finished.emit()

    def harvest_captcha_attempt(self) -> bool:
        """Thực hiện một lần thử lấy và giải captcha. Trả về True nếu thành công, False nếu sai captcha."""
        headers = {
            'User-Agent': 'Mozilla/5.0',
            'Referer': CAPTCHA_PAGE_URL
        }

        try:
            self.session.get(CAPTCHA_PAGE_URL, headers=headers, timeout=default_timeout)
        except requests.RequestException as e:
            raise Exception(f"Lỗi kết nối: {e}")

        try:
            r = self.session.get(CAPTCHA_IMAGE_URL, headers=headers, timeout=default_timeout)
            r.raise_for_status()
            import io
            image_buffer = io.BytesIO(r.content)
        except requests.RequestException as e:
            raise Exception(f"Lỗi tải captcha: {e}")

        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(solve_captcha, image_buffer)
                code = future.result(timeout=default_ocr_timeout)
                if not code or "❌" in code:
                    raise Exception("Lỗi OCR captcha")
        except concurrent.futures.TimeoutError:
            raise Exception(f"OCR captcha timeout sau {default_ocr_timeout} giây")
        except Exception as e:
            raise Exception(f"Lỗi giải captcha: {e}")

        data = {"mst": self.mst, "fullname": "", "address": "", "captcha": code}
        self.signals.log.emit(f"[{self.mst}] 📤 Gửi request với captcha: {code}")

        try:
            res = self.session.post("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp", data=data, headers=headers, timeout=default_timeout)
            res.raise_for_status()
        except requests.RequestException as e:
            raise Exception(f"Lỗi gửi request: {e}")

        html = res.text
        if any(x in html.lower() for x in CAPTCHA_WRONG_TEXTS):
            self.signals.attempt_made.emit(False)
            self.signals.log.emit(f"[{self.mst}] ❌ Captcha sai ({code}), sẽ thử lại...")
            return False # Báo hiệu captcha sai để vòng lặp bên ngoài biết và retry

        # Captcha đúng! Tiến hành lưu.
        self.signals.attempt_made.emit(True)
        self.signals.log.emit(f"[{self.mst}] ✅ Captcha ĐÚNG! ({code})")
        try:
            os.makedirs(self.save_folder, exist_ok=True)
            safe_code = re.sub(r'[\\/*?:"<>|]', "", code)
            if not safe_code:
                self.signals.log.emit(f"[{self.mst}] ⚠️ Captcha '{code}' chứa ký tự không hợp lệ, bỏ qua.")
                self.signals.captcha_failed.emit()
                return True # Đã xử lý xong, không retry nữa

            filename = f"{safe_code}.png"
            filepath = os.path.join(self.save_folder, filename)

            if os.path.exists(filepath):
                self.signals.log.emit(f"[{self.mst}] ⚠️ File '{filename}' đã tồn tại, captcha bị trùng, bỏ qua.")
                if hasattr(self.signals, 'captcha_duplicated'):
                    self.signals.captcha_duplicated.emit(filename)
                self.signals.captcha_failed.emit()
                return True # Đã xử lý xong, không retry nữa

            # Lưu ảnh captcha đúng nguyên bản (không chuyển nền trắng)
            with open(filepath, "wb") as f:
                f.write(image_buffer.getvalue())

            self.signals.captcha_saved.emit(filename)
        except Exception as e:
            self.signals.error.emit(f"[{self.mst}] ❌ Lỗi khi lưu file captcha: {e}")
            self.signals.captcha_failed.emit()
        
        return True # Đã xử lý xong, không retry nữa

class CountryCheckSignals(QObject):
    finished = Signal(dict, str) # api_data, error_string

class CountryCheckWorker(QRunnable):
    def __init__(self, proxy_config: Optional[dict], parent: QObject = None):
        super().__init__()
        self.signals = CountryCheckSignals(parent)
        self.proxy_config = proxy_config

    @Slot()
    def run(self):
        api_data, error = {}, ""
        try:
            res = requests.get(IP_API_URL, timeout=15, proxies=self.proxy_config)
            res.raise_for_status()
            api_data = res.json()
            if api_data.get('status') != 'success':
                error = f"Yêu cầu API thất bại: {api_data.get('message', 'Unknown')}"
                api_data = {}
        except Exception as e:
            error = str(e)
        self.signals.finished.emit(api_data, error)

class EmojiAnimationLabel(QLabel):
    """QLabel hiển thị một chuỗi emoji động."""
    def __init__(self, emojis, delay=200, size=32, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.emojis = emojis
        self.idx = 0
        self.setFont(QFont("Segoe UI Emoji", size // 2))
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.animate)
        self.timer.start(delay)
        self.animate()

    def animate(self):
        self.setText(self.emojis[self.idx])
        self.idx = (self.idx + 1) % len(self.emojis)

class FlippingGIFLabel(QLabel):
    """QLabel hiển thị GIF động có thể lật qua lại."""
    def __init__(self, gif_path, flip_interval=2000, size=(32, 32), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.movie = QMovie(gif_path)
        self.movie.setScaledSize(QSize(*size))
        self.movie.frameChanged.connect(self.on_frame_changed)
        self.flipper = QTimer(self)
        self.flipper.timeout.connect(self.toggle_flip)
        self.flipper.start(flip_interval)
        self.is_flipped = False
        self.movie.start()

    def toggle_flip(self):
        self.is_flipped = not self.is_flipped
        self.on_frame_changed()

    @Slot(int)
    def on_frame_changed(self, frame_number=-1):
        pixmap = self.movie.currentPixmap()
        if self.is_flipped:
            pixmap = pixmap.transformed(QTransform().scale(-1, 1))
        self.setPixmap(pixmap)

    def destroyEvent(self, event):
        if self.movie: self.movie.stop()
        if self.flipper: self.flipper.stop()
        super().destroyEvent(event)

class MainWindow(QMainWindow):
    MST_COUNT_PREFIX = "📊 Số MST trong bảng: "
    VERSION = "1.2.5"

    def __init__(self):
        super().__init__()
        self.setWindowTitle(f'Công cụ Thu thập Captcha v{self.VERSION}')
        
        # Đặt icon mũi tên tải xuống, tượng trưng cho việc thu thập/harvesting
        self.setWindowIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_TitleBarMenuButton))

        self.setup_state()
        self.setup_ui()
        self.setup_connections()
        self.model_loaded = False
        self.pending_start = False
        self._last_model_percent = 0  # Thêm biến lưu % tải model
        self.load_model_bg()
        self.check_country()
        self.check_setting_file()
        self.update_mst_count()

    def setup_state(self):
        self.danh_sach: List[str] = []
        self.start_time: Optional[float] = None
        self.active_threads = 0
        self.saved_count = self.done = 0
        self.duplicated_count = 0  # Đếm captcha trùng
        self.total = 0
        self.total_attempts = 0
        self.save_folder = default_save_folder
        self.max_retries = default_max_retries
        self.proxy_address = default_proxy_address
        self.proxy_type = default_proxy_type
        self.is_vietnam_ip = False
        self.ip_info_str = ""

    def setup_ui(self):
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        top_layout = QHBoxLayout()
        top_layout.addWidget(QLabel("📋 Mỗi dòng 1 MST (để lấy captcha)"))
        top_layout.addStretch()
        self.mst_count_label = QLabel(f"{self.MST_COUNT_PREFIX}0")
        top_layout.addWidget(self.mst_count_label)
        self.main_layout.addLayout(top_layout)

        self.text_box = QTextEdit()
        self.text_box.setLineWrapMode(QTextEdit.NoWrap)
        self.text_box.setPlaceholderText("Dán danh sách MST vào đây hoặc tạo ngẫu nhiên...")
        self.text_box.setMinimumHeight(150)
        self.main_layout.addWidget(self.text_box)

        # Hàng chọn thư mục
        folder_layout = QHBoxLayout()
        folder_layout.addWidget(QLabel("📂 Thư mục lưu captcha:"))
        self.entry_save_folder = QLineEdit(self.save_folder)
        self.entry_save_folder.setReadOnly(True)
        folder_layout.addWidget(self.entry_save_folder)
        self.btn_select_folder = QPushButton("Chọn...")
        folder_layout.addWidget(self.btn_select_folder)
        self.main_layout.addLayout(folder_layout)

        # Hàng cài đặt
        settings_layout = QHBoxLayout()
        settings_layout.addStretch()
        
        settings_layout.addWidget(QLabel("Số lượng tạo:"))
        self.entry_mst_count = QLineEdit(str(default_mst_to_generate))
        self.entry_mst_count.setFixedWidth(80)
        settings_layout.addWidget(self.entry_mst_count)
        
        settings_layout.addWidget(QLabel("Số luồng:"))
        self.entry_workers = QLineEdit(str(default_workers))
        self.entry_workers.setFixedWidth(50)
        settings_layout.addWidget(self.entry_workers)

        # Bỏ setting số lần thử lại khỏi GUI, chỉ cho phép sửa trong file INI
        # settings_layout.addWidget(QLabel("Số lần thử lại/MST:"))
        # self.entry_max_retries = QLineEdit(str(self.max_retries))
        # self.entry_max_retries.setFixedWidth(50)
        # settings_layout.addWidget(self.entry_max_retries)
        
        settings_layout.addStretch()
        self.main_layout.addLayout(settings_layout)

        # Hàng cài đặt proxy
        proxy_layout = QHBoxLayout()
        proxy_layout.addStretch()
        proxy_layout.addWidget(QLabel("🕵️ Proxy (ip:port hoặc user:pass@ip:port):"))
        self.entry_proxy_address = QLineEdit(self.proxy_address)
        self.entry_proxy_address.setFixedWidth(250)
        proxy_layout.addWidget(self.entry_proxy_address)
        proxy_layout.addWidget(QLabel("Loại:"))
        self.combo_proxy_type = QComboBox()
        self.combo_proxy_type.addItems(["No Proxy", "HTTP", "SOCKS4", "SOCKS5"])
        self.combo_proxy_type.setCurrentText(self.proxy_type)
        proxy_layout.addWidget(self.combo_proxy_type)
        proxy_layout.addStretch()
        self.main_layout.addLayout(proxy_layout)


        btn_layout = QHBoxLayout()
        self.btn_start = QPushButton("🚀 Bắt đầu lấy captcha đúng")
        self.btn_generate_mst = QPushButton("Tạo MST ngẫu nhiên")
        self.btn_open_folder = QPushButton("📁 Mở thư mục captcha")
        self.btn_exit = QPushButton("❌ Thoát")
        
        btn_layout.addWidget(self.btn_start)
        btn_layout.addWidget(self.btn_generate_mst)
        btn_layout.addWidget(self.btn_open_folder)
        btn_layout.addWidget(self.btn_exit)
        self.main_layout.addLayout(btn_layout)

        status_frame = QFrame()
        status_frame.setFrameShape(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)

        self.kq_frame = QHBoxLayout()
        self.label_kq = QLabel("")
        self.kq_frame.addWidget(self.label_kq)
        self.kq_widget = None 

        self.time_label = QLabel("⏱ Thời gian: 00:00:00")

        status_layout.addStretch()
        status_layout.addLayout(self.kq_frame)
        status_layout.addWidget(self.time_label)
        status_layout.addStretch()
        self.main_layout.addWidget(status_frame)

        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.main_layout.addWidget(self.status_label)

        self.model_status_label = QLabel("Đang tải model...")
        self.model_status_label.setAlignment(Qt.AlignCenter)
        self.model_status_label.setStyleSheet("color: orange;")
        self.main_layout.addWidget(self.model_status_label)

        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        self.main_layout.addWidget(self.log_box)

        self.timer = QTimer(self)
        self.update_status()

    def setup_connections(self):
        self.text_box.textChanged.connect(self.update_mst_count)
        self.entry_workers.editingFinished.connect(self.on_workers_change)
        self.entry_mst_count.editingFinished.connect(self.save_settings)
        # self.entry_max_retries.editingFinished.connect(self.on_retries_change) # Đã được loại bỏ
        self.entry_proxy_address.editingFinished.connect(self.save_settings)
        self.combo_proxy_type.currentTextChanged.connect(self.save_settings)
        
        self.btn_start.clicked.connect(self.start_harvesting)
        self.btn_generate_mst.clicked.connect(self.generate_random_msts)
        self.btn_select_folder.clicked.connect(self.select_save_folder)
        self.btn_open_folder.clicked.connect(self.open_captcha_folder)
        self.btn_exit.clicked.connect(self.close)

        self.timer.timeout.connect(self.update_time_display)

    def closeEvent(self, event):
            self.save_settings()
            event.accept()

    @Slot()
    def log(self, msg: str):
        self.log_box.append(msg)
        logging.info(msg.strip())
    
    @Slot()
    def update_mst_count(self):
        content = self.text_box.toPlainText().strip()
        lines = [line.strip() for line in content.splitlines() if line.strip()] if content else []
        self.mst_count_label.setText(f"{self.MST_COUNT_PREFIX}{len(lines)}")

    @Slot()
    def update_status(self):
        max_workers = QThreadPool.globalInstance().maxThreadCount()
        
        percent_str = ""
        if self.total_attempts > 0:
            percentage = (self.saved_count / self.total_attempts) * 100
            percent_str = f" ({percentage:.1f}%)"
        # Hiển thị thêm số captcha trùng
        status_text = (f"🧩 Captcha đúng: {self.saved_count}/{self.total_attempts}{percent_str}  "
                       f"⚠️ Trùng: {self.duplicated_count}   "
                       f"⚙️ Tiến độ: {self.done}/{self.total}   "
                       f"👨‍👩‍👦‍👦 Luồng: {self.active_threads}/{max_workers}")
        self.status_label.setText(status_text)
        
    @Slot()
    def update_time_display(self):
        if self.start_time:
            elapsed = int(time.time() - self.start_time)
            hours, remainder = divmod(elapsed, 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.setText(f"⏱ {hours:02d}:{minutes:02d}:{seconds:02d}")

    def load_model_bg(self):
        self.btn_start.setEnabled(True)  # Luôn cho phép nhấn nút Bắt đầu
        self.model_status_label.setText("Đang tải model...")
        self.model_status_label.setStyleSheet("color: orange;")

        class ModelLoader(QRunnable):
            def __init__(self, signals):
                super().__init__()
                self.signals = signals
                self._aborted = False

            def _progress_handler(self, percent):
                """
                Hàm bao bọc an toàn. Nó sẽ chủ động ngắt tiến trình tải model
                nếu cửa sổ chính bị đóng trong lúc đang tải.
                """
                if self._aborted:
                    raise InterruptedError("Model loading aborted.")

                try:
                    # Cố gắng gửi tín hiệu đi
                    self.signals.progress.emit(percent)
                except RuntimeError:
                    # Lỗi này xảy ra khi đối tượng nhận tín hiệu (cửa sổ) đã bị hủy.
                    # Đánh dấu là đã hủy và tung ra ngoại lệ để dừng hàm load_model.
                    logging.warning("Tín hiệu bị hủy khi đang tải model (cửa sổ đã đóng). Hủy tiến trình.")
                    self._aborted = True
                    raise InterruptedError("Model loading aborted due to window closure.")

            def run(self):
                try:
                    load_model(progress_callback=self._progress_handler)
                    
                    # Nếu không bị ngắt, thử gửi tín hiệu 'finished'.
                    # Vẫn cần try-except ở đây phòng trường hợp cửa sổ đóng
                    # ngay sau khi load_model xong và trước khi dòng này được gọi.
                    if not self._aborted:
                        try:
                            self.signals.finished.emit()
                        except RuntimeError:
                            logging.warning("Cửa sổ đóng ngay khi model tải xong.")

                except InterruptedError as e:
                    # Đây là một lối thoát an toàn, chỉ cần ghi log.
                    logging.info(f"Tiến trình tải model đã được hủy một cách an toàn: {e}")
                except Exception as e:
                    # Đối với các lỗi không mong muốn khác, hãy thử gửi tín hiệu lỗi.
                    logging.error(f"Lỗi không mong muốn khi tải model: {e}", exc_info=True)
                    if not self._aborted:
                        try:
                            self.signals.error.emit(str(e))
                        except RuntimeError:
                            logging.error("Không thể gửi tín hiệu lỗi (cửa sổ đã đóng).")
        
        class ModelSignals(QObject):
            progress = Signal(int)
            finished = Signal()
            error = Signal(str)
            
        self.model_signals = ModelSignals(self)
        self.model_signals.progress.connect(self.on_model_progress)
        self.model_signals.finished.connect(self.on_model_loaded)
        self.model_signals.error.connect(self.on_model_load_error)
        
        QThreadPool.globalInstance().start(ModelLoader(self.model_signals))

    @Slot(int)
    def on_model_progress(self, percent):
        self._last_model_percent = percent
        if self.pending_start:
            self.model_status_label.setText(f"Đang tải model... Sẽ tự động bắt đầu khi model sẵn sàng. {percent}%")
        else:
            self.model_status_label.setText(f"Đang tải model... {percent}%")

    @Slot()
    def on_model_loaded(self):
        self.model_status_label.setText("Model đã sẵn sàng")
        self.model_status_label.setStyleSheet("color: green;")
        self.btn_start.setEnabled(True)
        self.model_loaded = True
        if self.pending_start:
            self.pending_start = False
            self.start_harvesting()

    @Slot(str)
    def on_model_load_error(self, error_msg):
        self.model_status_label.setText(f"Lỗi tải model: {error_msg}")
        self.model_status_label.setStyleSheet("color: red;")
        QMessageBox.critical(self, "Lỗi nghiêm trọng", f"Không thể tải model TrOCR:\n{error_msg}\nChương trình sẽ thoát.")
        self.close()

    def check_country(self):
        self.log("Kiểm tra vị trí IP...")
        proxy_config = _create_proxy_dict(default_proxy_address, default_proxy_type)
        if proxy_config:
            self.log(f"Sử dụng proxy đã lưu để kiểm tra IP: {proxy_config.get('http')}")
        worker = CountryCheckWorker(proxy_config, self)
        worker.signals.finished.connect(self.on_country_checked)
        QThreadPool.globalInstance().start(worker)

    @Slot(dict, str)
    def on_country_checked(self, data, error):
        if error:
            self.log(f"⚠️ Không thể kiểm tra vị trí IP: {error}. Vui lòng tự đảm bảo bạn không ở Việt Nam.")
            QMessageBox.warning(self, "Lỗi kiểm tra IP", f"Không thể xác định vị trí IP của bạn: {error}\n\nỨng dụng sẽ tiếp tục, nhưng hãy chắc chắn bạn đang dùng Proxy nếu ở Việt Nam.")
            return

        ip = data.get('query', 'N/A')
        country = data.get('country', 'N/A')
        country_code = data.get('countryCode', '')
        isp = data.get('isp', 'N/A')

        self.ip_info_str = f"{ip} ({country}) - ISP: {isp}"
        self.log(f"🌍 IP hiện tại: {self.ip_info_str}")
        
        if country_code == 'VN':
            self.is_vietnam_ip = True
            QMessageBox.warning(self, "Cảnh báo truy cập", f"Phát hiện IP Việt Nam ({ip}).\n\nBạn sẽ không thể thực hiện các thao tác truy cập web thuế nếu không cấu hình proxy.")

    def check_setting_file(self):
        if os.path.exists(CONFIG_FILE):
            try:
                # Cập nhật UI từ file config
                self.entry_workers.setText(str(default_workers))
                self.entry_mst_count.setText(str(default_mst_to_generate))
                self.save_folder = default_save_folder
                self.entry_save_folder.setText(self.save_folder)
                self.max_retries = default_max_retries
                # self.entry_max_retries.setText(str(self.max_retries)) # Bỏ cập nhật UI
                self.proxy_address = default_proxy_address
                self.entry_proxy_address.setText(self.proxy_address)
                self.proxy_type = default_proxy_type
                self.combo_proxy_type.setCurrentText(self.proxy_type)
                self.log(f"✅ Đã tải cấu hình từ {CONFIG_FILE}")
            except Exception as e:
                self.log(f"⚠️ Lỗi đọc {CONFIG_FILE}: {e}. Dùng giá trị mặc định.")
        else:
            self.log(f"❌ Không tìm thấy {CONFIG_FILE}. Tạo file mới với cấu hình mặc định.")
            self.create_default_setting_file()

    def create_default_setting_file(self):
        try:
            default_cfg = configparser.ConfigParser()
            default_cfg['DEFAULT'] = {
                'max_workers': '10',
                'request_timeout': '120',
                'ocr_timeout': '120',
                'mst_to_generate': '100',
                'save_folder': 'train_data',
                'max_retries_per_mst': '20',
                'proxy_address': '',
                'proxy_type': 'No Proxy'
            }
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                default_cfg.write(f)
            self.log(f"✅ Đã tạo file {CONFIG_FILE} mặc định.")
        except Exception as e:
            self.log(f"❌ Không thể tạo file {CONFIG_FILE}: {e}")

    def save_settings(self):
        try:
            cfg['DEFAULT']['max_workers'] = self.entry_workers.text()
            cfg['DEFAULT']['mst_to_generate'] = self.entry_mst_count.text()
            cfg['DEFAULT']['save_folder'] = self.save_folder
            # cfg['DEFAULT']['max_retries_per_mst'] = self.entry_max_retries.text() # Bỏ lưu, chỉ đọc từ file
            cfg['DEFAULT']['proxy_address'] = self.entry_proxy_address.text().strip()
            cfg['DEFAULT']['proxy_type'] = self.combo_proxy_type.currentText()
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                cfg.write(f)
            self.log(f"💾 Đã lưu cấu hình vào {CONFIG_FILE}")
        except Exception as e:
            self.log(f"❌ Lỗi lưu cấu hình: {e}")

    @Slot()
    def on_workers_change(self):
        value = self.entry_workers.text().strip()
        if value.isdigit() and int(value) > 0:
            new_workers = int(value)
            reply = QMessageBox.question(self, 'Xác nhận', f'Bạn có chắc muốn đổi số luồng thành {new_workers}?', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                QThreadPool.globalInstance().setMaxThreadCount(new_workers)
                self.log(f"🔧 Đã cập nhật số luồng: {new_workers}")
                self.update_status()
                self.save_settings()
        else:
            self.entry_workers.setText(str(QThreadPool.globalInstance().maxThreadCount()))

    # Bỏ phương thức này vì không còn UI để chỉnh
    # @Slot()
    # def on_retries_change(self):
    #     value = self.entry_max_retries.text().strip()
    #     if value.isdigit() and int(value) >= 0:
    #         self.max_retries = int(value)
    #         self.log(f"🔧 Đã cập nhật số lần thử lại: {self.max_retries}")
    #         self.save_settings()
    #     else:
    #         self.entry_max_retries.setText(str(self.max_retries))

    def get_proxy_config(self) -> Optional[dict]:
        addr = self.entry_proxy_address.text().strip()
        ptype = self.combo_proxy_type.currentText()
        return _create_proxy_dict(addr, ptype)

    def show_kq_loading(self, show=True):
        if show:
            if self.kq_widget: return
            gif_path = "resource/working.gif"
            if os.path.exists(gif_path):
                 self.kq_widget = FlippingGIFLabel(gif_path, size=(28,28), parent=self)
            else:
                self.kq_widget = EmojiAnimationLabel([" S"," So"," Sol"," Solv"," Solvi"," Solvin"," Solving"," Solving "], delay=150, size=28, parent=self)
            self.kq_frame.insertWidget(0, self.kq_widget)
            self.label_kq.setText(" Đang lấy captcha...")
        else:
            if self.kq_widget:
                self.kq_widget.deleteLater()
                self.kq_widget = None
            self.label_kq.setText("")

    def show_kq_done(self):
        self.show_kq_loading(False)
        self.kq_widget = QLabel("🎉", self)
        self.kq_widget.setFont(QFont("Segoe UI Emoji", 16))
        self.kq_frame.insertWidget(0, self.kq_widget)
        self.label_kq.setText(" Đã xong hết rồi!")
    
    @Slot(str)
    def handle_worker_error(self, error_msg):
        self.log_box.append(error_msg)
        logging.error(f"WORKER ERROR: {error_msg}")
        
    @Slot()
    def start_harvesting(self):
        if self.is_vietnam_ip and not self.get_proxy_config():
            QMessageBox.critical(self, "Thao tác bị chặn", "Bạn đang sử dụng IP Việt Nam và chưa cấu hình proxy.\n\nVui lòng cấu hình proxy để truy cập trang web thuế.")
            return

        if not getattr(self, 'model_loaded', False):
            self.pending_start = True
            percent = getattr(self, '_last_model_percent', 0)
            self.model_status_label.setText(f"Đang tải model... Sẽ tự động bắt đầu khi model sẵn sàng. {percent}%")
            return
        self.label_kq.setText("")
        if self.kq_widget:
            self.kq_widget.deleteLater()
            self.kq_widget = None

        raw_text = self.text_box.toPlainText()
        danh_sach_raw = [line.strip() for line in raw_text.splitlines() if line.strip()]

        # Nếu không có MST nào, tạo 1 MST mặc định
        if not danh_sach_raw:
            danh_sach_raw = [str(random.randint(10**9, 10**10 - 1))]
            self.text_box.setPlainText("\n".join(danh_sach_raw))

        self.danh_sach = danh_sach_raw
        self.saved_count = self.done = self.duplicated_count = 0  # Reset cả số captcha trùng
        self.total = len(self.danh_sach)
        self.total_attempts = 0
        
        self.log(f"Bắt đầu thu thập captcha với {self.total} MST...")
        self.update_status()
        
        self.start_time = time.time()
        self.timer.start(1000)

        self.btn_start.setEnabled(False)
        self.show_kq_loading(True)

        self.task_queue = list(enumerate(self.danh_sach))
        QThreadPool.globalInstance().setMaxThreadCount(int(self.entry_workers.text()))
        self.process_queue()
        
    def process_queue(self):
        while self.task_queue:
            if QThreadPool.globalInstance().activeThreadCount() >= QThreadPool.globalInstance().maxThreadCount():
                break

            idx, mst = self.task_queue.pop(0)
            proxy_config = self.get_proxy_config()
            worker = CaptchaHarvestWorker(mst, self.save_folder, self.max_retries, proxy_config, self.ip_info_str, self)
            worker.signals.log.connect(self.log)
            worker.signals.captcha_saved.connect(self.on_captcha_saved)
            worker.signals.captcha_failed.connect(self.on_captcha_failed)
            worker.signals.attempt_made.connect(self.on_attempt_made)
            worker.signals.finished.connect(self.on_worker_finished)
            worker.signals.error.connect(self.handle_worker_error)
            worker.signals.captcha_duplicated.connect(self.on_captcha_duplicated)
            
            self.active_threads += 1
            self.update_status()
            QThreadPool.globalInstance().start(worker)

    @Slot(str)
    def on_captcha_saved(self, filename):
        self.saved_count += 1
        self.log(f"✅ Đã lưu: {filename}")
        self.done += 1
        self.update_status()

    @Slot()
    def on_captcha_failed(self):
        self.done += 1
        self.update_status()
        
    @Slot(bool)
    def on_attempt_made(self, is_ok):
        self.total_attempts += 1
        self.update_status()
        
    @Slot()
    def on_worker_finished(self):
        self.active_threads -= 1
        self.update_status()

        self.process_queue()

        if self.done == self.total:
            self.finish_processing()
            
    def finish_processing(self):
        self.timer.stop()
        self.log(f"🎯 Hoàn thành! Đã lưu được {self.saved_count} captcha.")
        
        self.show_kq_done()
        self.btn_start.setEnabled(True)
        
        QMessageBox.information(self, "Hoàn thành", 
                                f"🎉 Đã xử lý xong!\n\n- Captcha đã lưu: {self.saved_count}")

    @Slot(str)
    def on_captcha_duplicated(self, filename):
        self.log(f"⚠️ Captcha đã tồn tại: {filename}. Đã bỏ qua.")
        self.duplicated_count += 1  # Tăng số captcha trùng
        self.done += 1 # Tăng số captcha đã xử lý để cập nhật tiến độ
        self.update_status()

    @Slot()
    def open_captcha_folder(self):
        folder_path = os.path.abspath(self.save_folder)
        try:
            os.makedirs(folder_path, exist_ok=True)
            # Đa nền tảng: dùng Qt để mở thư mục
            from PySide6.QtCore import QUrl
            from PySide6.QtGui import QDesktopServices
            QDesktopServices.openUrl(QUrl.fromLocalFile(folder_path))
            self.log(f"📁 Đã mở thư mục: {folder_path}")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở thư mục:\n{e}")
            self.log(f"❌ Lỗi mở thư mục: {e}")

    @Slot()
    def generate_random_msts(self):
        try:
            count = int(self.entry_mst_count.text())
            if count <= 0:
                raise ValueError
        except ValueError:
            QMessageBox.warning(self, "Lỗi", "Số lượng MST không hợp lệ. Phải là số nguyên dương.")
            return

        mst_list = [str(random.randint(10**9, 10**10 - 1)) for _ in range(count)]
        self.text_box.setPlainText("\n".join(mst_list))
        self.log(f"✅ Đã tạo {count} MST ngẫu nhiên.")
        self.save_settings()

    @Slot()
    def select_save_folder(self):
        folder_path = QFileDialog.getExistingDirectory(self, "Chọn thư mục lưu Captcha", self.save_folder)
        if folder_path:
            self.save_folder = folder_path
            self.entry_save_folder.setText(folder_path)
            self.log(f"📂 Đã chọn thư mục lưu: {folder_path}")
            self.save_settings()

def main():
    app = QApplication(sys.argv)
    
    # Hook đã được đặt ở phạm vi toàn cục, không cần đặt lại ở đây.
    # sys.excepthook = global_exception_hook
    
    font = app.font()
    font.setPointSize(font.pointSize() + 2)
    app.setFont(font)

    QThreadPool.globalInstance().setMaxThreadCount(default_workers)
    
    window = MainWindow()
    window.resize(800, 600)
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 