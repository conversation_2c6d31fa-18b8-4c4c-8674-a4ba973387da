#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/hypot_ops.h>

namespace at {


// aten::hypot.out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & hypot_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::hypot_out::call(self, other, out);
}
// aten::hypot.out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & hypot_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out) {
    return at::_ops::hypot_out::call(self, other, out);
}

// aten::hypot(Tensor self, Tensor other) -> Tensor
inline at::Tensor hypot(const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::hypot::call(self, other);
}

}
