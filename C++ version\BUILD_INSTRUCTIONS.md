# Build Instructions for Kiem Tra MST Qt C++ Version

## Prerequisites

1. **Qt6** - Download and install Qt6 with the following components:
   - Qt6 Core
   - Qt6 Widgets  
   - Qt6 Network
   - Qt6 Tools (CMake, etc.)

2. **Visual Studio 2022** (Windows) or equivalent C++ compiler

3. **CMake** (version 3.16 or higher)

4. **Python** (for TrOCR functionality) with the following packages:
   ```bash
   pip install torch transformers pillow
   ```

## Build Steps

### Option 1: Using qmake (Recommended)
```bash
cd "C++ version"
build_qmake.bat
```

### Option 2: Manual qmake build
```bash
cd "C++ version"
qmake kiem_tra_mst_qt.pro
make release
```

### Option 3: Using CMake (Alternative)
```bash
cd "C++ version"
build.bat
```

### Option 4: Manual CMake build
```bash
cd "C++ version"
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### Option 5: Debug build (for troubleshooting)
```bash
cd "C++ version"
build_debug.bat
```
This builds in debug mode with additional bounds checking and logging.

### Option 6: Test build setup
```bash
cd "C++ version"
test_build.bat
```
This will test if qmake is working and generate Makefile without building.

## Required Files Structure

Make sure you have the following structure:
```
C++ version/
├── kiem_tra_mst_qt.cpp
├── kiem_tra_mst_qt.h
├── CMakeLists.txt
├── build.bat
├── resource/
│   └── working.gif
├── CaptchaData/
│   └── (TrOCR model files)
├── default.ico
└── setting.ini (optional)
```

## Features Implemented

✅ **Complete TrOCR Integration**
- Real TrOCR model loading via Python subprocess
- Image preprocessing (RGBA to RGB conversion)
- Fallback to random generation if TrOCR fails

✅ **Full Debug Dialog**
- Save response files (raw HTML/JSON)
- Force wrong captcha for testing
- Always fail mode for testing

✅ **Complete Retry Dialog**
- File browser for selecting result files
- Options to retry pending and failed items
- Full retry processing logic

✅ **Enhanced UI Components**
- Proper event handling for FlippingGIFLabel
- Better file/folder opening logic
- Cross-platform path handling

✅ **All Python Features Ported**
- 1:1 feature parity with Python version
- Same configuration system
- Same worker threading model
- Same result file format

## Running the Application

After successful build, the executable will be located at:
```
build/bin/Release/kiem_tra_mst_qt.exe
```

Make sure the following files are in the same directory as the executable:
- `CaptchaData/` folder with TrOCR model
- `resource/working.gif` (optional, for loading animation)
- `default.ico` (optional, for window icon)
- `setting.ini` (optional, will be created automatically)

## Troubleshooting

1. **Qt6 not found**: Make sure Qt6 is installed and qmake/CMAKE_PREFIX_PATH includes Qt6 installation directory

2. **MOC errors**: Use qmake instead of CMake. qmake handles MOC generation better:
   ```bash
   build_qmake.bat
   ```

3. **"No rule to make target 'moc'" error**: This is a CMake MOC issue. Use qmake build instead.

4. **"default argument given" error**: This has been fixed. Make sure you have the latest version of the files.

5. **QString ASSERT "pos <= d.size" error**: This indicates array bounds violation. Fixed with:
   - Added bounds checking in `onWorkerUpdate()`
   - Added bounds checking in `EmojiAnimationLabel::animate()`
   - Use debug build (`build_debug.bat`) to get more detailed error info

6. **TrOCR not working**: Ensure Python is installed with required packages and accessible from PATH

7. **Build errors**: Check that all required Qt6 components are installed

8. **Runtime errors**: Verify all resource files are copied to the executable directory

7. **qmake not found**: Make sure Qt6 bin directory is in your PATH environment variable
