#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/fbgemm_pack_quantized_matrix_ops.h>

namespace at {


// aten::fbgemm_pack_quantized_matrix(Tensor input) -> Tensor
inline at::Tensor fbgemm_pack_quantized_matrix(const at::Tensor & input) {
    return at::_ops::fbgemm_pack_quantized_matrix::call(input);
}

// aten::fbgemm_pack_quantized_matrix.KN(Tensor input, int K, int N) -> Tensor
inline at::Tensor fbgemm_pack_quantized_matrix(const at::Tensor & input, int64_t K, int64_t N) {
    return at::_ops::fbgemm_pack_quantized_matrix_KN::call(input, K, N);
}

}
