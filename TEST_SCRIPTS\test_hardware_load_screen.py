#!/usr/bin/env python3
"""
Test hardware detection load screen
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer, QRunnable, QThreadPool, QObject, Signal, Slot
from PySide6.QtGui import QFont

# Import các class cần thiết
from kiem_tra_mst_qt import HardwareDetectionDialog, EmojiAnimationLabel, WorkerSignals
from hardware_detector import hardware_detector

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Hardware Detection Load Screen")
        self.setFixedSize(400, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        self.test_button = QPushButton("Test Hardware Detection Dialog")
        self.test_button.clicked.connect(self.show_hardware_detection)
        layout.addWidget(self.test_button)
        
        self.result_label = QPushButton("Kết quả sẽ hiển thị ở đây")
        self.result_label.setEnabled(False)
        layout.addWidget(self.result_label)
    
    def show_hardware_detection(self):
        """Test hardware detection dialog"""
        # Tạo và hiển thị hardware detection dialog
        hardware_dialog = HardwareDetectionDialog(self)
        
        # Chạy hardware detection trong background
        class HardwareDetectionWorker(QRunnable):
            def __init__(self, dialog, main_window):
                super().__init__()
                self.dialog = dialog
                self.main_window = main_window
                self.signals = WorkerSignals()
                
            def run(self):
                try:
                    # Step 1: Detect CPU
                    self.dialog.update_progress(20, "Đang detect CPU...")
                    QTimer.singleShot(500, lambda: None)  # Simulate delay
                    cpu_info = hardware_detector.get_cpu_info()
                    
                    # Step 2: Detect NVIDIA GPU
                    self.dialog.update_progress(40, "Đang detect NVIDIA GPU...")
                    QTimer.singleShot(500, lambda: None)  # Simulate delay
                    nvidia_gpus = hardware_detector.get_nvidia_gpu_info()
                    
                    # Step 3: Detect Intel GPU
                    self.dialog.update_progress(60, "Đang detect Intel GPU...")
                    QTimer.singleShot(500, lambda: None)  # Simulate delay
                    intel_gpus = hardware_detector.get_intel_gpu_info()
                    
                    # Step 4: Check PyTorch
                    self.dialog.update_progress(80, "Đang kiểm tra PyTorch...")
                    QTimer.singleShot(500, lambda: None)  # Simulate delay
                    pytorch_info = hardware_detector.get_pytorch_device_info()
                    
                    # Step 5: Determine best device
                    self.dialog.update_progress(90, "Đang chọn phần cứng tốt nhất...")
                    QTimer.singleShot(500, lambda: None)  # Simulate delay
                    device_type, device_name, device_info = hardware_detector.determine_best_device()
                    device_display_name = hardware_detector.get_device_display_name()
                    
                    # Step 6: Complete
                    self.dialog.update_progress(100, "Hoàn thành!", device_display_name)
                    
                    # Emit signal để cập nhật UI
                    self.signals.finished.emit()
                    
                except Exception as e:
                    self.signals.error.emit(str(e))
        
        # Tạo worker và chạy
        worker = HardwareDetectionWorker(hardware_dialog, self)
        worker.signals.finished.connect(lambda: self.on_hardware_detection_complete(hardware_dialog))
        worker.signals.error.connect(lambda err: self.on_hardware_detection_error(hardware_dialog, err))
        
        QThreadPool.globalInstance().start(worker)
        result = hardware_dialog.exec()
        
        if result:
            print("Hardware detection completed successfully")
        else:
            print("Hardware detection was cancelled or failed")
    
    def on_hardware_detection_complete(self, dialog):
        """Xử lý khi hardware detection hoàn thành"""
        device_display_name = hardware_detector.get_device_display_name()
        self.result_label.setText(f"✅ Phần cứng: {device_display_name}")
        print(f"Hardware detection completed: {device_display_name}")
    
    def on_hardware_detection_error(self, dialog, error_msg):
        """Xử lý khi hardware detection lỗi"""
        self.result_label.setText(f"❌ Lỗi: {error_msg}")
        print(f"Hardware detection error: {error_msg}")
        dialog.accept()

def main():
    app = QApplication(sys.argv)
    
    # Tăng cỡ chữ chung cho toàn bộ ứng dụng
    font = app.font()
    font.setPointSize(font.pointSize() + 2)
    app.setFont(font)
    
    window = TestMainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
