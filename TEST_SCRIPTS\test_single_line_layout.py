#!/usr/bin/env python3
"""
Test single line layout với tất cả thông tin trên 1 dòng
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

from hardware_detector import hardware_detector

class SingleLineLayoutWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('Test Single Line Layout v5.0.12')
        self.setFixedSize(800, 300)
        
        # Setup UI
        self.setup_ui()
        
        # Cập nhật thông tin
        QTimer.singleShot(100, self.update_info)
        
        # Timer để cập nhật thời gian
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        self.elapsed_seconds = 0
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("Demo Single Line Status Layout")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("", 16, QFont.Bold))
        main_layout.addWidget(title)
        
        # Status frame - Single line layout
        status_frame = QFrame()
        status_frame.setFrameShape(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)
        
        # Kết quả (giả lập)
        self.result_label = QLabel("✅ Thành công: 15/20")
        self.result_label.setStyleSheet("color: green; font-weight: bold;")
        
        # Thời gian
        self.time_label = QLabel("⏱ Thời gian: 00:00:00")
        self.time_label.setStyleSheet("font-size: 11px;")
        
        # Model status
        self.model_status_label = QLabel("✅ Model sẵn sàng")
        self.model_status_label.setStyleSheet("color: green; font-size: 10px;")
        
        # Hardware status
        self.hardware_status_label = QLabel("🔍 Đang detect phần cứng...")
        self.hardware_status_label.setStyleSheet("color: blue; font-weight: bold; font-size: 10px;")
        
        # Separators
        sep1 = QLabel(" | ")
        sep1.setStyleSheet("color: gray; font-size: 10px;")
        sep2 = QLabel(" | ")
        sep2.setStyleSheet("color: gray; font-size: 10px;")
        
        # Layout: [Kết quả] [Stretch] ⏱ Thời gian | ✅ Model | 🎮 Hardware
        status_layout.addWidget(self.result_label)
        status_layout.addStretch()
        status_layout.addWidget(self.time_label)
        status_layout.addWidget(sep1)
        status_layout.addWidget(self.model_status_label)
        status_layout.addWidget(sep2)
        status_layout.addWidget(self.hardware_status_label)
        
        main_layout.addWidget(status_frame)
        
        # Demo buttons
        button_layout = QHBoxLayout()
        
        self.btn_simulate_loading = QPushButton("Simulate Loading")
        self.btn_simulate_loading.clicked.connect(self.simulate_loading)
        button_layout.addWidget(self.btn_simulate_loading)
        
        self.btn_simulate_error = QPushButton("Simulate Error")
        self.btn_simulate_error.clicked.connect(self.simulate_error)
        button_layout.addWidget(self.btn_simulate_error)
        
        self.btn_reset = QPushButton("Reset")
        self.btn_reset.clicked.connect(self.reset_status)
        button_layout.addWidget(self.btn_reset)
        
        main_layout.addLayout(button_layout)
        
        # Info area
        self.info_label = QLabel("Layout mới: Tất cả thông tin trên 1 dòng để tiết kiệm không gian")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("border: 1px solid gray; padding: 10px; margin: 10px; background-color: #f0f0f0;")
        main_layout.addWidget(self.info_label)
    
    def update_info(self):
        """Cập nhật thông tin phần cứng"""
        try:
            device_display_name = hardware_detector.get_device_display_name()
            self.hardware_status_label.setText(device_display_name)
            self.hardware_status_label.setStyleSheet("color: green; font-weight: bold; font-size: 10px;")
            
            # Cập nhật info
            device_type, device_name, device_info = hardware_detector.determine_best_device()
            info_text = f"Layout: [Kết quả] ⏱ Thời gian | ✅ Model | {device_display_name}\n"
            info_text += f"Font sizes: Result (normal), Time (11px), Model & Hardware (10px)\n"
            info_text += f"Current hardware: {device_name} ({device_type})"
            
            self.info_label.setText(info_text)
            
        except Exception as e:
            self.hardware_status_label.setText("❌ Lỗi phần cứng")
            self.hardware_status_label.setStyleSheet("color: red; font-weight: bold; font-size: 10px;")
            self.info_label.setText(f"Lỗi: {e}")
    
    def update_time(self):
        """Cập nhật thời gian"""
        self.elapsed_seconds += 1
        hours = self.elapsed_seconds // 3600
        minutes = (self.elapsed_seconds % 3600) // 60
        seconds = self.elapsed_seconds % 60
        self.time_label.setText(f"⏱ Thời gian: {hours:02d}:{minutes:02d}:{seconds:02d}")
    
    def simulate_loading(self):
        """Mô phỏng trạng thái loading"""
        self.model_status_label.setText("Tải model... 45%")
        self.model_status_label.setStyleSheet("color: orange; font-size: 10px;")
        
        self.hardware_status_label.setText("🔍 Đang detect...")
        self.hardware_status_label.setStyleSheet("color: blue; font-weight: bold; font-size: 10px;")
        
        # Reset sau 3 giây
        QTimer.singleShot(3000, self.update_info)
    
    def simulate_error(self):
        """Mô phỏng trạng thái lỗi"""
        self.model_status_label.setText("❌ Lỗi model")
        self.model_status_label.setStyleSheet("color: red; font-size: 10px;")
        
        self.hardware_status_label.setText("❌ Lỗi phần cứng")
        self.hardware_status_label.setStyleSheet("color: red; font-weight: bold; font-size: 10px;")
        
        # Reset sau 3 giây
        QTimer.singleShot(3000, self.reset_status)
    
    def reset_status(self):
        """Reset về trạng thái bình thường"""
        self.model_status_label.setText("✅ Model sẵn sàng")
        self.model_status_label.setStyleSheet("color: green; font-size: 10px;")
        self.update_info()

def main():
    app = QApplication(sys.argv)
    
    # Tăng cỡ chữ chung
    font = app.font()
    font.setPointSize(font.pointSize() + 1)
    app.setFont(font)
    
    window = SingleLineLayoutWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
