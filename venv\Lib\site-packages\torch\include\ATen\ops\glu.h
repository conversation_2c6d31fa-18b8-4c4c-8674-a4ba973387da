#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/glu_ops.h>

namespace at {


// aten::glu.out(Tensor self, int dim=-1, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & glu_out(at::Tensor & out, const at::Tensor & self, int64_t dim=-1) {
    return at::_ops::glu_out::call(self, dim, out);
}
// aten::glu.out(Tensor self, int dim=-1, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & glu_outf(const at::Tensor & self, int64_t dim, at::Tensor & out) {
    return at::_ops::glu_out::call(self, dim, out);
}

// aten::glu(Tensor self, int dim=-1) -> Tensor
inline at::Tensor glu(const at::Tensor & self, int64_t dim=-1) {
    return at::_ops::glu::call(self, dim);
}

}
