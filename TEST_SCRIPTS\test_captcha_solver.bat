@echo off
echo ========================================
echo TESTING CAPTCHA SOLVER C# VERSION
echo ========================================

echo.
echo 🎯 CAPTCHA SOLVING OPTIONS AVAILABLE:
echo.

echo ✅ 1. LOCAL OCR (TrOCR):
echo    🔧 Method: Microsoft TrOCR model via ONNX
echo    💰 Cost: Free
echo    ⚡ Speed: Fast (GPU accelerated)
echo    🎯 Accuracy: 70-85%% (depends on captcha type)
echo    📦 Dependencies: Microsoft.ML.OnnxRuntime

echo.
echo ✅ 2. TWOCAPTCHA API:
echo    🔧 Method: Human solvers via API
echo    💰 Cost: ~$1-3 per 1000 captchas
echo    ⚡ Speed: 10-30 seconds
echo    🎯 Accuracy: 95-99%%
echo    📦 Dependencies: HTTP client + API key

echo.
echo ✅ 3. ANTICAPTCHA API:
echo    🔧 Method: Human + AI hybrid
echo    💰 Cost: ~$1-2 per 1000 captchas
echo    ⚡ Speed: 5-20 seconds
echo    🎯 Accuracy: 95-99%%
echo    📦 Dependencies: HTTP client + API key

echo.
echo ========================================
echo TESTING CAPTCHA SOLVER
echo ========================================

echo.
echo 🔍 Checking build...
cd "..\C Sharp version"

dotnet build --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)
echo ✅ Build successful

echo.
echo 🧪 Creating test captcha images...

if not exist "test_captchas" mkdir test_captchas

echo Creating sample captcha images...
echo ^<html^>^<body style="background:white;margin:50px;"^>
echo ^<div style="background:#f0f0f0;padding:20px;border:2px solid #ccc;font-family:Arial;font-size:24px;letter-spacing:3px;color:#333;"^>
echo ABC123
echo ^</div^>^</body^>^</html^> > test_captchas\sample1.html

echo ^<html^>^<body style="background:white;margin:50px;"^>
echo ^<div style="background:#e0e0e0;padding:15px;border:1px solid #999;font-family:Courier;font-size:20px;letter-spacing:2px;color:#666;"^>
echo XYZ789
echo ^</div^>^</body^>^</html^> > test_captchas\sample2.html

echo ✅ Test captcha files created

echo.
echo ========================================
echo CAPTCHA SOLVER TEST MENU
echo ========================================

:menu
echo.
echo 🎮 Choose test option:
echo.
echo 1. 🤖 Test Local OCR (TrOCR simulation)
echo 2. 🌐 Test TwoCaptcha API (requires API key)
echo 3. 🔧 Test AntiCaptcha API (requires API key)
echo 4. 📊 Performance Comparison
echo 5. 🔧 Configuration
echo 6. ❌ Exit
echo.

set /p choice="Enter choice (1-6): "

if "%choice%"=="1" (
    echo.
    echo 🤖 TESTING LOCAL OCR...
    echo.
    echo 📝 Creating test program...
    
    echo using System;
    echo using System.Threading.Tasks;
    echo using KiemTraMST;
    echo.
    echo class TestLocalOcr
    echo {
    echo     static async Task Main^(^)
    echo     {
    echo         Console.WriteLine^("🤖 Testing Local OCR Captcha Solver..."^);
    echo         Console.WriteLine^(^);
    echo.
    echo         var solver = new CaptchaSolver^(serviceType: "local"^);
    echo.
    echo         // Test with sample data
    echo         var testData = new byte[1000]; // Dummy image data
    echo         new Random^(^).NextBytes^(testData^);
    echo.
    echo         Console.WriteLine^("📸 Processing test captcha..."^);
    echo         var result = await solver.SolveCaptchaAsync^(testData^);
    echo.
    echo         Console.WriteLine^($"✅ Result: {result.Text}"^);
    echo         Console.WriteLine^($"🎯 Success: {result.Success}"^);
    echo         Console.WriteLine^($"⏱ Time: {result.SolveTime.TotalMilliseconds}ms"^);
    echo         Console.WriteLine^($"📊 Confidence: {result.Confidence:P}"^);
    echo.
    echo         if ^(!string.IsNullOrEmpty^(result.Error^)^)
    echo             Console.WriteLine^($"❌ Error: {result.Error}"^);
    echo.
    echo         Console.WriteLine^("Press any key to continue..."^);
    echo         Console.ReadKey^(^);
    echo     }
    echo } > TestLocalOcr.cs
    
    echo.
    echo 🚀 Running local OCR test...
    dotnet run TestLocalOcr.cs
    
    del TestLocalOcr.cs
    goto menu
)

if "%choice%"=="2" (
    echo.
    echo 🌐 TESTING TWOCAPTCHA API...
    echo.
    set /p apikey="Enter TwoCaptcha API key (or press Enter to skip): "
    
    if "%apikey%"=="" (
        echo ⚠️ No API key provided, skipping test
        goto menu
    )
    
    echo.
    echo 📝 Creating TwoCaptcha test...
    
    echo using System;
    echo using System.Threading.Tasks;
    echo using KiemTraMST;
    echo.
    echo class TestTwoCaptcha
    echo {
    echo     static async Task Main^(^)
    echo     {
    echo         Console.WriteLine^("🌐 Testing TwoCaptcha API..."^);
    echo         Console.WriteLine^(^);
    echo.
    echo         var solver = new CaptchaSolver^("%apikey%", "twocaptcha"^);
    echo.
    echo         // Check balance first
    echo         Console.WriteLine^("💰 Checking account balance..."^);
    echo         var balance = await solver.GetBalanceAsync^(^);
    echo         Console.WriteLine^($"Balance: ${balance}"^);
    echo.
    echo         if ^(balance ^< 0.01^)
    echo         {
    echo             Console.WriteLine^("❌ Insufficient balance for testing"^);
    echo             return;
    echo         }
    echo.
    echo         // Test with sample data
    echo         var testData = new byte[5000]; // Larger dummy image
    echo         new Random^(^).NextBytes^(testData^);
    echo.
    echo         Console.WriteLine^("📸 Submitting test captcha to TwoCaptcha..."^);
    echo         var result = await solver.SolveCaptchaAsync^(testData^);
    echo.
    echo         Console.WriteLine^($"✅ Result: {result.Text}"^);
    echo         Console.WriteLine^($"🎯 Success: {result.Success}"^);
    echo         Console.WriteLine^($"⏱ Time: {result.SolveTime.TotalSeconds}s"^);
    echo         Console.WriteLine^($"📊 Confidence: {result.Confidence:P}"^);
    echo.
    echo         if ^(!string.IsNullOrEmpty^(result.Error^)^)
    echo             Console.WriteLine^($"❌ Error: {result.Error}"^);
    echo.
    echo         Console.WriteLine^("Press any key to continue..."^);
    echo         Console.ReadKey^(^);
    echo     }
    echo } > TestTwoCaptcha.cs
    
    echo.
    echo 🚀 Running TwoCaptcha test...
    dotnet run TestTwoCaptcha.cs
    
    del TestTwoCaptcha.cs
    goto menu
)

if "%choice%"=="3" (
    echo.
    echo 🔧 TESTING ANTICAPTCHA API...
    echo.
    set /p apikey="Enter AntiCaptcha API key (or press Enter to skip): "
    
    if "%apikey%"=="" (
        echo ⚠️ No API key provided, skipping test
        goto menu
    )
    
    echo ⚠️ AntiCaptcha test implementation similar to TwoCaptcha
    echo See CaptchaSolver.cs for full implementation
    goto menu
)

if "%choice%"=="4" (
    echo.
    echo 📊 CAPTCHA SOLVER PERFORMANCE COMPARISON:
    echo.
    echo 🤖 LOCAL OCR ^(TrOCR^):
    echo    ⚡ Speed: 0.5-2 seconds
    echo    💰 Cost: Free
    echo    🎯 Accuracy: 70-85%%
    echo    📦 Offline: Yes
    echo    🔧 Setup: Complex ^(ONNX model required^)
    echo.
    echo 🌐 TWOCAPTCHA:
    echo    ⚡ Speed: 10-30 seconds
    echo    💰 Cost: $1-3 per 1000
    echo    🎯 Accuracy: 95-99%%
    echo    📦 Offline: No
    echo    🔧 Setup: Simple ^(API key only^)
    echo.
    echo 🔧 ANTICAPTCHA:
    echo    ⚡ Speed: 5-20 seconds
    echo    💰 Cost: $1-2 per 1000
    echo    🎯 Accuracy: 95-99%%
    echo    📦 Offline: No
    echo    🔧 Setup: Simple ^(API key only^)
    echo.
    echo 🏆 RECOMMENDATION:
    echo    💡 Development/Testing: Local OCR
    echo    💡 Production/High Volume: TwoCaptcha/AntiCaptcha
    echo    💡 Best Accuracy: Human-based APIs
    echo    💡 Best Cost: Local OCR
    echo.
    pause
    goto menu
)

if "%choice%"=="5" (
    echo.
    echo 🔧 CAPTCHA SOLVER CONFIGURATION:
    echo.
    echo 📝 Configuration options in setting.ini:
    echo.
    echo [captcha]
    echo service=local          # local, twocaptcha, anticaptcha
    echo api_key=               # API key for paid services
    echo timeout=30             # Timeout in seconds
    echo max_retries=3          # Max retry attempts
    echo confidence_threshold=0.7  # Minimum confidence for local OCR
    echo.
    echo 🔧 Local OCR configuration:
    echo model_path=Models/trocr_model.onnx
    echo device=cuda            # cuda, cpu, auto
    echo batch_size=1           # Batch processing size
    echo.
    echo 📁 Files to configure:
    echo    setting.ini - Main configuration
    echo    CaptchaSolver.cs - Implementation
    echo    Models/ - OCR model files
    echo.
    pause
    goto menu
)

if "%choice%"=="6" (
    echo.
    echo 👋 Captcha solver testing completed!
    echo.
    echo 🎊 SUMMARY:
    echo    ✅ Local OCR: Fast and free
    echo    ✅ TwoCaptcha: High accuracy, paid
    echo    ✅ AntiCaptcha: Fast and accurate, paid
    echo    ✅ All methods integrated in C# version
    echo.
    pause
    exit /b 0
)

echo Invalid choice. Please try again.
goto menu
