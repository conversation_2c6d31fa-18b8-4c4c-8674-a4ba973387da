{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "D:/PROGRAMS_D/Qt/6.9.1/msvc2022_64/include", "D:/PROGRAMS_D/Qt/6.9.1/msvc2022_64/include/QtCore", "D:/PROGRAMS_D/Qt/6.9.1/msvc2022_64/include/QtWidgets", "D:/PROGRAMS_D/Qt/6.9.1/msvc2022_64/include/QtGui", "D:/PROGRAMS_D/Qt/6.9.1/msvc2022_64/include/QtNetwork", "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/*/include", "C:/Program Files (x86)/Windows Kits/10/Include/*/ucrt", "C:/Program Files (x86)/Windows Kits/10/Include/*/um", "C:/Program Files (x86)/Windows Kits/10/Include/*/shared"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "QT_CORE_LIB", "QT_GUI_LIB", "QT_WIDGETS_LIB", "QT_NETWORK_LIB"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.37.32822/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64"}], "version": 4}