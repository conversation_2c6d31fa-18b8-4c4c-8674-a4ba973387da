#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/_linalg_svd_meta.h>

namespace at {
namespace native {
struct TORCH_API structured__linalg_svd_out : public at::meta::structured__linalg_svd {
void impl(const at::Tensor & A, bool full_matrices, bool compute_uv, ::std::optional<c10::string_view> driver, const at::Tensor & U, const at::Tensor & S, const at::Tensor & Vh);
};
} // namespace native
} // namespace at
