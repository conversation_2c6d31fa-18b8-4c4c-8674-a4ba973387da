# Kiểm Tra MST - C# Version 5.0.12

Ứng dụng kiểm tra MST được chuyển đổi từ Python sang C# với WPF interface, sử dụng .NET 9.

## Tính năng chính

### ✅ Hardware Detection & Auto-Selection
- Tự động detect CPU, NVIDIA GPU, Intel GPU
- Chọn phần cứng tốt nhất theo thứ tự ưu tiên:
  1. NVIDIA GPU (CUDA) - Ưu tiên cao nhất
  2. Intel XPU (Discrete GPU) - Ưu tiên thứ 2  
  3. Apple MPS - Cho MacBook
  4. CPU + Intel Integrated GPU - Fallback
  5. CPU Only - Cuối cùng

### 📱 Single Line Status Layout
- Layout: `✅ Model | Đang dừng | ⏱ 00:05:23 | 🎮 GTX 1080 Ti`
- Canh giữa và font chữ đồng nhất
- Placeholder "Đang dừng" khi không hoạt động
- Click hardware để xem thông tin chi tiết

### 🔍 MST Checking Features
- <PERSON><PERSON><PERSON> tra MST qua multiple APIs
- Multi-threading với configurable workers
- Retry mechanism với exponential backoff
- Export kết quả ra CSV
- Real-time progress tracking

## Yêu cầu hệ thống

- .NET 9.0 hoặc cao hơn
- Windows 10/11 (x64)
- Visual Studio 2022 17.8+ hoặc JetBrains Rider 2024.1+ (để build)

## Cài đặt

### Cách 1: Build từ source
```bash
cd "C Sharp version"
dotnet restore
dotnet build --configuration Release
dotnet run
```

### Cách 2: Publish standalone
```bash
dotnet publish --configuration Release --self-contained true --runtime win-x64
```

## Cấu hình

Chỉnh sửa file `setting.ini`:

```ini
[DEFAULT]
max_workers=20      # Số worker threads
timeout=30          # Timeout cho mỗi request (giây)
retry_count=3       # Số lần retry khi lỗi

[API]
primary_url=https://masothue.com
secondary_url=https://api.vietqr.io/v2/business

[UI]
window_width=900
window_height=700
font_size=12
```

## Sử dụng

1. **Nhập MST**: Nhập danh sách MST vào textbox (mỗi MST một dòng)
2. **Tải file**: Click "📁 Tải từ file" để load từ file .txt
3. **Bắt đầu**: Click "▶️ Bắt đầu" để kiểm tra
4. **Theo dõi**: Xem progress và log real-time
5. **Xuất kết quả**: Click "💾 Xuất Excel" để save CSV
6. **Hardware info**: Click vào hardware status để xem chi tiết

## Cấu trúc project

```
C Sharp version/
├── KiemTraMST.csproj          # Project file
├── App.xaml                   # Application definition
├── App.xaml.cs               # Application code-behind
├── MainWindow.xaml           # Main UI
├── MainWindow.xaml.cs        # Main UI code-behind
├── HardwareDetector.cs       # Hardware detection logic
├── ConfigManager.cs          # Configuration management
├── MstChecker.cs            # MST checking logic
├── setting.ini              # Configuration file
└── README.md               # This file
```

## Dependencies

- **Microsoft.ML.OnnxRuntime 1.17.1**: ONNX runtime cho AI models
- **Microsoft.ML.OnnxRuntime.Gpu 1.17.1**: GPU support cho ONNX
- **System.Drawing.Common 8.0.2**: Image processing
- **Newtonsoft.Json 13.0.3**: JSON parsing
- **RestSharp 111.2.0**: HTTP client
- **System.Management 8.0.0**: WMI queries cho hardware detection

## Tính năng nâng cao

### Hardware Detection
- Automatic GPU detection (NVIDIA, Intel, AMD)
- CPU information với core count
- Memory information cho GPUs
- Fallback mechanisms

### Performance
- Multi-threaded MST checking
- Configurable worker count
- Connection pooling
- Retry với exponential backoff
- Cancellation support

### UI/UX
- Real-time progress updates
- Comprehensive logging
- Export functionality
- Responsive design
- Error handling

## Troubleshooting

### Lỗi thường gặp

1. **"Hardware detection failed"**
   - Kiểm tra WMI service đang chạy
   - Chạy với quyền Administrator

2. **"API timeout"**
   - Tăng timeout trong setting.ini
   - Kiểm tra kết nối internet

3. **"Too many requests"**
   - Giảm max_workers trong setting.ini
   - Tăng delay giữa các requests

## Version History

- **5.0.12**: C# conversion với full feature parity
- Hardware detection tích hợp
- Single line status layout
- Multi-API support
- Enhanced error handling

## License

Copyright © 2024. All rights reserved.
