@echo off
echo Running C++ application with Qt6 PATH
echo ======================================

REM Add Qt6 and MinGW to PATH
set PATH=D:\PROGRAMS_D\Qt\6.9.1\mingw_64\bin;D:\PROGRAMS_D\Qt\Tools\mingw1120_64\bin;%PATH%

REM Run the application from C++ version folder
echo Starting application...
echo Executable path: "%~dp0build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\bin\kiem_tra_mst_qt.exe"
echo.

"%~dp0build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\bin\kiem_tra_mst_qt.exe"
set EXIT_CODE=%ERRORLEVEL%

echo.
echo Application finished with exit code: %EXIT_CODE%
pause
