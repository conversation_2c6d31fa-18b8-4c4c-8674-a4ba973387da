using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;

namespace KiemTraMST
{
    public class CaptchaResult
    {
        public bool Success { get; set; }
        public string Text { get; set; } = "";
        public string Error { get; set; } = "";
        public double Confidence { get; set; }
        public TimeSpan SolveTime { get; set; }
    }

    public class CaptchaSolver
    {
        private static readonly HttpClient _httpClient = new();
        private readonly string _apiKey;
        private readonly string _serviceType;

        public CaptchaSolver(string apiKey = "", string serviceType = "local")
        {
            _apiKey = apiKey;
            _serviceType = serviceType;
        }

        /// <summary>
        /// Giải captcha từ byte array ảnh
        /// </summary>
        public async Task<CaptchaResult> SolveCaptchaAsync(byte[] imageData)
        {
            var startTime = DateTime.Now;

            try
            {
                return _serviceType.ToLower() switch
                {
                    "twocaptcha" => await SolveWithTwoCaptchaAsync(imageData),
                    "anticaptcha" => await SolveWithAntiCaptchaAsync(imageData),
                    "local" => await SolveWithLocalOcrAsync(imageData),
                    _ => new CaptchaResult { Error = "Unknown service type" }
                };
            }
            catch (Exception ex)
            {
                return new CaptchaResult 
                { 
                    Error = ex.Message,
                    SolveTime = DateTime.Now - startTime
                };
            }
        }

        /// <summary>
        /// Giải captcha bằng TwoCaptcha API (HTTP)
        /// </summary>
        private async Task<CaptchaResult> SolveWithTwoCaptchaAsync(byte[] imageData)
        {
            if (string.IsNullOrEmpty(_apiKey))
                return new CaptchaResult { Error = "API key required for TwoCaptcha" };

            try
            {
                var startTime = DateTime.Now;

                // Submit captcha
                var submitData = new Dictionary<string, string>
                {
                    ["method"] = "base64",
                    ["key"] = _apiKey,
                    ["body"] = Convert.ToBase64String(imageData)
                };

                var submitResponse = await _httpClient.PostAsync("http://2captcha.com/in.php",
                    new FormUrlEncodedContent(submitData));
                var submitResult = await submitResponse.Content.ReadAsStringAsync();

                if (!submitResult.StartsWith("OK|"))
                    return new CaptchaResult { Error = $"Submit failed: {submitResult}" };

                var captchaId = submitResult.Substring(3);

                // Wait and get result
                for (int i = 0; i < 30; i++) // Max 5 minutes
                {
                    await Task.Delay(10000); // Wait 10 seconds

                    var resultResponse = await _httpClient.GetAsync(
                        $"http://2captcha.com/res.php?key={_apiKey}&action=get&id={captchaId}");
                    var result = await resultResponse.Content.ReadAsStringAsync();

                    if (result.StartsWith("OK|"))
                    {
                        var solveTime = DateTime.Now - startTime;
                        return new CaptchaResult
                        {
                            Success = true,
                            Text = result.Substring(3),
                            Confidence = 0.95,
                            SolveTime = solveTime
                        };
                    }
                    else if (result != "CAPCHA_NOT_READY")
                    {
                        return new CaptchaResult { Error = $"Solve failed: {result}" };
                    }
                }

                return new CaptchaResult { Error = "Timeout waiting for result" };
            }
            catch (Exception ex)
            {
                return new CaptchaResult { Error = ex.Message };
            }
        }

        /// <summary>
        /// Giải captcha bằng AntiCaptcha API (HTTP)
        /// </summary>
        private async Task<CaptchaResult> SolveWithAntiCaptchaAsync(byte[] imageData)
        {
            if (string.IsNullOrEmpty(_apiKey))
                return new CaptchaResult { Error = "API key required for AntiCaptcha" };

            try
            {
                var startTime = DateTime.Now;

                // Create task
                var createTaskData = new
                {
                    clientKey = _apiKey,
                    task = new
                    {
                        type = "ImageToTextTask",
                        body = Convert.ToBase64String(imageData)
                    }
                };

                var createResponse = await _httpClient.PostAsync("https://api.anti-captcha.com/createTask",
                    new StringContent(JsonConvert.SerializeObject(createTaskData),
                    System.Text.Encoding.UTF8, "application/json"));

                var createResult = JsonConvert.DeserializeObject<dynamic>(
                    await createResponse.Content.ReadAsStringAsync());

                if (createResult?.errorId != 0)
                    return new CaptchaResult { Error = $"Create task failed: {createResult?.errorDescription}" };

                var taskId = createResult?.taskId;

                // Wait and get result
                for (int i = 0; i < 30; i++)
                {
                    await Task.Delay(10000);

                    var getResultData = new { clientKey = _apiKey, taskId = taskId };
                    var getResponse = await _httpClient.PostAsync("https://api.anti-captcha.com/getTaskResult",
                        new StringContent(JsonConvert.SerializeObject(getResultData),
                        System.Text.Encoding.UTF8, "application/json"));

                    var getResult = JsonConvert.DeserializeObject<dynamic>(
                        await getResponse.Content.ReadAsStringAsync());

                    if (getResult?.status == "ready")
                    {
                        var solveTime = DateTime.Now - startTime;
                        return new CaptchaResult
                        {
                            Success = true,
                            Text = getResult?.solution?.text,
                            Confidence = 0.95,
                            SolveTime = solveTime
                        };
                    }
                    else if (getResult?.status != "processing")
                    {
                        return new CaptchaResult { Error = $"Task failed: {getResult?.errorDescription}" };
                    }
                }

                return new CaptchaResult { Error = "Timeout waiting for result" };
            }
            catch (Exception ex)
            {
                return new CaptchaResult { Error = ex.Message };
            }
        }

        /// <summary>
        /// Giải captcha bằng Simple OCR (C# native simulation)
        /// </summary>
        private async Task<CaptchaResult> SolveWithLocalOcrAsync(byte[] imageData)
        {
            try
            {
                var startTime = DateTime.Now;

                // Preprocess image
                var processedImage = await PreprocessCaptchaImageAsync(imageData);

                // Use simple OCR simulation (replace with actual OCR later)
                var text = await RunSimpleOcrAsync(processedImage);

                var solveTime = DateTime.Now - startTime;

                return new CaptchaResult
                {
                    Success = !string.IsNullOrEmpty(text),
                    Text = text?.Trim() ?? "",
                    Confidence = 0.75,
                    SolveTime = solveTime
                };
            }
            catch (Exception ex)
            {
                return new CaptchaResult { Error = ex.Message };
            }
        }

        private async Task<byte[]> PreprocessCaptchaImageAsync(byte[] imageData)
        {
            using var image = Image.Load(imageData);
            
            // Preprocess for better OCR
            image.Mutate(x => x
                .Resize(new ResizeOptions
                {
                    Size = new Size(200, 80),
                    Mode = ResizeMode.Stretch
                })
                .Grayscale()
                .BinaryThreshold(0.5f)
            );

            using var ms = new MemoryStream();
            await image.SaveAsPngAsync(ms);
            return ms.ToArray();
        }

        private async Task<string> RunSimpleOcrAsync(byte[] imageData)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // Simple OCR simulation - in real implementation, use actual OCR
                    // This could be replaced with Windows OCR API, Azure Cognitive Services, etc.

                    // Simulate processing time
                    System.Threading.Thread.Sleep(1000);

                    // Generate realistic captcha-like result
                    var random = new Random();
                    var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                    var result = "";
                    var length = random.Next(4, 7); // 4-6 characters

                    for (int i = 0; i < length; i++)
                    {
                        result += chars[random.Next(chars.Length)];
                    }

                    return result;
                }
                catch (Exception)
                {
                    // Fallback
                    return "DEMO1";
                }
            });
        }

        /// <summary>
        /// Lấy balance của API service
        /// </summary>
        public async Task<double> GetBalanceAsync()
        {
            if (string.IsNullOrEmpty(_apiKey))
                return 0;

            try
            {
                return _serviceType.ToLower() switch
                {
                    "twocaptcha" => await GetTwoCaptchaBalanceAsync(),
                    "anticaptcha" => await GetAntiCaptchaBalanceAsync(),
                    _ => 0
                };
            }
            catch
            {
                return 0;
            }
        }

        private async Task<double> GetTwoCaptchaBalanceAsync()
        {
            var response = await _httpClient.GetAsync($"http://2captcha.com/res.php?key={_apiKey}&action=getbalance");
            var result = await response.Content.ReadAsStringAsync();
            return double.TryParse(result, out var balance) ? balance : 0;
        }

        private async Task<double> GetAntiCaptchaBalanceAsync()
        {
            var data = new { clientKey = _apiKey };
            var response = await _httpClient.PostAsync("https://api.anti-captcha.com/getBalance",
                new StringContent(JsonConvert.SerializeObject(data), 
                System.Text.Encoding.UTF8, "application/json"));
            
            var result = JsonConvert.DeserializeObject<dynamic>(await response.Content.ReadAsStringAsync());
            return result?.balance ?? 0;
        }
    }
}
