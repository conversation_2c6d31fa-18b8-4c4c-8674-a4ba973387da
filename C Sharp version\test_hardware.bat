@echo off
echo ========================================
echo Testing Hardware Detection Only
echo ========================================

echo.
echo 1. Building simple test...
dotnet build --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    dotnet build --configuration Release
    pause
    exit /b 1
)

echo.
echo 2. Running hardware detection test...
echo This will test hardware detection without full UI...

dotnet run --configuration Release --project SimpleTest.cs 2>error.log
if %errorlevel% neq 0 (
    echo ERROR: Hardware test failed
    echo Check error.log for details:
    type error.log
    pause
    exit /b 1
)

echo.
echo ✅ Hardware detection test completed
pause
