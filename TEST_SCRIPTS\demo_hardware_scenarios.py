#!/usr/bin/env python3
"""
Demo các kịch bản phần cứng khác nhau
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hardware_detector import hardware_detector

def demo_scenarios():
    print("=== DEMO CÁC KỊCH BẢN PHẦN CỨNG ===\n")
    
    scenarios = [
        {
            'name': '🎮 Gaming PC với NVIDIA RTX 4090',
            'pytorch': {
                'torch_available': True,
                'cuda_available': True,
                'cuda_device_count': 1,
                'cuda_devices': [{
                    'index': 0,
                    'name': 'NVIDIA GeForce RTX 4090',
                    'memory': '24564 MB',
                    'compute_capability': '8.9',
                    'multiprocessor_count': 128
                }],
                'current_device': 'cuda',
                'xpu_available': False,
                'other_backends': []
            },
            'nvidia': [{
                'index': '0',
                'name': 'NVIDIA GeForce RTX 4090',
                'memory': '24564 MB',
                'driver': '537.13',
                'compute_cap': '8.9',
                'vendor': 'NVIDIA'
            }],
            'intel': []
        },
        {
            'name': '💻 Laptop Intel với UHD Graphics',
            'pytorch': {
                'torch_available': True,
                'cuda_available': False,
                'cuda_device_count': 0,
                'cuda_devices': [],
                'current_device': 'cpu',
                'xpu_available': False,
                'other_backends': []
            },
            'nvidia': [],
            'intel': [{
                'index': '0',
                'name': 'Intel(R) UHD Graphics 620',
                'memory': '1024',
                'vendor': 'Intel',
                'type': 'integrated'
            }]
        },
        {
            'name': '🔷 Workstation Intel với Arc A770',
            'pytorch': {
                'torch_available': True,
                'cuda_available': False,
                'cuda_device_count': 0,
                'cuda_devices': [],
                'current_device': 'xpu',
                'xpu_available': True,
                'xpu_device_count': 1,
                'other_backends': ['XPU (Intel Arc A770)']
            },
            'nvidia': [],
            'intel': [{
                'index': '0',
                'name': 'Intel Arc A770',
                'memory': '16384',
                'vendor': 'Intel',
                'type': 'discrete'
            }]
        },
        {
            'name': '🍎 MacBook Pro với Apple M2',
            'pytorch': {
                'torch_available': True,
                'cuda_available': False,
                'cuda_device_count': 0,
                'cuda_devices': [],
                'current_device': 'mps',
                'xpu_available': False,
                'other_backends': ['MPS (Apple)']
            },
            'nvidia': [],
            'intel': []
        },
        {
            'name': '🖥️ Server chỉ có CPU',
            'pytorch': {
                'torch_available': True,
                'cuda_available': False,
                'cuda_device_count': 0,
                'cuda_devices': [],
                'current_device': 'cpu',
                'xpu_available': False,
                'other_backends': []
            },
            'nvidia': [],
            'intel': []
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. {scenario['name']}")
        
        # Clear cache và inject mock data
        hardware_detector.clear_cache()
        hardware_detector._detection_cache['pytorch'] = scenario['pytorch']
        hardware_detector._detection_cache['nvidia'] = scenario['nvidia']
        hardware_detector._detection_cache['intel'] = scenario['intel']
        
        # Test best device
        device_type, device_name, device_info = hardware_detector.determine_best_device()
        display_name = hardware_detector.get_device_display_name()
        
        print(f"   Device Type: {device_type}")
        print(f"   Device Name: {device_name}")
        print(f"   Display: {display_name}")
        
        # Thông tin thêm
        if device_type == 'cuda':
            print(f"   VRAM: {device_info.get('memory', 'Unknown')}")
            print(f"   Compute: {device_info.get('compute_capability', 'Unknown')}")
        elif device_type == 'xpu':
            print(f"   Device Count: {device_info.get('device_count', 1)}")
        elif device_type == 'cpu' and 'intel_gpu' in device_info:
            print(f"   Intel GPU: {device_info['intel_gpu']}")
        
        print()
    
    # Clear cache cuối cùng
    hardware_detector.clear_cache()
    print("✅ Demo completed, cache cleared")

def show_current_system():
    print("=== HỆ THỐNG HIỆN TẠI ===\n")
    
    # Lấy thông tin thật
    detailed_info = hardware_detector.get_detailed_info()
    
    print("🖥️ CPU Information:")
    cpu_info = detailed_info['cpu']
    print(f"   Name: {cpu_info['name']}")
    print(f"   Architecture: {cpu_info['architecture']}")
    print(f"   Cores: {cpu_info['cores']}")
    
    print(f"\n🎮 NVIDIA GPUs: {len(detailed_info['nvidia_gpus'])}")
    for gpu in detailed_info['nvidia_gpus']:
        print(f"   - {gpu['name']} ({gpu.get('memory', 'Unknown memory')})")
    
    print(f"\n🔷 Intel GPUs: {len(detailed_info['intel_gpus'])}")
    for gpu in detailed_info['intel_gpus']:
        print(f"   - {gpu['name']} ({gpu.get('memory', 'Unknown')} MB)")
    
    pytorch_info = detailed_info['pytorch']
    print(f"\n🔥 PyTorch:")
    print(f"   Available: {pytorch_info['torch_available']}")
    print(f"   CUDA: {pytorch_info['cuda_available']}")
    print(f"   XPU: {pytorch_info.get('xpu_available', False)}")
    print(f"   Other backends: {pytorch_info.get('other_backends', [])}")
    
    device_type, device_name, device_info = detailed_info['best_device']
    display_name = hardware_detector.get_device_display_name()
    
    print(f"\n🏆 Best Device for OCR:")
    print(f"   Type: {device_type}")
    print(f"   Name: {device_name}")
    print(f"   Display: {display_name}")

if __name__ == "__main__":
    show_current_system()
    print("\n" + "="*50 + "\n")
    demo_scenarios()
